import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const EmployeeDataContext = createContext();

export const useEmployeeData = () => {
  const context = useContext(EmployeeDataContext);
  if (!context) {
    throw new Error('useEmployeeData must be used within an EmployeeDataProvider');
  }
  return context;
};

export const EmployeeDataProvider = ({ children }) => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [lastSync, setLastSync] = useState(null);

  useEffect(() => {
    loadCachedData();
  }, []);

  const loadCachedData = async () => {
    try {
      setLoading(true);
      const cachedOrders = await AsyncStorage.getItem('employee_orders');
      const cachedSync = await AsyncStorage.getItem('last_sync');
      
      if (cachedOrders) {
        setOrders(JSON.parse(cachedOrders));
      }
      
      if (cachedSync) {
        setLastSync(new Date(cachedSync));
      }
    } catch (error) {
      console.error('Error loading cached data:', error);
    } finally {
      setLoading(false);
    }
  };

  const cacheData = async (ordersData) => {
    try {
      await AsyncStorage.setItem('employee_orders', JSON.stringify(ordersData));
      await AsyncStorage.setItem('last_sync', new Date().toISOString());
      setLastSync(new Date());
    } catch (error) {
      console.error('Error caching data:', error);
    }
  };

  const updateOrderStatus = async (orderId, newStatus, notes = '') => {
    try {
      // Update local state
      const updatedOrders = orders.map(order =>
        order.id === orderId ? { ...order, status: newStatus, lastUpdated: new Date().toISOString() } : order
      );
      
      setOrders(updatedOrders);
      await cacheData(updatedOrders);
      
      // In real app, this would sync with DataSyncService
      console.log(`Order ${orderId} updated to ${newStatus}`);
      
      return { success: true };
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  };

  const refreshOrders = async (employeeId) => {
    try {
      setLoading(true);
      
      // Mock data for demo - in real app, this would fetch from DataSyncService
      const mockOrders = [
        {
          id: 'ORD001',
          customer_name: 'Ahmed Rahman',
          garment_type: 'suit',
          status: 'cutting',
          priority: 'urgent',
          delivery_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
          assigned_to: employeeId,
        },
        {
          id: 'ORD002',
          customer_name: 'Fatima Khan',
          garment_type: 'dress',
          status: 'stitching',
          priority: 'normal',
          delivery_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
          assigned_to: employeeId,
        },
        {
          id: 'ORD003',
          customer_name: 'Mohammad Ali',
          garment_type: 'shirt',
          status: 'fitting',
          priority: 'express',
          delivery_date: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(),
          assigned_to: employeeId,
        },
      ];
      
      setOrders(mockOrders);
      await cacheData(mockOrders);
      
      return mockOrders;
    } catch (error) {
      console.error('Error refreshing orders:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const getOrderById = (orderId) => {
    return orders.find(order => order.id === orderId);
  };

  const getOrdersByStatus = (status) => {
    return orders.filter(order => order.status === status);
  };

  const getUrgentOrders = () => {
    return orders.filter(order => order.priority === 'urgent' || order.priority === 'express');
  };

  const getOrdersStats = () => {
    return {
      total: orders.length,
      inProgress: orders.filter(o => o.status !== 'ready' && o.status !== 'delivered').length,
      urgent: orders.filter(o => o.priority === 'urgent' || o.priority === 'express').length,
      completed: orders.filter(o => o.status === 'ready' || o.status === 'delivered').length,
    };
  };

  const clearCache = async () => {
    try {
      await AsyncStorage.removeItem('employee_orders');
      await AsyncStorage.removeItem('last_sync');
      setOrders([]);
      setLastSync(null);
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  };

  const value = {
    // Data
    orders,
    loading,
    lastSync,
    
    // Actions
    updateOrderStatus,
    refreshOrders,
    getOrderById,
    getOrdersByStatus,
    getUrgentOrders,
    getOrdersStats,
    clearCache,
    
    // Utils
    cacheData,
    loadCachedData,
  };

  return (
    <EmployeeDataContext.Provider value={value}>
      {children}
    </EmployeeDataContext.Provider>
  );
};
