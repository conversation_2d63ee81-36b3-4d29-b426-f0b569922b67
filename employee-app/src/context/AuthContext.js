import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DataSyncService from '../../../shared/services/DataSyncService';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [employee, setEmployee] = useState(null);
  const [loading, setLoading] = useState(true);

  // Demo employees data (in real app, this would come from the shared database)
  const demoEmployees = [
    {
      id: 'EMP001',
      name: '<PERSON>',
      role: 'Senior Tailor',
      pin: '1234',
      skills: ['Shirt Making', 'Suit Making', 'Alterations'],
      phone: '+880 1700-000001',
      isActive: true,
    },
    {
      id: 'EMP002',
      name: '<PERSON><PERSON>',
      role: '<PERSON>',
      pin: '1234',
      skills: ['Dress Making', 'Blouse Making', 'Alterations'],
      phone: '+880 1700-000002',
      isActive: true,
    },
    {
      id: 'EMP003',
      name: 'Mohammad Ali',
      role: 'Cutter',
      pin: '1234',
      skills: ['Pattern Making', 'Cutting', 'Fabric Selection'],
      phone: '+880 1700-000003',
      isActive: true,
    },
    {
      id: 'EMP004',
      name: 'Rashida Begum',
      role: 'Embroiderer',
      pin: '1234',
      skills: ['Embroidery', 'Hand Stitching', 'Decorative Work'],
      phone: '+880 1700-000004',
      isActive: true,
    },
    {
      id: 'EMP005',
      name: 'Karim Sheikh',
      role: 'Presser',
      pin: '1234',
      skills: ['Pressing', 'Finishing', 'Quality Control'],
      phone: '+880 1700-000005',
      isActive: true,
    },
  ];

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const savedEmployee = await AsyncStorage.getItem('employee');
      if (savedEmployee) {
        const employeeData = JSON.parse(savedEmployee);
        setEmployee(employeeData);
        setIsAuthenticated(true);
        
        // Initialize DataSyncService for employee app
        await DataSyncService.initialize('employee');
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
    } finally {
      setLoading(false);
    }
  };

  const login = async (employeeId, pin) => {
    try {
      // Find employee in demo data
      const foundEmployee = demoEmployees.find(
        emp => emp.id === employeeId && emp.pin === pin && emp.isActive
      );

      if (!foundEmployee) {
        return false;
      }

      // In real app, you would verify credentials with the shared database
      const employeeData = {
        id: foundEmployee.id,
        name: foundEmployee.name,
        role: foundEmployee.role,
        skills: foundEmployee.skills,
        phone: foundEmployee.phone,
        loginTime: new Date().toISOString(),
      };

      // Save to AsyncStorage
      await AsyncStorage.setItem('employee', JSON.stringify(employeeData));
      
      // Initialize DataSyncService
      await DataSyncService.initialize('employee');
      
      setEmployee(employeeData);
      setIsAuthenticated(true);
      
      return true;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  };

  const logout = async () => {
    try {
      await AsyncStorage.removeItem('employee');
      DataSyncService.destroy();
      setEmployee(null);
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const updateEmployee = async (updates) => {
    try {
      const updatedEmployee = { ...employee, ...updates };
      await AsyncStorage.setItem('employee', JSON.stringify(updatedEmployee));
      setEmployee(updatedEmployee);
    } catch (error) {
      console.error('Update employee error:', error);
    }
  };

  const value = {
    isAuthenticated,
    employee,
    loading,
    login,
    logout,
    updateEmployee,
    demoEmployees, // For demo purposes
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
