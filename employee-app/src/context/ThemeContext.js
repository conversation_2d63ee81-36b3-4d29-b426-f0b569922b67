import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

const lightTheme = {
  colors: {
    primary: '#2196F3',
    primaryContainer: '#E3F2FD',
    onPrimary: '#FFFFFF',
    onPrimaryContainer: '#1976D2',
    secondary: '#03DAC6',
    secondaryContainer: '#E0F2F1',
    onSecondary: '#FFFFFF',
    onSecondaryContainer: '#00695C',
    background: '#FAFAFA',
    surface: '#FFFFFF',
    onBackground: '#212121',
    onSurface: '#212121',
    text: '#212121',
    textSecondary: '#757575',
    border: '#E0E0E0',
    error: '#F44336',
    warning: '#FF9800',
    success: '#4CAF50',
    info: '#2196F3',
  },
};

const darkTheme = {
  colors: {
    primary: '#64B5F6',
    primaryContainer: '#1976D2',
    onPrimary: '#000000',
    onPrimaryContainer: '#E3F2FD',
    secondary: '#4DB6AC',
    secondaryContainer: '#00695C',
    onSecondary: '#000000',
    onSecondaryContainer: '#E0F2F1',
    background: '#121212',
    surface: '#1E1E1E',
    onBackground: '#FFFFFF',
    onSurface: '#FFFFFF',
    text: '#FFFFFF',
    textSecondary: '#BDBDBD',
    border: '#424242',
    error: '#EF5350',
    warning: '#FFB74D',
    success: '#66BB6A',
    info: '#64B5F6',
  },
};

export const ThemeProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadThemePreference();
  }, []);

  const loadThemePreference = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem('theme');
      if (savedTheme) {
        setIsDarkMode(savedTheme === 'dark');
      }
    } catch (error) {
      console.error('Error loading theme preference:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleTheme = async () => {
    try {
      const newTheme = !isDarkMode;
      setIsDarkMode(newTheme);
      await AsyncStorage.setItem('theme', newTheme ? 'dark' : 'light');
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  };

  const theme = isDarkMode ? darkTheme : lightTheme;

  const value = {
    theme,
    isDarkMode,
    toggleTheme,
    loading,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
