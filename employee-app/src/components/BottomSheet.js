import React, { useEffect, useRef } from 'react';
import {
  View,
  Modal,
  Animated,
  PanResponder,
  Dimensions,
  TouchableWithoutFeedback,
  StyleSheet,
} from 'react-native';
import { Surface, Text, IconButton, useTheme } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

const BottomSheet = ({
  visible,
  onClose,
  title,
  children,
  snapPoints = ['50%', '90%'],
  initialSnap = 0,
  enablePanGesture = true,
  showHandle = true,
  showCloseButton = true,
}) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const translateY = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const lastGesture = useRef(SCREEN_HEIGHT);

  const snapPointsPixels = snapPoints.map(point => {
    if (typeof point === 'string' && point.includes('%')) {
      return SCREEN_HEIGHT * (1 - parseInt(point) / 100);
    }
    return SCREEN_HEIGHT - point;
  });

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        return enablePanGesture && Math.abs(gestureState.dy) > 10;
      },
      onPanResponderGrant: () => {
        translateY.setOffset(lastGesture.current);
        translateY.setValue(0);
      },
      onPanResponderMove: (evt, gestureState) => {
        if (gestureState.dy > 0) {
          translateY.setValue(gestureState.dy);
        }
      },
      onPanResponderRelease: (evt, gestureState) => {
        translateY.flattenOffset();
        const currentPosition = lastGesture.current + gestureState.dy;
        
        if (gestureState.dy > 100 || gestureState.vy > 0.5) {
          // Close if dragged down significantly
          closeBottomSheet();
        } else {
          // Snap to nearest snap point
          const nearestSnapPoint = snapPointsPixels.reduce((prev, curr) => {
            return Math.abs(curr - currentPosition) < Math.abs(prev - currentPosition) ? curr : prev;
          });
          
          animateToPosition(nearestSnapPoint);
        }
      },
    })
  ).current;

  const animateToPosition = (position) => {
    lastGesture.current = position;
    Animated.spring(translateY, {
      toValue: position,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  };

  const openBottomSheet = () => {
    animateToPosition(snapPointsPixels[initialSnap]);
  };

  const closeBottomSheet = () => {
    Animated.spring(translateY, {
      toValue: SCREEN_HEIGHT,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start(() => {
      onClose();
    });
  };

  useEffect(() => {
    if (visible) {
      openBottomSheet();
    } else {
      translateY.setValue(SCREEN_HEIGHT);
      lastGesture.current = SCREEN_HEIGHT;
    }
  }, [visible]);

  if (!visible) return null;

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={closeBottomSheet}
    >
      <View style={styles.overlay}>
        <TouchableWithoutFeedback onPress={closeBottomSheet}>
          <View style={styles.backdrop} />
        </TouchableWithoutFeedback>
        
        <Animated.View
          style={[
            styles.bottomSheet,
            {
              backgroundColor: theme.colors.surface,
              transform: [{ translateY }],
              paddingBottom: insets.bottom,
            },
          ]}
          {...(enablePanGesture ? panResponder.panHandlers : {})}
        >
          <Surface
            style={[
              styles.content,
              { backgroundColor: theme.colors.surface }
            ]}
            elevation={0}
          >
            {/* Handle */}
            {showHandle && (
              <View style={styles.handleContainer}>
                <View
                  style={[
                    styles.handle,
                    { backgroundColor: theme.colors.onSurfaceVariant }
                  ]}
                />
              </View>
            )}

            {/* Header */}
            {(title || showCloseButton) && (
              <View style={styles.header}>
                {title && (
                  <Text variant="headlineSmall" style={styles.title}>
                    {title}
                  </Text>
                )}
                {showCloseButton && (
                  <IconButton
                    icon="close"
                    size={24}
                    onPress={closeBottomSheet}
                    style={styles.closeButton}
                  />
                )}
              </View>
            )}

            {/* Content */}
            <View style={styles.bodyContent}>
              {children}
            </View>
          </Surface>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  bottomSheet: {
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    minHeight: '50%',
    maxHeight: '90%',
  },
  content: {
    flex: 1,
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
  },
  handleContainer: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  handle: {
    width: 32,
    height: 4,
    borderRadius: 2,
    opacity: 0.4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingBottom: 16,
  },
  title: {
    flex: 1,
    fontWeight: '600',
  },
  closeButton: {
    margin: 0,
  },
  bodyContent: {
    flex: 1,
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
});

export default BottomSheet;
