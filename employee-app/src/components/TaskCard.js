import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { 
  Card, 
  Text, 
  Chip, 
  Button, 
  useTheme,
  ProgressBar 
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';

const TaskCard = ({
  order,
  onPress,
  onStatusUpdate,
  showProgress = true,
  compact = false,
}) => {
  const theme = useTheme();

  const getStatusColor = (status) => {
    const colors = {
      'measurement_pending': theme.colors.tertiary,
      'measurement_taken': theme.colors.primary,
      'cutting': '#9C27B0',
      'stitching': '#FF5722',
      'fitting': '#607D8B',
      'finishing': '#795548',
      'ready': theme.colors.primary,
      'delivered': '#4CAF50',
    };
    return colors[status] || theme.colors.outline;
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'normal': theme.colors.primary,
      'urgent': '#FF9800',
      'express': theme.colors.error,
    };
    return colors[priority] || theme.colors.outline;
  };

  const getStatusProgress = (status) => {
    const statusOrder = [
      'measurement_pending',
      'measurement_taken', 
      'cutting',
      'stitching',
      'fitting',
      'finishing',
      'ready',
      'delivered'
    ];
    const currentIndex = statusOrder.indexOf(status);
    return currentIndex >= 0 ? (currentIndex + 1) / statusOrder.length : 0;
  };

  const getNextStatus = (currentStatus) => {
    const statusFlow = {
      'measurement_pending': 'measurement_taken',
      'measurement_taken': 'cutting',
      'cutting': 'stitching',
      'stitching': 'fitting',
      'fitting': 'finishing',
      'finishing': 'ready',
      'ready': 'delivered'
    };
    return statusFlow[currentStatus];
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const today = new Date();
    const diffTime = date - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays < 0) return `${Math.abs(diffDays)} days overdue`;
    return `${diffDays} days left`;
  };

  const isUrgent = order.priority === 'urgent' || order.priority === 'express';
  const isOverdue = new Date(order.dueDate) < new Date();
  const nextStatus = getNextStatus(order.status);

  return (
    <Card
      style={[
        styles.card,
        compact && styles.compactCard,
        isOverdue && { borderLeftWidth: 4, borderLeftColor: theme.colors.error }
      ]}
      onPress={onPress}
    >
      <Card.Content style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.orderInfo}>
            <Text variant="titleMedium" style={styles.orderId}>
              #{order.id}
            </Text>
            <Text variant="bodyMedium" style={styles.customerName}>
              {order.customerName}
            </Text>
          </View>
          
          <View style={styles.badges}>
            {isUrgent && (
              <Chip
                mode="flat"
                style={[
                  styles.priorityChip,
                  { backgroundColor: getPriorityColor(order.priority) + '20' }
                ]}
                textStyle={{ 
                  color: getPriorityColor(order.priority),
                  fontSize: 10,
                  fontWeight: '600'
                }}
              >
                {order.priority.toUpperCase()}
              </Chip>
            )}
          </View>
        </View>

        {/* Garment Info */}
        <View style={styles.garmentInfo}>
          <Icon 
            name="checkroom" 
            size={16} 
            color={theme.colors.onSurfaceVariant} 
          />
          <Text 
            variant="bodyMedium" 
            style={[styles.garmentType, { color: theme.colors.onSurfaceVariant }]}
          >
            {order.garmentType}
          </Text>
        </View>

        {/* Due Date */}
        <View style={styles.dueDateContainer}>
          <Icon 
            name="schedule" 
            size={16} 
            color={isOverdue ? theme.colors.error : theme.colors.onSurfaceVariant} 
          />
          <Text 
            variant="bodySmall" 
            style={[
              styles.dueDate,
              { color: isOverdue ? theme.colors.error : theme.colors.onSurfaceVariant }
            ]}
          >
            {formatDate(order.dueDate)}
          </Text>
        </View>

        {/* Progress Bar */}
        {showProgress && !compact && (
          <View style={styles.progressContainer}>
            <ProgressBar
              progress={getStatusProgress(order.status)}
              color={getStatusColor(order.status)}
              style={styles.progressBar}
            />
            <Text 
              variant="labelSmall" 
              style={[styles.progressText, { color: theme.colors.onSurfaceVariant }]}
            >
              {Math.round(getStatusProgress(order.status) * 100)}% Complete
            </Text>
          </View>
        )}

        {/* Status and Actions */}
        <View style={styles.footer}>
          <Chip
            mode="flat"
            style={[
              styles.statusChip,
              { backgroundColor: getStatusColor(order.status) + '20' }
            ]}
            textStyle={{ 
              color: getStatusColor(order.status),
              fontSize: 12,
              fontWeight: '600'
            }}
          >
            {order.status.replace('_', ' ').toUpperCase()}
          </Chip>

          {nextStatus && onStatusUpdate && (
            <Button
              mode="contained"
              compact
              style={[
                styles.actionButton,
                { backgroundColor: getStatusColor(nextStatus) }
              ]}
              labelStyle={styles.actionButtonText}
              onPress={() => onStatusUpdate(order.id, nextStatus)}
            >
              Next: {nextStatus.replace('_', ' ')}
            </Button>
          )}
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 16,
    marginVertical: 6,
    borderRadius: 12,
  },
  compactCard: {
    marginVertical: 4,
  },
  content: {
    paddingVertical: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontWeight: '700',
    marginBottom: 2,
  },
  customerName: {
    opacity: 0.8,
  },
  badges: {
    alignItems: 'flex-end',
  },
  priorityChip: {
    height: 24,
  },
  garmentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  garmentType: {
    marginLeft: 8,
    textTransform: 'capitalize',
  },
  dueDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  dueDate: {
    marginLeft: 8,
    fontWeight: '500',
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    marginBottom: 4,
  },
  progressText: {
    textAlign: 'right',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusChip: {
    height: 28,
  },
  actionButton: {
    borderRadius: 20,
  },
  actionButtonText: {
    fontSize: 11,
    fontWeight: '600',
  },
});

export default TaskCard;
