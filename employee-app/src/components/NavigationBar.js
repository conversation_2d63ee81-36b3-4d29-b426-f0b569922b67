import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Surface, Text, useTheme, Badge } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';

const NavigationBar = ({ 
  currentTab, 
  onTabPress, 
  tabs = [],
  showBadge = false,
  badgeCount = 0 
}) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  const defaultTabs = [
    {
      id: 'tasks',
      label: 'My Tasks',
      icon: 'assignment',
      badge: badgeCount > 0,
      badgeCount: badgeCount,
    },
    {
      id: 'scan',
      label: 'Scan',
      icon: 'qr-code-scanner',
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: 'person',
    },
  ];

  const navigationTabs = tabs.length > 0 ? tabs : defaultTabs;

  return (
    <Surface
      style={[
        styles.container,
        {
          backgroundColor: theme.colors.surface,
          paddingBottom: insets.bottom,
          borderTopColor: theme.colors.outline,
        },
      ]}
      elevation={3}
    >
      <View style={styles.tabContainer}>
        {navigationTabs.map((tab) => {
          const isActive = currentTab === tab.id;
          
          return (
            <TouchableOpacity
              key={tab.id}
              style={[
                styles.tab,
                isActive && {
                  backgroundColor: theme.colors.secondaryContainer,
                },
              ]}
              onPress={() => onTabPress(tab.id)}
              activeOpacity={0.7}
            >
              <View style={styles.iconContainer}>
                <Icon
                  name={tab.icon}
                  size={24}
                  color={
                    isActive
                      ? theme.colors.onSecondaryContainer
                      : theme.colors.onSurfaceVariant
                  }
                />
                {tab.badge && tab.badgeCount > 0 && (
                  <Badge
                    style={[
                      styles.badge,
                      { backgroundColor: theme.colors.error }
                    ]}
                    size={16}
                  >
                    {tab.badgeCount > 99 ? '99+' : tab.badgeCount}
                  </Badge>
                )}
              </View>
              
              <Text
                variant="labelMedium"
                style={[
                  styles.label,
                  {
                    color: isActive
                      ? theme.colors.onSecondaryContainer
                      : theme.colors.onSurfaceVariant,
                  },
                ]}
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </Surface>
  );
};

const styles = StyleSheet.create({
  container: {
    borderTopWidth: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingTop: 8,
    paddingHorizontal: 8,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 16,
    marginHorizontal: 4,
  },
  iconContainer: {
    position: 'relative',
    marginBottom: 4,
  },
  badge: {
    position: 'absolute',
    top: -8,
    right: -8,
    minWidth: 16,
    height: 16,
  },
  label: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default NavigationBar;
