import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import DataSyncService from '../../../shared/services/DataSyncService';

const MyTasksScreen = ({ navigation }) => {
  const { employee } = useAuth();
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadMyOrders();
    
    // Listen for real-time updates
    const handleOrderUpdate = (update) => {
      if (update.table === 'orders') {
        loadMyOrders();
      }
    };

    DataSyncService.addListener('orders', handleOrderUpdate);

    return () => {
      DataSyncService.removeListener('orders', handleOrderUpdate);
    };
  }, []);

  const loadMyOrders = async () => {
    try {
      if (employee?.id) {
        const myOrders = await DataSyncService.getMyOrders(employee.id);
        setOrders(myOrders);
      }
    } catch (error) {
      console.error('Failed to load orders:', error);
      Alert.alert('Error', 'Failed to load your tasks');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadMyOrders();
  };

  const getStatusColor = (status) => {
    const colors = {
      'measurement_pending': '#FF9800',
      'measurement_taken': '#2196F3',
      'cutting': '#9C27B0',
      'stitching': '#FF5722',
      'fitting': '#607D8B',
      'finishing': '#795548',
      'ready': '#4CAF50',
      'delivered': '#8BC34A',
    };
    return colors[status] || '#757575';
  };

  const getStatusIcon = (status) => {
    const icons = {
      'measurement_pending': 'ruler-outline',
      'measurement_taken': 'checkmark-circle-outline',
      'cutting': 'cut-outline',
      'stitching': 'build-outline',
      'fitting': 'person-outline',
      'finishing': 'brush-outline',
      'ready': 'checkmark-done-outline',
      'delivered': 'car-outline',
    };
    return icons[status] || 'ellipse-outline';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'normal': '#4CAF50',
      'urgent': '#FF9800',
      'express': '#F44336',
    };
    return colors[priority] || '#757575';
  };

  const canUpdateStatus = (currentStatus) => {
    // Define which statuses can be updated by employees
    const updatableStatuses = [
      'measurement_taken',
      'cutting',
      'stitching',
      'fitting',
      'finishing'
    ];
    return updatableStatuses.includes(currentStatus);
  };

  const getNextStatus = (currentStatus) => {
    const statusFlow = {
      'measurement_taken': 'cutting',
      'cutting': 'stitching',
      'stitching': 'fitting',
      'fitting': 'finishing',
      'finishing': 'ready'
    };
    return statusFlow[currentStatus];
  };

  const handleQuickUpdate = async (order) => {
    const nextStatus = getNextStatus(order.status);
    if (!nextStatus) return;

    try {
      await DataSyncService.updateOrderStatus(
        order.id,
        nextStatus,
        employee.id,
        `Updated to ${nextStatus} by ${employee.name}`
      );
      
      Alert.alert('Success', `Order updated to ${nextStatus}`);
      loadMyOrders();
    } catch (error) {
      Alert.alert('Error', 'Failed to update order status');
    }
  };

  const renderOrderItem = ({ item: order }) => (
    <TouchableOpacity
      style={styles.orderCard}
      onPress={() => navigation.navigate('OrderDetails', { order })}
    >
      <View style={styles.orderHeader}>
        <View style={styles.orderInfo}>
          <Text style={styles.orderId}>#{order.id}</Text>
          <Text style={styles.customerName}>{order.customer_name}</Text>
        </View>
        <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(order.priority) }]}>
          <Text style={styles.priorityText}>{order.priority?.toUpperCase()}</Text>
        </View>
      </View>

      <View style={styles.orderDetails}>
        <View style={styles.garmentInfo}>
          <Ionicons name="shirt-outline" size={16} color="#666" />
          <Text style={styles.garmentType}>{order.garment_type}</Text>
        </View>
        
        <View style={styles.deliveryInfo}>
          <Ionicons name="calendar-outline" size={16} color="#666" />
          <Text style={styles.deliveryDate}>
            Due: {new Date(order.delivery_date).toLocaleDateString()}
          </Text>
        </View>
      </View>

      <View style={styles.statusSection}>
        <View style={styles.statusInfo}>
          <Ionicons 
            name={getStatusIcon(order.status)} 
            size={20} 
            color={getStatusColor(order.status)} 
          />
          <Text style={[styles.statusText, { color: getStatusColor(order.status) }]}>
            {order.status.replace('_', ' ').toUpperCase()}
          </Text>
        </View>

        {canUpdateStatus(order.status) && (
          <TouchableOpacity
            style={[styles.quickUpdateButton, { backgroundColor: getStatusColor(order.status) }]}
            onPress={() => handleQuickUpdate(order)}
          >
            <Text style={styles.quickUpdateText}>
              Mark as {getNextStatus(order.status)?.replace('_', ' ')}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="clipboard-outline" size={64} color="#ccc" />
      <Text style={styles.emptyStateText}>No tasks assigned</Text>
      <Text style={styles.emptyStateSubtext}>
        Check back later for new orders
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>My Tasks</Text>
        <Text style={styles.headerSubtitle}>
          Welcome back, {employee?.name}
        </Text>
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{orders.length}</Text>
          <Text style={styles.statLabel}>Total Tasks</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>
            {orders.filter(o => o.status !== 'ready' && o.status !== 'delivered').length}
          </Text>
          <Text style={styles.statLabel}>In Progress</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>
            {orders.filter(o => o.priority === 'urgent' || o.priority === 'express').length}
          </Text>
          <Text style={styles.statLabel}>Urgent</Text>
        </View>
      </View>

      <FlatList
        data={orders}
        renderItem={renderOrderItem}
        keyExtractor={(item) => item.id}
        style={styles.ordersList}
        contentContainerStyle={orders.length === 0 ? styles.emptyContainer : null}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    backgroundColor: '#2196F3',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  ordersList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  orderCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  customerName: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: 'white',
  },
  orderDetails: {
    marginBottom: 12,
  },
  garmentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  garmentType: {
    fontSize: 14,
    color: '#333',
    marginLeft: 8,
    textTransform: 'capitalize',
  },
  deliveryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deliveryDate: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  statusSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  quickUpdateButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  quickUpdateText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyStateText: {
    fontSize: 18,
    color: '#666',
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
  },
});

export default MyTasksScreen;
