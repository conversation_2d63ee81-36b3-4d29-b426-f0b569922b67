import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

const UpdateStatusScreen = ({ navigation, route }) => {
  const { order } = route.params;
  const [selectedStatus, setSelectedStatus] = useState(order.status);
  const [notes, setNotes] = useState('');
  const [updating, setUpdating] = useState(false);

  const statusOptions = [
    { id: 'measurement_taken', name: 'Measurement Taken', icon: 'checkmark-circle', color: '#2196F3' },
    { id: 'cutting', name: 'Cutting', icon: 'cut', color: '#9C27B0' },
    { id: 'stitching', name: 'Stitching', icon: 'build', color: '#FF5722' },
    { id: 'fitting', name: 'Fitting', icon: 'person', color: '#607D8B' },
    { id: 'finishing', name: 'Finishing', icon: 'brush', color: '#795548' },
    { id: 'ready', name: 'Ready for Delivery', icon: 'checkmark-done', color: '#4CAF50' },
  ];

  const handleUpdate = async () => {
    if (selectedStatus === order.status && !notes.trim()) {
      Alert.alert('No Changes', 'Please select a new status or add notes');
      return;
    }

    setUpdating(true);
    try {
      // Mock update - replace with real DataSyncService call
      console.log(`Updating order ${order.id} to ${selectedStatus} with notes: ${notes}`);
      
      Alert.alert('Success', 'Order status updated successfully', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to update order status');
    } finally {
      setUpdating(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Update Status</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content}>
        {/* Order Info */}
        <View style={styles.orderInfo}>
          <Text style={styles.orderId}>#{order.id}</Text>
          <Text style={styles.customerName}>{order.customer_name}</Text>
          <Text style={styles.garmentType}>{order.garment_type}</Text>
        </View>

        {/* Current Status */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Current Status</Text>
          <View style={styles.currentStatus}>
            <Text style={styles.currentStatusText}>
              {order.status.replace('_', ' ').toUpperCase()}
            </Text>
          </View>
        </View>

        {/* Status Options */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Update to</Text>
          {statusOptions.map((status) => (
            <TouchableOpacity
              key={status.id}
              style={[
                styles.statusOption,
                selectedStatus === status.id && styles.statusOptionSelected,
                { borderColor: selectedStatus === status.id ? status.color : '#E0E0E0' }
              ]}
              onPress={() => setSelectedStatus(status.id)}
            >
              <View style={[styles.statusIcon, { backgroundColor: status.color + '20' }]}>
                <Ionicons name={status.icon} size={24} color={status.color} />
              </View>
              <Text style={[
                styles.statusName,
                { color: selectedStatus === status.id ? status.color : '#333' }
              ]}>
                {status.name}
              </Text>
              {selectedStatus === status.id && (
                <Ionicons name="checkmark-circle" size={24} color={status.color} />
              )}
            </TouchableOpacity>
          ))}
        </View>

        {/* Notes */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Progress Notes (Optional)</Text>
          <TextInput
            style={styles.notesInput}
            placeholder="Add notes about your progress, any issues, or special observations..."
            value={notes}
            onChangeText={setNotes}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>

        {/* Update Button */}
        <TouchableOpacity
          style={[
            styles.updateButton,
            updating && styles.updateButtonDisabled,
            { backgroundColor: statusOptions.find(s => s.id === selectedStatus)?.color || '#2196F3' }
          ]}
          onPress={handleUpdate}
          disabled={updating}
        >
          <Text style={styles.updateButtonText}>
            {updating ? 'Updating...' : 'Update Status'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#2196F3',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  orderInfo: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  orderId: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  customerName: {
    fontSize: 16,
    color: '#666',
    marginBottom: 4,
  },
  garmentType: {
    fontSize: 14,
    color: '#999',
    textTransform: 'capitalize',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  currentStatus: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  currentStatusText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
  },
  statusOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    borderWidth: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  statusOptionSelected: {
    backgroundColor: '#f8f9ff',
  },
  statusIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  statusName: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  notesInput: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    fontSize: 14,
    color: '#333',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    minHeight: 100,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  updateButton: {
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  updateButtonDisabled: {
    backgroundColor: '#ccc',
  },
  updateButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
});

export default UpdateStatusScreen;
