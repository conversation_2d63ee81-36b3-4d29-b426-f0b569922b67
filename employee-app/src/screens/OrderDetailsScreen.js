import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';

const OrderDetailsScreen = ({ navigation, route }) => {
  const { order } = route.params;
  const { employee } = useAuth();
  const [notes, setNotes] = useState('');
  const [updating, setUpdating] = useState(false);

  const statusFlow = {
    'measurement_pending': 'measurement_taken',
    'measurement_taken': 'cutting',
    'cutting': 'stitching',
    'stitching': 'fitting',
    'fitting': 'finishing',
    'finishing': 'ready'
  };

  const getStatusColor = (status) => {
    const colors = {
      'measurement_pending': '#FF9800',
      'measurement_taken': '#2196F3',
      'cutting': '#9C27B0',
      'stitching': '#FF5722',
      'fitting': '#607D8B',
      'finishing': '#795548',
      'ready': '#4CAF50',
      'delivered': '#8BC34A',
    };
    return colors[status] || '#757575';
  };

  const getStatusIcon = (status) => {
    const icons = {
      'measurement_pending': 'ruler-outline',
      'measurement_taken': 'checkmark-circle-outline',
      'cutting': 'cut-outline',
      'stitching': 'build-outline',
      'fitting': 'person-outline',
      'finishing': 'brush-outline',
      'ready': 'checkmark-done-outline',
      'delivered': 'car-outline',
    };
    return icons[status] || 'ellipse-outline';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'normal': '#4CAF50',
      'urgent': '#FF9800',
      'express': '#F44336',
    };
    return colors[priority] || '#757575';
  };

  const canUpdateStatus = (currentStatus) => {
    const updatableStatuses = [
      'measurement_taken',
      'cutting',
      'stitching',
      'fitting',
      'finishing'
    ];
    return updatableStatuses.includes(currentStatus);
  };

  const handleStatusUpdate = async (newStatus) => {
    if (!newStatus) return;

    Alert.alert(
      'Update Status',
      `Mark this order as "${newStatus.replace('_', ' ')}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Update',
          onPress: async () => {
            setUpdating(true);
            try {
              // Mock update - replace with real DataSyncService call
              console.log(`Updating order ${order.id} to ${newStatus}`);
              
              Alert.alert('Success', `Order updated to ${newStatus.replace('_', ' ')}`, [
                { text: 'OK', onPress: () => navigation.goBack() }
              ]);
            } catch (error) {
              Alert.alert('Error', 'Failed to update order status');
            } finally {
              setUpdating(false);
            }
          }
        }
      ]
    );
  };

  const nextStatus = statusFlow[order.status];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Order Details</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Order Header */}
        <View style={styles.orderHeader}>
          <View style={styles.orderIdSection}>
            <Text style={styles.orderId}>#{order.id}</Text>
            <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(order.priority) }]}>
              <Text style={styles.priorityText}>{order.priority?.toUpperCase()}</Text>
            </View>
          </View>
          
          <Text style={styles.customerName}>{order.customer_name}</Text>
          
          <View style={styles.statusContainer}>
            <Ionicons 
              name={getStatusIcon(order.status)} 
              size={24} 
              color={getStatusColor(order.status)} 
            />
            <Text style={[styles.statusText, { color: getStatusColor(order.status) }]}>
              {order.status.replace('_', ' ').toUpperCase()}
            </Text>
          </View>
        </View>

        {/* Garment Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Garment Details</Text>
          <View style={styles.detailRow}>
            <Ionicons name="shirt-outline" size={20} color="#666" />
            <Text style={styles.detailLabel}>Type:</Text>
            <Text style={styles.detailValue}>{order.garment_type}</Text>
          </View>
          <View style={styles.detailRow}>
            <Ionicons name="calendar-outline" size={20} color="#666" />
            <Text style={styles.detailLabel}>Due Date:</Text>
            <Text style={styles.detailValue}>
              {new Date(order.delivery_date).toLocaleDateString()}
            </Text>
          </View>
          <View style={styles.detailRow}>
            <Ionicons name="time-outline" size={20} color="#666" />
            <Text style={styles.detailLabel}>Days Left:</Text>
            <Text style={[
              styles.detailValue,
              { color: Math.ceil((new Date(order.delivery_date) - new Date()) / (1000 * 60 * 60 * 24)) <= 2 ? '#F44336' : '#666' }
            ]}>
              {Math.ceil((new Date(order.delivery_date) - new Date()) / (1000 * 60 * 60 * 24))} days
            </Text>
          </View>
        </View>

        {/* Customer Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Customer Information</Text>
          <View style={styles.customerInfo}>
            <View style={styles.customerAvatar}>
              <Text style={styles.customerInitial}>
                {order.customer_name.charAt(0)}
              </Text>
            </View>
            <View style={styles.customerDetails}>
              <Text style={styles.customerNameDetail}>{order.customer_name}</Text>
              <Text style={styles.customerPhone}>+880 1700-000000</Text>
              <Text style={styles.customerAddress}>Dhaka, Bangladesh</Text>
            </View>
          </View>
        </View>

        {/* Measurements */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Measurements</Text>
          <View style={styles.measurementGrid}>
            <View style={styles.measurementItem}>
              <Text style={styles.measurementLabel}>Chest</Text>
              <Text style={styles.measurementValue}>40"</Text>
            </View>
            <View style={styles.measurementItem}>
              <Text style={styles.measurementLabel}>Waist</Text>
              <Text style={styles.measurementValue}>34"</Text>
            </View>
            <View style={styles.measurementItem}>
              <Text style={styles.measurementLabel}>Length</Text>
              <Text style={styles.measurementValue}>28"</Text>
            </View>
            <View style={styles.measurementItem}>
              <Text style={styles.measurementLabel}>Shoulder</Text>
              <Text style={styles.measurementValue}>18"</Text>
            </View>
          </View>
        </View>

        {/* Special Instructions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Special Instructions</Text>
          <Text style={styles.instructions}>
            Customer prefers slim fit. Extra attention to collar stitching. 
            Use premium buttons. Delivery before 6 PM.
          </Text>
        </View>

        {/* Progress Notes */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Add Progress Notes</Text>
          <TextInput
            style={styles.notesInput}
            placeholder="Add notes about your progress..."
            value={notes}
            onChangeText={setNotes}
            multiline
            numberOfLines={3}
            textAlignVertical="top"
          />
        </View>

        {/* Action Buttons */}
        {canUpdateStatus(order.status) && nextStatus && (
          <View style={styles.actionSection}>
            <TouchableOpacity
              style={[styles.updateButton, { backgroundColor: getStatusColor(nextStatus) }]}
              onPress={() => handleStatusUpdate(nextStatus)}
              disabled={updating}
            >
              <Ionicons name="checkmark-circle-outline" size={20} color="white" />
              <Text style={styles.updateButtonText}>
                {updating ? 'Updating...' : `Mark as ${nextStatus.replace('_', ' ')}`}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.reportButton}
              onPress={() => Alert.alert('Report Issue', 'Issue reporting feature coming soon')}
            >
              <Ionicons name="warning-outline" size={20} color="#FF5722" />
              <Text style={styles.reportButtonText}>Report Issue</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#2196F3',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  orderHeader: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  orderIdSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  orderId: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  priorityBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  priorityText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: 'white',
  },
  customerName: {
    fontSize: 18,
    color: '#666',
    marginBottom: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
    marginLeft: 12,
    width: 80,
  },
  detailValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
    flex: 1,
  },
  customerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  customerAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  customerInitial: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  customerDetails: {
    flex: 1,
  },
  customerNameDetail: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  customerPhone: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  customerAddress: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  measurementGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  measurementItem: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#f9f9f9',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  measurementLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  measurementValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  instructions: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  notesInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: '#333',
    backgroundColor: '#f9f9f9',
    minHeight: 80,
  },
  actionSection: {
    marginBottom: 20,
  },
  updateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  updateButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginLeft: 8,
  },
  reportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#FF5722',
  },
  reportButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF5722',
    marginLeft: 8,
  },
});

export default OrderDetailsScreen;
