import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';

// Material Design 3 Color Tokens
const lightColorScheme = {
  primary: '#1976D2',
  onPrimary: '#FFFFFF',
  primaryContainer: '#D3E3FD',
  onPrimaryContainer: '#001C38',
  secondary: '#575E71',
  onSecondary: '#FFFFFF',
  secondaryContainer: '#DBE2F9',
  onSecondaryContainer: '#141B2C',
  tertiary: '#715573',
  onTertiary: '#FFFFFF',
  tertiaryContainer: '#FBD7FC',
  onTertiaryContainer: '#29132D',
  error: '#BA1A1A',
  onError: '#FFFFFF',
  errorContainer: '#FFDAD6',
  onErrorContainer: '#410002',
  background: '#FEFBFF',
  onBackground: '#1B1B1F',
  surface: '#FEFBFF',
  onSurface: '#1B1B1F',
  surfaceVariant: '#E1E2EC',
  onSurfaceVariant: '#44474F',
  outline: '#75777F',
  outlineVariant: '#C5C6D0',
  shadow: '#000000',
  scrim: '#000000',
  inverseSurface: '#303034',
  inverseOnSurface: '#F2F0F4',
  inversePrimary: '#A4C8FF',
  elevation: {
    level0: 'transparent',
    level1: '#F7F9FF',
    level2: '#F1F4FF',
    level3: '#ECF0FF',
    level4: '#E9EDFF',
    level5: '#E6EAFF',
  },
};

const darkColorScheme = {
  primary: '#A4C8FF',
  onPrimary: '#003060',
  primaryContainer: '#004788',
  onPrimaryContainer: '#D3E3FD',
  secondary: '#BFC6DC',
  onSecondary: '#293041',
  secondaryContainer: '#3F4759',
  onSecondaryContainer: '#DBE2F9',
  tertiary: '#DFBBDF',
  onTertiary: '#3F2844',
  tertiaryContainer: '#573E5B',
  onTertiaryContainer: '#FBD7FC',
  error: '#FFB4AB',
  onError: '#690005',
  errorContainer: '#93000A',
  onErrorContainer: '#FFDAD6',
  background: '#111318',
  onBackground: '#E3E2E6',
  surface: '#111318',
  onSurface: '#E3E2E6',
  surfaceVariant: '#44474F',
  onSurfaceVariant: '#C5C6D0',
  outline: '#8F9099',
  outlineVariant: '#44474F',
  shadow: '#000000',
  scrim: '#000000',
  inverseSurface: '#E3E2E6',
  inverseOnSurface: '#2F3033',
  inversePrimary: '#1976D2',
  elevation: {
    level0: 'transparent',
    level1: '#1D1B20',
    level2: '#232229',
    level3: '#2B2930',
    level4: '#2C2B32',
    level5: '#2E2D36',
  },
};

export const lightTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    ...lightColorScheme,
  },
};

export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    ...darkColorScheme,
  },
};

// Component specific styles
export const componentStyles = {
  card: {
    borderRadius: 12,
    elevation: 1,
  },
  button: {
    borderRadius: 20,
  },
  fab: {
    borderRadius: 16,
  },
  chip: {
    borderRadius: 8,
  },
  bottomSheet: {
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
  },
  navbar: {
    height: 80,
    borderRadius: 0,
  },
};
