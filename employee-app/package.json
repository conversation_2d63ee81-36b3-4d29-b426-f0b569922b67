{"name": "elite-tailor-employee-app", "version": "1.0.0", "description": "Employee mobile app for Elite Tailor Shop - Task management and order updates", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "test": "jest", "lint": "eslint ."}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/netinfo": "9.3.10", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "expo": "~49.0.0", "expo-barcode-scanner": "~12.5.3", "expo-camera": "~13.4.2", "expo-sqlite": "~11.3.3", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.3", "react-native-gesture-handler": "~2.12.0", "react-native-paper": "^5.10.1", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-vector-icons": "^10.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "eslint": "^8.44.0", "eslint-config-expo": "^7.0.0", "jest": "^29.5.0", "typescript": "^5.1.3"}, "keywords": ["tailor", "employee", "mobile", "task-management", "react-native", "expo"], "author": "Elite Tailor Shop", "license": "MIT", "private": true}