import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import {
  StyleSheet,
  View,
  Alert,
  ScrollView,
  RefreshControl,
} from 'react-native';
import {
  PaperProvider,
  MD3LightTheme,
  MD3DarkTheme,
  Surface,
  Text,
  TextInput,
  Button,
  Card,
  Avatar,
  Chip,
  Appbar,
  useTheme,
} from 'react-native-paper';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';

// Enhanced Employee App with Full Features (except accounting/analytics)
const AppContent = () => {
  const [currentScreen, setCurrentScreen] = useState('login');
  const [employee, setEmployee] = useState(null);
  const [employeeId, setEmployeeId] = useState('');
  const [pin, setPin] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [showBottomSheet, setShowBottomSheet] = useState(false);
  const [bottomSheetType, setBottomSheetType] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isDarkMode, setIsDarkMode] = useState(false);
  const theme = useTheme();

  // Enhanced mock employee data
  const employees = {
    'EMP001': {
      id: 'EMP001',
      name: 'Ahmed Hassan',
      role: 'Senior Tailor',
      pin: '1234',
      avatar: 'AH',
      skills: ['Suit Making', 'Shirt Tailoring', 'Alterations'],
      experience: '8 years',
      rating: 4.9,
      completedOrders: 156,
      phone: '+880 1700-000001',
      joinDate: '2016-03-15'
    },
    'EMP002': {
      id: 'EMP002',
      name: 'Fatima Rahman',
      role: 'Junior Tailor',
      pin: '1234',
      avatar: 'FR',
      skills: ['Dress Making', 'Blouse Tailoring', 'Embroidery'],
      experience: '3 years',
      rating: 4.7,
      completedOrders: 89,
      phone: '+880 1700-000002',
      joinDate: '2021-07-20'
    },
    'EMP003': {
      id: 'EMP003',
      name: 'Mohammad Ali',
      role: 'Cutter',
      pin: '1234',
      avatar: 'MA',
      skills: ['Pattern Making', 'Fabric Cutting', 'Quality Control'],
      experience: '5 years',
      rating: 4.8,
      completedOrders: 234,
      phone: '+880 1700-000003',
      joinDate: '2019-01-10'
    },
  };

  // Comprehensive mock data
  const mockCustomers = [
    {
      id: 'CUST001',
      name: 'Ahmed Rahman',
      phone: '+880 1700-111001',
      email: '<EMAIL>',
      address: 'House 123, Road 5, Dhanmondi, Dhaka',
      joinDate: '2024-01-15',
      totalOrders: 5,
      measurements: {
        chest: '42"', waist: '36"', length: '30"', shoulder: '18"'
      }
    },
    {
      id: 'CUST002',
      name: 'Fatima Khan',
      phone: '+880 1700-111002',
      email: '<EMAIL>',
      address: 'Apartment 4B, Gulshan Avenue, Dhaka',
      joinDate: '2024-02-20',
      totalOrders: 3,
      measurements: {
        bust: '36"', waist: '28"', hips: '38"', length: '58"'
      }
    },
    {
      id: 'CUST003',
      name: 'Mohammad Ali',
      phone: '+880 1700-111003',
      email: '<EMAIL>',
      address: 'Plot 67, Banani, Dhaka',
      joinDate: '2024-03-10',
      totalOrders: 8,
      measurements: {
        chest: '40"', waist: '34"', sleeve: '24"', collar: '16"'
      }
    }
  ];

  const mockProducts = [
    {
      id: 'PROD001',
      name: 'Premium Wool Suit',
      category: 'Suits',
      price: 8500,
      fabric: 'Wool Blend',
      colors: ['Navy Blue', 'Charcoal', 'Black'],
      sizes: ['S', 'M', 'L', 'XL', 'XXL'],
      inStock: true,
      description: 'High-quality wool blend suit perfect for formal occasions'
    },
    {
      id: 'PROD002',
      name: 'Silk Evening Dress',
      category: 'Dresses',
      price: 6500,
      fabric: 'Pure Silk',
      colors: ['Emerald Green', 'Royal Blue', 'Burgundy'],
      sizes: ['XS', 'S', 'M', 'L', 'XL'],
      inStock: true,
      description: 'Elegant silk dress for special occasions'
    },
    {
      id: 'PROD003',
      name: 'Cotton Formal Shirt',
      category: 'Shirts',
      price: 1200,
      fabric: 'Cotton Blend',
      colors: ['White', 'Light Blue', 'Pink'],
      sizes: ['S', 'M', 'L', 'XL'],
      inStock: true,
      description: 'Professional cotton shirt for business wear'
    }
  ];

  const mockOrders = [
    {
      id: 'ORD001',
      customerId: 'CUST001',
      customerName: 'Ahmed Rahman',
      customerPhone: '+880 1700-111001',
      productId: 'PROD001',
      garmentType: 'Three-Piece Suit',
      fabric: 'Wool Blend',
      color: 'Navy Blue',
      status: 'cutting',
      priority: 'urgent',
      dueDate: '2025-05-30',
      orderDate: '2025-05-20',
      assignedTo: 'EMP001',
      estimatedPrice: 8500,
      advancePayment: 4000,
      measurements: {
        chest: '42"', waist: '36"', length: '30"', shoulder: '18"'
      },
      notes: 'Customer prefers slim fit. Extra attention to collar stitching.',
      images: ['suit1.jpg', 'suit2.jpg']
    },
    {
      id: 'ORD002',
      customerId: 'CUST002',
      customerName: 'Fatima Khan',
      customerPhone: '+880 1700-111002',
      productId: 'PROD002',
      garmentType: 'Evening Dress',
      fabric: 'Silk Chiffon',
      color: 'Emerald Green',
      status: 'stitching',
      priority: 'normal',
      dueDate: '2025-06-02',
      orderDate: '2025-05-18',
      assignedTo: 'EMP002',
      estimatedPrice: 6500,
      advancePayment: 3000,
      measurements: {
        bust: '36"', waist: '28"', hips: '38"', length: '58"'
      },
      notes: 'Wedding guest dress. Needs to be ready by June 1st.',
      images: ['dress1.jpg']
    },
    {
      id: 'ORD003',
      customerId: 'CUST003',
      customerName: 'Mohammad Ali',
      customerPhone: '+880 1700-111003',
      productId: 'PROD003',
      garmentType: 'Formal Shirt',
      fabric: 'Cotton Blend',
      color: 'White',
      status: 'fitting',
      priority: 'express',
      dueDate: '2025-05-28',
      orderDate: '2025-05-25',
      assignedTo: 'EMP001',
      estimatedPrice: 1200,
      advancePayment: 600,
      measurements: {
        chest: '40"', waist: '34"', sleeve: '24"', collar: '16"'
      },
      notes: 'Business meeting tomorrow. Rush order.',
      images: ['shirt1.jpg']
    },
    {
      id: 'ORD004',
      customerId: 'CUST002',
      customerName: 'Rashida Begum',
      customerPhone: '+880 1700-111004',
      productId: 'PROD002',
      garmentType: 'Saree Blouse',
      fabric: 'Silk',
      color: 'Gold',
      status: 'measurement_taken',
      priority: 'normal',
      dueDate: '2025-06-05',
      orderDate: '2025-05-22',
      assignedTo: 'EMP002',
      estimatedPrice: 2500,
      advancePayment: 1000,
      measurements: {
        bust: '38"', waist: '32"', length: '14"', sleeve: '12"'
      },
      notes: 'Traditional design with heavy embroidery work.',
      images: ['blouse1.jpg']
    },
    {
      id: 'ORD005',
      customerId: 'CUST001',
      customerName: 'Ahmed Rahman',
      customerPhone: '+880 1700-111001',
      productId: 'PROD003',
      garmentType: 'Business Shirt',
      fabric: 'Cotton',
      color: 'Light Blue',
      status: 'ready',
      priority: 'normal',
      dueDate: '2025-05-25',
      orderDate: '2025-05-15',
      assignedTo: 'EMP001',
      estimatedPrice: 1200,
      advancePayment: 1200,
      measurements: {
        chest: '42"', waist: '36"', sleeve: '24"', collar: '16"'
      },
      notes: 'Regular customer. Standard measurements.',
      images: ['shirt2.jpg']
    }
  ];

  // Enhanced handlers for all features
  const handleLogin = () => {
    const emp = employees[employeeId];
    if (emp && emp.pin === pin) {
      setEmployee(emp);
      setCurrentScreen('dashboard');
    } else {
      Alert.alert('Error', 'Invalid Employee ID or PIN');
    }
  };

  const handleLogout = () => {
    setEmployee(null);
    setEmployeeId('');
    setPin('');
    setCurrentScreen('login');
  };

  const getMyOrders = () => {
    return mockOrders.filter(order => order.assignedTo === employee?.id);
  };

  const getAllOrders = () => {
    return searchQuery
      ? mockOrders.filter(order =>
          order.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
          order.garmentType.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : mockOrders;
  };

  const getCustomers = () => {
    return searchQuery
      ? mockCustomers.filter(customer =>
          customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          customer.phone.includes(searchQuery) ||
          customer.email.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : mockCustomers;
  };

  const getProducts = () => {
    return searchQuery
      ? mockProducts.filter(product =>
          product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.category.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : mockProducts;
  };

  const updateOrderStatus = (orderId, newStatus) => {
    Alert.alert('Success', `Order ${orderId} updated!`);
  };

  const handleRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const openBottomSheet = (type, data = null) => {
    setBottomSheetType(type);
    if (type === 'order' && data) setSelectedOrder(data);
    if (type === 'customer' && data) setSelectedCustomer(data);
    setShowBottomSheet(true);
  };

  const closeBottomSheet = () => {
    setShowBottomSheet(false);
    setBottomSheetType('');
    setSelectedOrder(null);
    setSelectedCustomer(null);
  };

  const handleNavigation = (screen) => {
    setCurrentScreen(screen);
    setSearchQuery('');
  };

  const getStatusColor = (status) => {
    const colors = {
      'cutting': '#9C27B0',
      'stitching': '#FF5722',
      'fitting': '#607D8B',
      'ready': '#4CAF50',
    };
    return colors[status] || '#757575';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'normal': '#4CAF50',
      'urgent': '#FF9800',
      'express': '#F44336',
    };
    return colors[priority] || '#757575';
  };

  // Enhanced Login Screen
  if (currentScreen === 'login') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.primary }]}>
        <StatusBar style="light" />
        <ScrollView contentContainerStyle={styles.loginContainer}>
          <View style={styles.loginHeader}>
            <Avatar.Icon
              size={80}
              icon="account-hard-hat"
              style={{ backgroundColor: theme.colors.primaryContainer }}
            />
            <Text variant="displaySmall" style={styles.title}>
              Elite Tailor
            </Text>
            <Text variant="titleMedium" style={styles.subtitle}>
              Employee Portal
            </Text>
          </View>

          <Card style={styles.loginCard}>
            <Card.Content>
              <Text variant="headlineSmall" style={styles.loginTitle}>
                Welcome Back
              </Text>

              <TextInput
                mode="outlined"
                label="Employee ID"
                value={employeeId}
                onChangeText={setEmployeeId}
                placeholder="e.g., EMP001"
                autoCapitalize="characters"
                style={styles.textInput}
              />

              <TextInput
                mode="outlined"
                label="PIN"
                value={pin}
                onChangeText={setPin}
                placeholder="Enter 4-digit PIN"
                secureTextEntry
                keyboardType="numeric"
                maxLength={4}
                style={styles.textInput}
              />

              <Button
                mode="contained"
                onPress={handleLogin}
                style={styles.loginButton}
              >
                Sign In
              </Button>

              <Text variant="bodySmall" style={styles.demoText}>
                Demo: EMP001/1234, EMP002/1234, EMP003/1234
              </Text>
            </Card.Content>
          </Card>
        </ScrollView>
      </SafeAreaView>
    );
  }

  // Comprehensive Dashboard Screen
  if (currentScreen === 'dashboard') {
    const myOrders = getMyOrders();
    const allOrders = getAllOrders();
    const customers = getCustomers();
    const products = getProducts();

    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content
            title="Dashboard"
            subtitle={`Welcome, ${employee.name}`}
          />
          <Appbar.Action
            icon="theme-light-dark"
            onPress={() => setIsDarkMode(!isDarkMode)}
          />
          <Appbar.Action
            icon="logout"
            onPress={handleLogout}
          />
        </Appbar.Header>

        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        >
          {/* Quick Stats */}
          <View style={styles.statsContainer}>
            <Card style={styles.statCard} onPress={() => handleNavigation('tasks')}>
              <Card.Content style={styles.statCardContent}>
                <Text variant="displaySmall" style={styles.statNumber}>
                  {myOrders.length}
                </Text>
                <Text variant="bodyMedium">My Tasks</Text>
              </Card.Content>
            </Card>

            <Card style={styles.statCard} onPress={() => handleNavigation('orders')}>
              <Card.Content style={styles.statCardContent}>
                <Text variant="displaySmall" style={styles.statNumber}>
                  {allOrders.length}
                </Text>
                <Text variant="bodyMedium">All Orders</Text>
              </Card.Content>
            </Card>

            <Card style={styles.statCard} onPress={() => handleNavigation('customers')}>
              <Card.Content style={styles.statCardContent}>
                <Text variant="displaySmall" style={styles.statNumber}>
                  {customers.length}
                </Text>
                <Text variant="bodyMedium">Customers</Text>
              </Card.Content>
            </Card>
          </View>

          {/* Quick Actions */}
          <View style={styles.quickActionsContainer}>
            <Text variant="titleLarge" style={styles.sectionTitle}>
              Quick Actions
            </Text>

            <View style={styles.quickActionsGrid}>
              <Card style={styles.quickActionCard} onPress={() => handleNavigation('orders')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="plus" style={{ backgroundColor: theme.colors.primaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>New Order</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('customers')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="account-plus" style={{ backgroundColor: theme.colors.secondaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Add Customer</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('products')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="hanger" style={{ backgroundColor: theme.colors.tertiaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Products</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => Alert.alert('Scan', 'QR Scanner coming soon!')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="qrcode-scan" style={{ backgroundColor: theme.colors.errorContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Scan QR</Text>
                </Card.Content>
              </Card>
            </View>
          </View>

          {/* Recent Orders */}
          <View style={styles.recentOrdersContainer}>
            <View style={styles.sectionHeader}>
              <Text variant="titleLarge" style={styles.sectionTitle}>
                Recent Orders
              </Text>
              <Button
                mode="text"
                onPress={() => handleNavigation('orders')}
              >
                View All
              </Button>
            </View>

            {allOrders.slice(0, 3).map((order) => (
              <Card
                key={order.id}
                style={styles.orderCard}
                onPress={() => openBottomSheet('order', order)}
              >
                <Card.Content>
                  <View style={styles.orderHeader}>
                    <Text variant="titleMedium">#{order.id}</Text>
                    <Chip mode="flat">
                      {order.priority.toUpperCase()}
                    </Chip>
                  </View>
                  <Text variant="bodyLarge" style={styles.customerName}>
                    {order.customerName}
                  </Text>
                  <Text variant="bodyMedium">
                    {order.garmentType}
                  </Text>
                  <Text variant="bodySmall">
                    Due: {order.dueDate}
                  </Text>
                  <View style={styles.statusContainer}>
                    <Chip mode="flat">
                      {order.status.replace('_', ' ').toUpperCase()}
                    </Chip>
                    <Text variant="bodySmall">
                      ৳{order.estimatedPrice}
                    </Text>
                  </View>
                </Card.Content>
              </Card>
            ))}
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={[styles.navButton, currentScreen === 'dashboard' && styles.activeNavButton]}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="account-group"
              onPress={() => handleNavigation('customers')}
              style={styles.navButton}
            >
              Customers
            </Button>
            <Button
              mode="text"
              icon="hanger"
              onPress={() => handleNavigation('products')}
              style={styles.navButton}
            >
              Products
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Enhanced Orders Screen
  if (currentScreen === 'orders') {
    const orders = getAllOrders();

    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content title="All Orders" />
          <Appbar.Action
            icon="plus"
            onPress={() => Alert.alert('Add Order', 'Create new order functionality coming soon!')}
          />
        </Appbar.Header>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <TextInput
            mode="outlined"
            label="Search orders..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.searchInput}
            left={<TextInput.Icon icon="magnify" />}
            right={searchQuery ? <TextInput.Icon icon="close" onPress={() => setSearchQuery('')} /> : null}
          />
        </View>

        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        >
          <View style={styles.ordersSection}>
            {orders.length === 0 ? (
              <Card style={styles.emptyCard}>
                <Card.Content style={styles.emptyCardContent}>
                  <Text variant="headlineSmall">No Orders Found</Text>
                  <Text variant="bodyMedium">Try adjusting your search</Text>
                </Card.Content>
              </Card>
            ) : (
              orders.map((order) => (
                <Card
                  key={order.id}
                  style={styles.orderCard}
                  onPress={() => openBottomSheet('order', order)}
                >
                  <Card.Content>
                    <View style={styles.orderHeader}>
                      <Text variant="titleMedium">#{order.id}</Text>
                      <Chip mode="flat" style={{ backgroundColor: getPriorityColor(order.priority) + '20' }}>
                        {order.priority.toUpperCase()}
                      </Chip>
                    </View>
                    <Text variant="bodyLarge" style={styles.customerName}>
                      {order.customerName}
                    </Text>
                    <Text variant="bodyMedium">
                      {order.garmentType} • {order.fabric}
                    </Text>
                    <Text variant="bodySmall">
                      Due: {order.dueDate} • Assigned: {order.assignedTo}
                    </Text>
                    <View style={styles.statusContainer}>
                      <Chip mode="flat" style={{ backgroundColor: getStatusColor(order.status) + '20' }}>
                        {order.status.replace('_', ' ').toUpperCase()}
                      </Chip>
                      <Text variant="bodySmall" style={styles.priceText}>
                        ৳{order.estimatedPrice}
                      </Text>
                    </View>
                  </Card.Content>
                </Card>
              ))
            )}
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={[styles.navButton, currentScreen === 'orders' && styles.activeNavButton]}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="account-group"
              onPress={() => handleNavigation('customers')}
              style={styles.navButton}
            >
              Customers
            </Button>
            <Button
              mode="text"
              icon="hanger"
              onPress={() => handleNavigation('products')}
              style={styles.navButton}
            >
              Products
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Enhanced Tasks Screen (My Tasks)
  if (currentScreen === 'tasks') {
    const myOrders = getMyOrders();

    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content
            title="My Tasks"
            subtitle={`${myOrders.length} assigned orders`}
          />
        </Appbar.Header>

        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        >
          <View style={styles.statsContainer}>
            <Card style={styles.statCard}>
              <Card.Content style={styles.statCardContent}>
                <Text variant="displaySmall" style={styles.statNumber}>
                  {myOrders.length}
                </Text>
                <Text variant="bodyMedium">Total Tasks</Text>
              </Card.Content>
            </Card>

            <Card style={styles.statCard}>
              <Card.Content style={styles.statCardContent}>
                <Text variant="displaySmall" style={styles.statNumber}>
                  {myOrders.filter(o => o.priority === 'urgent' || o.priority === 'express').length}
                </Text>
                <Text variant="bodyMedium">Urgent</Text>
              </Card.Content>
            </Card>
          </View>

          <View style={styles.ordersSection}>
            <Text variant="titleLarge" style={styles.sectionTitle}>
              My Assigned Orders
            </Text>

            {myOrders.length === 0 ? (
              <Card style={styles.emptyCard}>
                <Card.Content style={styles.emptyCardContent}>
                  <Text variant="headlineSmall">No Tasks Assigned</Text>
                  <Text variant="bodyMedium">Check back later for new orders</Text>
                </Card.Content>
              </Card>
            ) : (
              myOrders.map((order) => (
                <Card
                  key={order.id}
                  style={styles.orderCard}
                  onPress={() => openBottomSheet('order', order)}
                >
                  <Card.Content>
                    <View style={styles.orderHeader}>
                      <Text variant="titleMedium">#{order.id}</Text>
                      <Chip mode="flat" style={{ backgroundColor: getPriorityColor(order.priority) + '20' }}>
                        {order.priority.toUpperCase()}
                      </Chip>
                    </View>
                    <Text variant="bodyLarge" style={styles.customerName}>
                      {order.customerName}
                    </Text>
                    <Text variant="bodyMedium">
                      {order.garmentType}
                    </Text>
                    <Text variant="bodySmall">
                      Due: {order.dueDate}
                    </Text>
                    <View style={styles.statusContainer}>
                      <Chip mode="flat" style={{ backgroundColor: getStatusColor(order.status) + '20' }}>
                        {order.status.replace('_', ' ').toUpperCase()}
                      </Chip>
                      <Button
                        mode="contained"
                        compact
                        onPress={() => updateOrderStatus(order.id, 'next_status')}
                      >
                        Update
                      </Button>
                    </View>
                  </Card.Content>
                </Card>
              ))
            )}
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="account-group"
              onPress={() => handleNavigation('customers')}
              style={styles.navButton}
            >
              Customers
            </Button>
            <Button
              mode="text"
              icon="hanger"
              onPress={() => handleNavigation('products')}
              style={styles.navButton}
            >
              Products
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Customers Screen
  if (currentScreen === 'customers') {
    const customers = getCustomers();

    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content title="Customers" />
          <Appbar.Action
            icon="plus"
            onPress={() => Alert.alert('Add Customer', 'Add new customer functionality coming soon!')}
          />
        </Appbar.Header>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <TextInput
            mode="outlined"
            label="Search customers..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.searchInput}
            left={<TextInput.Icon icon="magnify" />}
            right={searchQuery ? <TextInput.Icon icon="close" onPress={() => setSearchQuery('')} /> : null}
          />
        </View>

        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        >
          <View style={styles.ordersSection}>
            {customers.length === 0 ? (
              <Card style={styles.emptyCard}>
                <Card.Content style={styles.emptyCardContent}>
                  <Text variant="headlineSmall">No Customers Found</Text>
                  <Text variant="bodyMedium">Try adjusting your search</Text>
                </Card.Content>
              </Card>
            ) : (
              customers.map((customer) => (
                <Card
                  key={customer.id}
                  style={styles.orderCard}
                  onPress={() => openBottomSheet('customer', customer)}
                >
                  <Card.Content>
                    <View style={styles.orderHeader}>
                      <View style={styles.customerHeader}>
                        <Avatar.Text
                          size={40}
                          label={customer.name.charAt(0)}
                          style={{ backgroundColor: theme.colors.primaryContainer }}
                        />
                        <View style={styles.customerInfo}>
                          <Text variant="titleMedium">{customer.name}</Text>
                          <Text variant="bodySmall">{customer.phone}</Text>
                        </View>
                      </View>
                      <Chip mode="flat">
                        {customer.totalOrders} orders
                      </Chip>
                    </View>
                    <Text variant="bodyMedium" style={styles.customerAddress}>
                      {customer.address}
                    </Text>
                    <Text variant="bodySmall">
                      Member since: {new Date(customer.joinDate).toLocaleDateString()}
                    </Text>
                  </Card.Content>
                </Card>
              ))
            )}
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="account-group"
              onPress={() => handleNavigation('customers')}
              style={[styles.navButton, currentScreen === 'customers' && styles.activeNavButton]}
            >
              Customers
            </Button>
            <Button
              mode="text"
              icon="hanger"
              onPress={() => handleNavigation('products')}
              style={styles.navButton}
            >
              Products
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Products Screen
  if (currentScreen === 'products') {
    const products = getProducts();

    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content title="Products" />
          <Appbar.Action
            icon="plus"
            onPress={() => Alert.alert('Add Product', 'Add new product functionality coming soon!')}
          />
        </Appbar.Header>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <TextInput
            mode="outlined"
            label="Search products..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.searchInput}
            left={<TextInput.Icon icon="magnify" />}
            right={searchQuery ? <TextInput.Icon icon="close" onPress={() => setSearchQuery('')} /> : null}
          />
        </View>

        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        >
          <View style={styles.ordersSection}>
            {products.length === 0 ? (
              <Card style={styles.emptyCard}>
                <Card.Content style={styles.emptyCardContent}>
                  <Text variant="headlineSmall">No Products Found</Text>
                  <Text variant="bodyMedium">Try adjusting your search</Text>
                </Card.Content>
              </Card>
            ) : (
              products.map((product) => (
                <Card
                  key={product.id}
                  style={styles.orderCard}
                  onPress={() => Alert.alert('Product Details', `${product.name}\n${product.description}\nPrice: ৳${product.price}`)}
                >
                  <Card.Content>
                    <View style={styles.orderHeader}>
                      <View style={styles.productHeader}>
                        <Avatar.Icon
                          size={40}
                          icon="hanger"
                          style={{ backgroundColor: theme.colors.tertiaryContainer }}
                        />
                        <View style={styles.productInfo}>
                          <Text variant="titleMedium">{product.name}</Text>
                          <Text variant="bodySmall">{product.category}</Text>
                        </View>
                      </View>
                      <View style={styles.productPrice}>
                        <Text variant="titleMedium" style={styles.priceText}>৳{product.price}</Text>
                        <Chip mode="flat" style={{ backgroundColor: product.inStock ? '#4CAF50' : '#F44336' }}>
                          {product.inStock ? 'In Stock' : 'Out of Stock'}
                        </Chip>
                      </View>
                    </View>
                    <Text variant="bodyMedium" style={styles.productDescription}>
                      {product.description}
                    </Text>
                    <Text variant="bodySmall">
                      Fabric: {product.fabric} • Colors: {product.colors.join(', ')}
                    </Text>
                  </Card.Content>
                </Card>
              ))
            )}
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="account-group"
              onPress={() => handleNavigation('customers')}
              style={styles.navButton}
            >
              Customers
            </Button>
            <Button
              mode="text"
              icon="hanger"
              onPress={() => handleNavigation('products')}
              style={[styles.navButton, currentScreen === 'products' && styles.activeNavButton]}
            >
              Products
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  return (
    <View style={{ flex: 1 }}>
      {/* Simple Bottom Sheet Modal */}
      {showBottomSheet && (
        <View style={styles.bottomSheetOverlay}>
          <View style={styles.bottomSheetBackdrop} onTouchEnd={closeBottomSheet} />
          <View style={[styles.bottomSheetContainer, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.bottomSheetHandle} />

            {bottomSheetType === 'order' && selectedOrder && (
              <ScrollView style={styles.bottomSheetContent}>
                <Text variant="headlineSmall" style={styles.bottomSheetTitle}>
                  Order Details
                </Text>

                <Card style={styles.detailCard}>
                  <Card.Content>
                    <Text variant="titleMedium">Order #{selectedOrder.id}</Text>
                    <Text variant="bodyLarge">{selectedOrder.customerName}</Text>
                    <Text variant="bodyMedium">{selectedOrder.garmentType}</Text>
                    <Text variant="bodySmall">Due: {selectedOrder.dueDate}</Text>
                    <Text variant="bodySmall">Price: ৳{selectedOrder.estimatedPrice}</Text>
                    <Text variant="bodySmall">Advance: ৳{selectedOrder.advancePayment}</Text>
                    <Text variant="bodySmall">Status: {selectedOrder.status}</Text>
                    <Text variant="bodySmall">Priority: {selectedOrder.priority}</Text>
                    <Text variant="bodySmall">Assigned to: {selectedOrder.assignedTo}</Text>
                    {selectedOrder.notes && (
                      <Text variant="bodySmall" style={{ marginTop: 8 }}>
                        Notes: {selectedOrder.notes}
                      </Text>
                    )}
                  </Card.Content>
                </Card>

                <Button
                  mode="contained"
                  onPress={() => {
                    updateOrderStatus(selectedOrder.id, 'updated');
                    closeBottomSheet();
                  }}
                  style={styles.bottomSheetButton}
                >
                  Update Status
                </Button>
              </ScrollView>
            )}

            {bottomSheetType === 'customer' && selectedCustomer && (
              <ScrollView style={styles.bottomSheetContent}>
                <Text variant="headlineSmall" style={styles.bottomSheetTitle}>
                  Customer Details
                </Text>

                <Card style={styles.detailCard}>
                  <Card.Content>
                    <Text variant="titleMedium">{selectedCustomer.name}</Text>
                    <Text variant="bodyMedium">{selectedCustomer.phone}</Text>
                    <Text variant="bodyMedium">{selectedCustomer.email}</Text>
                    <Text variant="bodySmall">{selectedCustomer.address}</Text>
                    <Text variant="bodySmall">Total Orders: {selectedCustomer.totalOrders}</Text>
                    <Text variant="bodySmall">Member since: {new Date(selectedCustomer.joinDate).toLocaleDateString()}</Text>
                  </Card.Content>
                </Card>

                <Button
                  mode="contained"
                  onPress={() => {
                    Alert.alert('Contact', `Calling ${selectedCustomer.name}...`);
                    closeBottomSheet();
                  }}
                  style={styles.bottomSheetButton}
                >
                  Call Customer
                </Button>
              </ScrollView>
            )}
          </View>
        </View>
      )}
    </View>
  );
};

// Main App Component with Theme Provider
export default function App() {
  const [isDarkMode, setIsDarkMode] = useState(false);

  const theme = isDarkMode ? MD3DarkTheme : MD3LightTheme;

  return (
    <SafeAreaProvider>
      <PaperProvider theme={theme}>
        <AppContent />
      </PaperProvider>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  // Login Screen Styles
  loginContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 24,
  },
  loginHeader: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    marginTop: 16,
    textAlign: 'center',
    color: 'white',
  },
  subtitle: {
    marginTop: 8,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  loginCard: {
    borderRadius: 28,
    marginHorizontal: 8,
  },
  loginTitle: {
    textAlign: 'center',
    marginBottom: 24,
  },
  textInput: {
    marginBottom: 16,
  },
  loginButton: {
    marginTop: 8,
    marginBottom: 16,
    borderRadius: 20,
  },
  demoText: {
    textAlign: 'center',
    opacity: 0.7,
  },

  // Tasks Screen Styles
  content: {
    flex: 1,
  },
  statsContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  statCard: {
    flex: 1,
    borderRadius: 16,
  },
  statCardContent: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  statNumber: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  ordersSection: {
    paddingHorizontal: 16,
    paddingBottom: 100,
  },
  sectionTitle: {
    marginBottom: 16,
    fontWeight: '600',
  },
  emptyCard: {
    borderRadius: 16,
    marginTop: 32,
  },
  emptyCardContent: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  orderCard: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  customerName: {
    fontWeight: '600',
    marginBottom: 4,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  bottomNav: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  navContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  activeNavButton: {
    backgroundColor: 'rgba(0,0,0,0.1)',
  },

  // Search Container
  searchContainer: {
    padding: 16,
  },
  searchInput: {
    marginBottom: 0,
  },

  // Quick Actions
  quickActionsContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  quickActionCard: {
    flex: 1,
    minWidth: '45%',
    borderRadius: 16,
  },
  quickActionContent: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  quickActionTitle: {
    marginTop: 8,
    textAlign: 'center',
    fontSize: 12,
  },

  // Recent Orders
  recentOrdersContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },

  // Customer Styles
  customerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  customerInfo: {
    marginLeft: 12,
    flex: 1,
  },
  customerAddress: {
    marginTop: 8,
    opacity: 0.8,
  },

  // Product Styles
  productHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  productInfo: {
    marginLeft: 12,
    flex: 1,
  },
  productPrice: {
    alignItems: 'flex-end',
  },
  productDescription: {
    marginTop: 8,
    opacity: 0.8,
  },
  priceText: {
    fontWeight: '600',
    marginBottom: 4,
  },

  // Bottom Sheet Styles
  bottomSheetOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'flex-end',
    zIndex: 1000,
  },
  bottomSheetBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  bottomSheetContainer: {
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    minHeight: '50%',
    maxHeight: '80%',
    paddingBottom: 20,
  },
  bottomSheetHandle: {
    width: 32,
    height: 4,
    backgroundColor: 'rgba(0,0,0,0.3)',
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 12,
    marginBottom: 16,
  },
  bottomSheetContent: {
    flex: 1,
    paddingHorizontal: 24,
  },
  bottomSheetTitle: {
    marginBottom: 16,
    fontWeight: '600',
  },
  detailCard: {
    marginBottom: 16,
    borderRadius: 16,
  },
  bottomSheetButton: {
    marginTop: 16,
    borderRadius: 20,
  },
});
