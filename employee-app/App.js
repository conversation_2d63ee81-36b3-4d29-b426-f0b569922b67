import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import {
  StyleSheet,
  View,
  Alert,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import {
  PaperProvider,
  MD3LightTheme,
  MD3DarkTheme,
  Surface,
  Text,
  TextInput,
  Button,
  Card,
  Avatar,
  Chip,
  Appbar,
  useTheme,
  Switch,
  Divider,
  List,
  IconButton,
} from 'react-native-paper';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';

// Enhanced Employee App with Full Features (except accounting/analytics)
const AppContent = () => {
  const [currentScreen, setCurrentScreen] = useState('login');
  const [employee, setEmployee] = useState(null);
  const [employeeId, setEmployeeId] = useState('');
  const [pin, setPin] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [showBottomSheet, setShowBottomSheet] = useState(false);
  const [bottomSheetType, setBottomSheetType] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isDarkMode, setIsDarkMode] = useState(false);
  const theme = useTheme();

  // Enhanced mock employee data
  const employees = {
    'EMP001': {
      id: 'EMP001',
      name: 'Ahmed Hassan',
      role: 'Senior Tailor',
      pin: '1234',
      avatar: 'AH',
      skills: ['Suit Making', 'Shirt Tailoring', 'Alterations'],
      experience: '8 years',
      rating: 4.9,
      completedOrders: 156,
      phone: '+880 1700-000001',
      joinDate: '2016-03-15'
    },
    'EMP002': {
      id: 'EMP002',
      name: 'Fatima Rahman',
      role: 'Junior Tailor',
      pin: '1234',
      avatar: 'FR',
      skills: ['Dress Making', 'Blouse Tailoring', 'Embroidery'],
      experience: '3 years',
      rating: 4.7,
      completedOrders: 89,
      phone: '+880 1700-000002',
      joinDate: '2021-07-20'
    },
    'EMP003': {
      id: 'EMP003',
      name: 'Mohammad Ali',
      role: 'Cutter',
      pin: '1234',
      avatar: 'MA',
      skills: ['Pattern Making', 'Fabric Cutting', 'Quality Control'],
      experience: '5 years',
      rating: 4.8,
      completedOrders: 234,
      phone: '+880 1700-000003',
      joinDate: '2019-01-10'
    },
  };

  // Comprehensive mock data
  const mockCustomers = [
    {
      id: 'CUST001',
      name: 'Ahmed Rahman',
      phone: '+880 1700-111001',
      email: '<EMAIL>',
      address: 'House 123, Road 5, Dhanmondi, Dhaka',
      joinDate: '2024-01-15',
      totalOrders: 5,
      measurements: {
        chest: '42"', waist: '36"', length: '30"', shoulder: '18"'
      }
    },
    {
      id: 'CUST002',
      name: 'Fatima Khan',
      phone: '+880 1700-111002',
      email: '<EMAIL>',
      address: 'Apartment 4B, Gulshan Avenue, Dhaka',
      joinDate: '2024-02-20',
      totalOrders: 3,
      measurements: {
        bust: '36"', waist: '28"', hips: '38"', length: '58"'
      }
    },
    {
      id: 'CUST003',
      name: 'Mohammad Ali',
      phone: '+880 1700-111003',
      email: '<EMAIL>',
      address: 'Plot 67, Banani, Dhaka',
      joinDate: '2024-03-10',
      totalOrders: 8,
      measurements: {
        chest: '40"', waist: '34"', sleeve: '24"', collar: '16"'
      }
    }
  ];

  const mockProducts = [
    {
      id: 'PROD001',
      name: 'Premium Wool Suit',
      category: 'Suits',
      price: 8500,
      fabric: 'Wool Blend',
      colors: ['Navy Blue', 'Charcoal', 'Black'],
      sizes: ['S', 'M', 'L', 'XL', 'XXL'],
      inStock: true,
      description: 'High-quality wool blend suit perfect for formal occasions'
    },
    {
      id: 'PROD002',
      name: 'Silk Evening Dress',
      category: 'Dresses',
      price: 6500,
      fabric: 'Pure Silk',
      colors: ['Emerald Green', 'Royal Blue', 'Burgundy'],
      sizes: ['XS', 'S', 'M', 'L', 'XL'],
      inStock: true,
      description: 'Elegant silk dress for special occasions'
    },
    {
      id: 'PROD003',
      name: 'Cotton Formal Shirt',
      category: 'Shirts',
      price: 1200,
      fabric: 'Cotton Blend',
      colors: ['White', 'Light Blue', 'Pink'],
      sizes: ['S', 'M', 'L', 'XL'],
      inStock: true,
      description: 'Professional cotton shirt for business wear'
    }
  ];

  const mockOrders = [
    {
      id: 'ORD001',
      customerId: 'CUST001',
      customerName: 'Ahmed Rahman',
      customerPhone: '+880 1700-111001',
      productId: 'PROD001',
      garmentType: 'Three-Piece Suit',
      fabric: 'Wool Blend',
      color: 'Navy Blue',
      status: 'cutting',
      priority: 'urgent',
      dueDate: '2025-05-30',
      orderDate: '2025-05-20',
      assignedTo: 'EMP001',
      estimatedPrice: 8500,
      advancePayment: 4000,
      measurements: {
        chest: '42"', waist: '36"', length: '30"', shoulder: '18"'
      },
      notes: 'Customer prefers slim fit. Extra attention to collar stitching.',
      images: ['suit1.jpg', 'suit2.jpg']
    },
    {
      id: 'ORD002',
      customerId: 'CUST002',
      customerName: 'Fatima Khan',
      customerPhone: '+880 1700-111002',
      productId: 'PROD002',
      garmentType: 'Evening Dress',
      fabric: 'Silk Chiffon',
      color: 'Emerald Green',
      status: 'stitching',
      priority: 'normal',
      dueDate: '2025-06-02',
      orderDate: '2025-05-18',
      assignedTo: 'EMP002',
      estimatedPrice: 6500,
      advancePayment: 3000,
      measurements: {
        bust: '36"', waist: '28"', hips: '38"', length: '58"'
      },
      notes: 'Wedding guest dress. Needs to be ready by June 1st.',
      images: ['dress1.jpg']
    },
    {
      id: 'ORD003',
      customerId: 'CUST003',
      customerName: 'Mohammad Ali',
      customerPhone: '+880 1700-111003',
      productId: 'PROD003',
      garmentType: 'Formal Shirt',
      fabric: 'Cotton Blend',
      color: 'White',
      status: 'fitting',
      priority: 'express',
      dueDate: '2025-05-28',
      orderDate: '2025-05-25',
      assignedTo: 'EMP001',
      estimatedPrice: 1200,
      advancePayment: 600,
      measurements: {
        chest: '40"', waist: '34"', sleeve: '24"', collar: '16"'
      },
      notes: 'Business meeting tomorrow. Rush order.',
      images: ['shirt1.jpg']
    },
    {
      id: 'ORD004',
      customerId: 'CUST002',
      customerName: 'Rashida Begum',
      customerPhone: '+880 1700-111004',
      productId: 'PROD002',
      garmentType: 'Saree Blouse',
      fabric: 'Silk',
      color: 'Gold',
      status: 'measurement_taken',
      priority: 'normal',
      dueDate: '2025-06-05',
      orderDate: '2025-05-22',
      assignedTo: 'EMP002',
      estimatedPrice: 2500,
      advancePayment: 1000,
      measurements: {
        bust: '38"', waist: '32"', length: '14"', sleeve: '12"'
      },
      notes: 'Traditional design with heavy embroidery work.',
      images: ['blouse1.jpg']
    },
    {
      id: 'ORD005',
      customerId: 'CUST001',
      customerName: 'Ahmed Rahman',
      customerPhone: '+880 1700-111001',
      productId: 'PROD003',
      garmentType: 'Business Shirt',
      fabric: 'Cotton',
      color: 'Light Blue',
      status: 'ready',
      priority: 'normal',
      dueDate: '2025-05-25',
      orderDate: '2025-05-15',
      assignedTo: 'EMP001',
      estimatedPrice: 1200,
      advancePayment: 1200,
      measurements: {
        chest: '42"', waist: '36"', sleeve: '24"', collar: '16"'
      },
      notes: 'Regular customer. Standard measurements.',
      images: ['shirt2.jpg']
    }
  ];

  // Enhanced handlers for all features
  const handleLogin = () => {
    const emp = employees[employeeId];
    if (emp && emp.pin === pin) {
      setEmployee(emp);
      setCurrentScreen('dashboard');
    } else {
      Alert.alert('Error', 'Invalid Employee ID or PIN');
    }
  };

  const handleLogout = () => {
    setEmployee(null);
    setEmployeeId('');
    setPin('');
    setCurrentScreen('login');
  };

  const getMyOrders = () => {
    return mockOrders.filter(order => order.assignedTo === employee?.id);
  };

  const getAllOrders = () => {
    return searchQuery
      ? mockOrders.filter(order =>
          order.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
          order.garmentType.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : mockOrders;
  };

  const getCustomers = () => {
    return searchQuery
      ? mockCustomers.filter(customer =>
          customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          customer.phone.includes(searchQuery) ||
          customer.email.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : mockCustomers;
  };

  const getProducts = () => {
    return searchQuery
      ? mockProducts.filter(product =>
          product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.category.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : mockProducts;
  };

  const updateOrderStatus = (orderId, newStatus) => {
    Alert.alert('Success', `Order ${orderId} updated!`);
  };

  const handleRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const openBottomSheet = (type, data = null) => {
    setBottomSheetType(type);
    if (type === 'order' && data) setSelectedOrder(data);
    if (type === 'customer' && data) setSelectedCustomer(data);
    setShowBottomSheet(true);
  };

  const closeBottomSheet = () => {
    setShowBottomSheet(false);
    setBottomSheetType('');
    setSelectedOrder(null);
    setSelectedCustomer(null);
  };

  const handleNavigation = (screen) => {
    setCurrentScreen(screen);
    setSearchQuery('');
  };

  // Utility functions
  const getStatusColor = (status) => {
    const colors = {
      'measurement_pending': '#9C27B0',
      'measurement_taken': '#2196F3',
      'cutting': '#9C27B0',
      'stitching': '#FF5722',
      'fitting': '#607D8B',
      'finishing': '#795548',
      'ready': '#4CAF50',
      'delivered': '#4CAF50',
    };
    return colors[status] || '#757575';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'normal': '#4CAF50',
      'urgent': '#FF9800',
      'express': '#F44336',
    };
    return colors[priority] || '#757575';
  };



  // Enhanced Login Screen
  if (currentScreen === 'login') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.primary }]}>
        <StatusBar style="light" />
        <ScrollView contentContainerStyle={styles.loginContainer}>
          <View style={styles.loginHeader}>
            <Avatar.Icon
              size={80}
              icon="account-hard-hat"
              style={{ backgroundColor: theme.colors.primaryContainer }}
            />
            <Text variant="displaySmall" style={styles.title}>
              Elite Tailor
            </Text>
            <Text variant="titleMedium" style={styles.subtitle}>
              Employee Portal
            </Text>
          </View>

          <Card style={styles.loginCard}>
            <Card.Content>
              <Text variant="headlineSmall" style={styles.loginTitle}>
                Welcome Back
              </Text>

              <TextInput
                mode="outlined"
                label="Employee ID"
                value={employeeId}
                onChangeText={setEmployeeId}
                placeholder="e.g., EMP001"
                autoCapitalize="characters"
                style={styles.textInput}
              />

              <TextInput
                mode="outlined"
                label="PIN"
                value={pin}
                onChangeText={setPin}
                placeholder="Enter 4-digit PIN"
                secureTextEntry
                keyboardType="numeric"
                maxLength={4}
                style={styles.textInput}
              />

              <Button
                mode="contained"
                onPress={handleLogin}
                style={styles.loginButton}
              >
                Sign In
              </Button>

              <Text variant="bodySmall" style={styles.demoText}>
                Demo: EMP001/1234, EMP002/1234, EMP003/1234
              </Text>
            </Card.Content>
          </Card>
        </ScrollView>
      </SafeAreaView>
    );
  }

  // Comprehensive Dashboard Screen
  if (currentScreen === 'dashboard') {
    const myOrders = getMyOrders();
    const allOrders = getAllOrders();
    const customers = getCustomers();
    const products = getProducts();

    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content
            title="Dashboard"
            subtitle={`Welcome, ${employee.name}`}
          />
          <Appbar.Action
            icon="theme-light-dark"
            onPress={() => setIsDarkMode(!isDarkMode)}
          />
          <Appbar.Action
            icon="logout"
            onPress={handleLogout}
          />
        </Appbar.Header>

        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        >
          {/* Quick Stats */}
          <View style={styles.statsContainer}>
            <Card style={styles.statCard} onPress={() => handleNavigation('tasks')}>
              <Card.Content style={styles.statCardContent}>
                <Text variant="displaySmall" style={styles.statNumber}>
                  {myOrders.length}
                </Text>
                <Text variant="bodyMedium">My Tasks</Text>
              </Card.Content>
            </Card>

            <Card style={styles.statCard} onPress={() => handleNavigation('orders')}>
              <Card.Content style={styles.statCardContent}>
                <Text variant="displaySmall" style={styles.statNumber}>
                  {allOrders.length}
                </Text>
                <Text variant="bodyMedium">All Orders</Text>
              </Card.Content>
            </Card>

            <Card style={styles.statCard} onPress={() => handleNavigation('customers')}>
              <Card.Content style={styles.statCardContent}>
                <Text variant="displaySmall" style={styles.statNumber}>
                  {customers.length}
                </Text>
                <Text variant="bodyMedium">Customers</Text>
              </Card.Content>
            </Card>
          </View>

          {/* Quick Actions */}
          <View style={styles.quickActionsContainer}>
            <Text variant="titleLarge" style={styles.sectionTitle}>
              Quick Actions
            </Text>

            <View style={styles.quickActionsGrid}>
              <Card style={styles.quickActionCard} onPress={() => handleNavigation('orders')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="plus" style={{ backgroundColor: theme.colors.primaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>New Order</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('customers')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="account-plus" style={{ backgroundColor: theme.colors.secondaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Add Customer</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('products')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="hanger" style={{ backgroundColor: theme.colors.tertiaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Products</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => Alert.alert('Scan', 'QR Scanner coming soon!')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="qrcode-scan" style={{ backgroundColor: theme.colors.errorContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Scan QR</Text>
                </Card.Content>
              </Card>
            </View>
          </View>

          {/* Recent Orders */}
          <View style={styles.recentOrdersContainer}>
            <View style={styles.sectionHeader}>
              <Text variant="titleLarge" style={styles.sectionTitle}>
                Recent Orders
              </Text>
              <Button
                mode="text"
                onPress={() => handleNavigation('orders')}
              >
                View All
              </Button>
            </View>

            {allOrders.slice(0, 3).map((order) => (
              <Card
                key={order.id}
                style={styles.orderCard}
                onPress={() => openBottomSheet('order', order)}
              >
                <Card.Content>
                  <View style={styles.orderHeader}>
                    <Text variant="titleMedium">#{order.id}</Text>
                    <Chip mode="flat">
                      {order.priority.toUpperCase()}
                    </Chip>
                  </View>
                  <Text variant="bodyLarge" style={styles.customerName}>
                    {order.customerName}
                  </Text>
                  <Text variant="bodyMedium">
                    {order.garmentType}
                  </Text>
                  <Text variant="bodySmall">
                    Due: {order.dueDate}
                  </Text>
                  <View style={styles.statusContainer}>
                    <Chip mode="flat">
                      {order.status.replace('_', ' ').toUpperCase()}
                    </Chip>
                    <Text variant="bodySmall">
                      ৳{order.estimatedPrice}
                    </Text>
                  </View>
                </Card.Content>
              </Card>
            ))}
          </View>
        </ScrollView>

        {/* Enhanced Bottom Navigation with 5 tabs */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={[styles.navButton, currentScreen === 'dashboard' && styles.activeNavButton]}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="qrcode-scan"
              onPress={() => handleNavigation('scan')}
              style={[styles.navButton, currentScreen === 'scan' && styles.activeNavButton]}
            >
              Scan
            </Button>
            <Button
              mode="text"
              icon="plus-circle"
              onPress={() => handleNavigation('quickActions')}
              style={[styles.navButton, styles.plusButton, currentScreen === 'quickActions' && styles.activeNavButton]}
            >
              Add
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={[styles.navButton, currentScreen === 'orders' && styles.activeNavButton]}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="cog"
              onPress={() => handleNavigation('settings')}
              style={[styles.navButton, currentScreen === 'settings' && styles.activeNavButton]}
            >
              Settings
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Enhanced Orders Screen
  if (currentScreen === 'orders') {
    const orders = getAllOrders();

    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content title="All Orders" />
          <Appbar.Action
            icon="plus"
            onPress={() => Alert.alert('Add Order', 'Create new order functionality coming soon!')}
          />
        </Appbar.Header>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <TextInput
            mode="outlined"
            label="Search orders..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.searchInput}
            left={<TextInput.Icon icon="magnify" />}
            right={searchQuery ? <TextInput.Icon icon="close" onPress={() => setSearchQuery('')} /> : null}
          />
        </View>

        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        >
          <View style={styles.ordersSection}>
            {orders.length === 0 ? (
              <Card style={styles.emptyCard}>
                <Card.Content style={styles.emptyCardContent}>
                  <Text variant="headlineSmall">No Orders Found</Text>
                  <Text variant="bodyMedium">Try adjusting your search</Text>
                </Card.Content>
              </Card>
            ) : (
              orders.map((order) => (
                <Card
                  key={order.id}
                  style={styles.orderCard}
                  onPress={() => openBottomSheet('order', order)}
                >
                  <Card.Content>
                    <View style={styles.orderHeader}>
                      <Text variant="titleMedium">#{order.id}</Text>
                      <Chip mode="flat" style={{ backgroundColor: getPriorityColor(order.priority) + '20' }}>
                        {order.priority.toUpperCase()}
                      </Chip>
                    </View>
                    <Text variant="bodyLarge" style={styles.customerName}>
                      {order.customerName}
                    </Text>
                    <Text variant="bodyMedium">
                      {order.garmentType} • {order.fabric}
                    </Text>
                    <Text variant="bodySmall">
                      Due: {order.dueDate} • Assigned: {order.assignedTo}
                    </Text>
                    <View style={styles.statusContainer}>
                      <Chip mode="flat" style={{ backgroundColor: getStatusColor(order.status) + '20' }}>
                        {order.status.replace('_', ' ').toUpperCase()}
                      </Chip>
                      <Text variant="bodySmall" style={styles.priceText}>
                        ৳{order.estimatedPrice}
                      </Text>
                    </View>
                  </Card.Content>
                </Card>
              ))
            )}
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={[styles.navButton, currentScreen === 'orders' && styles.activeNavButton]}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="account-group"
              onPress={() => handleNavigation('customers')}
              style={styles.navButton}
            >
              Customers
            </Button>
            <Button
              mode="text"
              icon="hanger"
              onPress={() => handleNavigation('products')}
              style={styles.navButton}
            >
              Products
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Enhanced Tasks Screen (My Tasks)
  if (currentScreen === 'tasks') {
    const myOrders = getMyOrders();

    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content
            title="My Tasks"
            subtitle={`${myOrders.length} assigned orders`}
          />
        </Appbar.Header>

        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        >
          <View style={styles.statsContainer}>
            <Card style={styles.statCard}>
              <Card.Content style={styles.statCardContent}>
                <Text variant="displaySmall" style={styles.statNumber}>
                  {myOrders.length}
                </Text>
                <Text variant="bodyMedium">Total Tasks</Text>
              </Card.Content>
            </Card>

            <Card style={styles.statCard}>
              <Card.Content style={styles.statCardContent}>
                <Text variant="displaySmall" style={styles.statNumber}>
                  {myOrders.filter(o => o.priority === 'urgent' || o.priority === 'express').length}
                </Text>
                <Text variant="bodyMedium">Urgent</Text>
              </Card.Content>
            </Card>
          </View>

          <View style={styles.ordersSection}>
            <Text variant="titleLarge" style={styles.sectionTitle}>
              My Assigned Orders
            </Text>

            {myOrders.length === 0 ? (
              <Card style={styles.emptyCard}>
                <Card.Content style={styles.emptyCardContent}>
                  <Text variant="headlineSmall">No Tasks Assigned</Text>
                  <Text variant="bodyMedium">Check back later for new orders</Text>
                </Card.Content>
              </Card>
            ) : (
              myOrders.map((order) => (
                <Card
                  key={order.id}
                  style={styles.orderCard}
                  onPress={() => openBottomSheet('order', order)}
                >
                  <Card.Content>
                    <View style={styles.orderHeader}>
                      <Text variant="titleMedium">#{order.id}</Text>
                      <Chip mode="flat" style={{ backgroundColor: getPriorityColor(order.priority) + '20' }}>
                        {order.priority.toUpperCase()}
                      </Chip>
                    </View>
                    <Text variant="bodyLarge" style={styles.customerName}>
                      {order.customerName}
                    </Text>
                    <Text variant="bodyMedium">
                      {order.garmentType}
                    </Text>
                    <Text variant="bodySmall">
                      Due: {order.dueDate}
                    </Text>
                    <View style={styles.statusContainer}>
                      <Chip mode="flat" style={{ backgroundColor: getStatusColor(order.status) + '20' }}>
                        {order.status.replace('_', ' ').toUpperCase()}
                      </Chip>
                      <Button
                        mode="contained"
                        compact
                        onPress={() => updateOrderStatus(order.id, 'next_status')}
                      >
                        Update
                      </Button>
                    </View>
                  </Card.Content>
                </Card>
              ))
            )}
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="account-group"
              onPress={() => handleNavigation('customers')}
              style={styles.navButton}
            >
              Customers
            </Button>
            <Button
              mode="text"
              icon="hanger"
              onPress={() => handleNavigation('products')}
              style={styles.navButton}
            >
              Products
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Customers Screen
  if (currentScreen === 'customers') {
    const customers = getCustomers();

    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content title="Customers" />
          <Appbar.Action
            icon="plus"
            onPress={() => Alert.alert('Add Customer', 'Add new customer functionality coming soon!')}
          />
        </Appbar.Header>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <TextInput
            mode="outlined"
            label="Search customers..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.searchInput}
            left={<TextInput.Icon icon="magnify" />}
            right={searchQuery ? <TextInput.Icon icon="close" onPress={() => setSearchQuery('')} /> : null}
          />
        </View>

        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        >
          <View style={styles.ordersSection}>
            {customers.length === 0 ? (
              <Card style={styles.emptyCard}>
                <Card.Content style={styles.emptyCardContent}>
                  <Text variant="headlineSmall">No Customers Found</Text>
                  <Text variant="bodyMedium">Try adjusting your search</Text>
                </Card.Content>
              </Card>
            ) : (
              customers.map((customer) => (
                <Card
                  key={customer.id}
                  style={styles.orderCard}
                  onPress={() => openBottomSheet('customer', customer)}
                >
                  <Card.Content>
                    <View style={styles.orderHeader}>
                      <View style={styles.customerHeader}>
                        <Avatar.Text
                          size={40}
                          label={customer.name.charAt(0)}
                          style={{ backgroundColor: theme.colors.primaryContainer }}
                        />
                        <View style={styles.customerInfo}>
                          <Text variant="titleMedium">{customer.name}</Text>
                          <Text variant="bodySmall">{customer.phone}</Text>
                        </View>
                      </View>
                      <Chip mode="flat">
                        {customer.totalOrders} orders
                      </Chip>
                    </View>
                    <Text variant="bodyMedium" style={styles.customerAddress}>
                      {customer.address}
                    </Text>
                    <Text variant="bodySmall">
                      Member since: {new Date(customer.joinDate).toLocaleDateString()}
                    </Text>
                  </Card.Content>
                </Card>
              ))
            )}
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="account-group"
              onPress={() => handleNavigation('customers')}
              style={[styles.navButton, currentScreen === 'customers' && styles.activeNavButton]}
            >
              Customers
            </Button>
            <Button
              mode="text"
              icon="hanger"
              onPress={() => handleNavigation('products')}
              style={styles.navButton}
            >
              Products
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Products Screen
  if (currentScreen === 'products') {
    const products = getProducts();

    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content title="Products" />
          <Appbar.Action
            icon="plus"
            onPress={() => Alert.alert('Add Product', 'Add new product functionality coming soon!')}
          />
        </Appbar.Header>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <TextInput
            mode="outlined"
            label="Search products..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.searchInput}
            left={<TextInput.Icon icon="magnify" />}
            right={searchQuery ? <TextInput.Icon icon="close" onPress={() => setSearchQuery('')} /> : null}
          />
        </View>

        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        >
          <View style={styles.ordersSection}>
            {products.length === 0 ? (
              <Card style={styles.emptyCard}>
                <Card.Content style={styles.emptyCardContent}>
                  <Text variant="headlineSmall">No Products Found</Text>
                  <Text variant="bodyMedium">Try adjusting your search</Text>
                </Card.Content>
              </Card>
            ) : (
              products.map((product) => (
                <Card
                  key={product.id}
                  style={styles.orderCard}
                  onPress={() => Alert.alert('Product Details', `${product.name}\n${product.description}\nPrice: ৳${product.price}`)}
                >
                  <Card.Content>
                    <View style={styles.orderHeader}>
                      <View style={styles.productHeader}>
                        <Avatar.Icon
                          size={40}
                          icon="hanger"
                          style={{ backgroundColor: theme.colors.tertiaryContainer }}
                        />
                        <View style={styles.productInfo}>
                          <Text variant="titleMedium">{product.name}</Text>
                          <Text variant="bodySmall">{product.category}</Text>
                        </View>
                      </View>
                      <View style={styles.productPrice}>
                        <Text variant="titleMedium" style={styles.priceText}>৳{product.price}</Text>
                        <Chip mode="flat" style={{ backgroundColor: product.inStock ? '#4CAF50' : '#F44336' }}>
                          {product.inStock ? 'In Stock' : 'Out of Stock'}
                        </Chip>
                      </View>
                    </View>
                    <Text variant="bodyMedium" style={styles.productDescription}>
                      {product.description}
                    </Text>
                    <Text variant="bodySmall">
                      Fabric: {product.fabric} • Colors: {product.colors.join(', ')}
                    </Text>
                  </Card.Content>
                </Card>
              ))
            )}
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="account-group"
              onPress={() => handleNavigation('customers')}
              style={styles.navButton}
            >
              Customers
            </Button>
            <Button
              mode="text"
              icon="hanger"
              onPress={() => handleNavigation('products')}
              style={[styles.navButton, currentScreen === 'products' && styles.activeNavButton]}
            >
              Products
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Scan Screen
  if (currentScreen === 'scan') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content title="QR Scanner" />
          <Appbar.Action
            icon="flashlight"
            onPress={() => Alert.alert('Flash', 'Flashlight toggle coming soon!')}
          />
        </Appbar.Header>

        <ScrollView style={styles.content}>
          <View style={styles.scanContainer}>
            <Card style={styles.scanCard}>
              <Card.Content style={styles.scanCardContent}>
                <Avatar.Icon
                  size={80}
                  icon="qrcode-scan"
                  style={{ backgroundColor: theme.colors.primaryContainer }}
                />
                <Text variant="headlineSmall" style={styles.scanTitle}>
                  QR Code Scanner
                </Text>
                <Text variant="bodyMedium" style={styles.scanSubtitle}>
                  Point camera at order QR code to quickly access order details
                </Text>

                <Button
                  mode="contained"
                  onPress={() => {
                    // Simulate scanning an order
                    const randomOrder = mockOrders[Math.floor(Math.random() * mockOrders.length)];
                    openBottomSheet('order', randomOrder);
                  }}
                  style={styles.scanButton}
                  icon="camera"
                >
                  Start Scanning
                </Button>

                <Button
                  mode="outlined"
                  onPress={() => {
                    Alert.alert('Demo Scan', 'Simulating QR scan...', [
                      { text: 'Cancel' },
                      {
                        text: 'Scan Order',
                        onPress: () => {
                          const randomOrder = mockOrders[Math.floor(Math.random() * mockOrders.length)];
                          openBottomSheet('order', randomOrder);
                        }
                      }
                    ]);
                  }}
                  style={styles.demoScanButton}
                  icon="play"
                >
                  Demo Scan
                </Button>
              </Card.Content>
            </Card>

            {/* Recent Scans */}
            <View style={styles.recentScansContainer}>
              <Text variant="titleLarge" style={styles.sectionTitle}>
                Recent Scans
              </Text>

              {mockOrders.slice(0, 3).map((order) => (
                <Card
                  key={order.id}
                  style={styles.recentScanCard}
                  onPress={() => openBottomSheet('order', order)}
                >
                  <Card.Content>
                    <View style={styles.recentScanHeader}>
                      <Text variant="titleMedium">#{order.id}</Text>
                      <Text variant="bodySmall">{order.orderDate}</Text>
                    </View>
                    <Text variant="bodyMedium">{order.customerName}</Text>
                    <Text variant="bodySmall">{order.garmentType}</Text>
                  </Card.Content>
                </Card>
              ))}
            </View>
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="qrcode-scan"
              onPress={() => handleNavigation('scan')}
              style={[styles.navButton, currentScreen === 'scan' && styles.activeNavButton]}
            >
              Scan
            </Button>
            <Button
              mode="text"
              icon="plus-circle"
              onPress={() => handleNavigation('quickActions')}
              style={[styles.navButton, styles.plusButton]}
            >
              Add
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="cog"
              onPress={() => handleNavigation('settings')}
              style={styles.navButton}
            >
              Settings
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Quick Actions Screen
  if (currentScreen === 'quickActions') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content title="Quick Actions" />
        </Appbar.Header>

        <ScrollView style={styles.content}>
          <View style={styles.quickActionsContainer}>
            <Text variant="titleLarge" style={styles.sectionTitle}>
              Create New
            </Text>

            <View style={styles.quickActionsGrid}>
              <Card style={styles.quickActionCard} onPress={() => handleNavigation('addOrder')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="plus" style={{ backgroundColor: theme.colors.primaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>New Order</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>Create tailoring order</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('addCustomer')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="account-plus" style={{ backgroundColor: theme.colors.secondaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Add Customer</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>New customer profile</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('addProduct')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="hanger" style={{ backgroundColor: theme.colors.tertiaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Add Product</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>New garment type</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('addEmployee')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="account-tie" style={{ backgroundColor: theme.colors.errorContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Add Employee</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>New staff member</Text>
                </Card.Content>
              </Card>
            </View>

            <Text variant="titleLarge" style={[styles.sectionTitle, { marginTop: 24 }]}>
              Management
            </Text>

            <View style={styles.quickActionsGrid}>
              <Card style={styles.quickActionCard} onPress={() => handleNavigation('employees')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="account-group" style={{ backgroundColor: theme.colors.primaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Employees</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>Manage staff</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('measurements')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="ruler" style={{ backgroundColor: theme.colors.secondaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Measurements</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>Size management</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('reports')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="chart-line" style={{ backgroundColor: theme.colors.tertiaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Reports</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>Business insights</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('search')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="magnify" style={{ backgroundColor: theme.colors.errorContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Search</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>Find anything</Text>
                </Card.Content>
              </Card>
            </View>
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="qrcode-scan"
              onPress={() => handleNavigation('scan')}
              style={styles.navButton}
            >
              Scan
            </Button>
            <Button
              mode="text"
              icon="plus-circle"
              onPress={() => handleNavigation('quickActions')}
              style={[styles.navButton, styles.plusButton, currentScreen === 'quickActions' && styles.activeNavButton]}
            >
              Add
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="cog"
              onPress={() => handleNavigation('settings')}
              style={styles.navButton}
            >
              Settings
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Comprehensive Settings Screen
  if (currentScreen === 'settings') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content title="Settings" />
        </Appbar.Header>

        <ScrollView style={styles.content}>
          <View style={styles.settingsContainer}>

            {/* App Preferences */}
            <View style={styles.settingsSection}>
              <Text variant="titleMedium" style={styles.settingsSectionTitle}>
                App Preferences
              </Text>

              <Card style={styles.settingsCard}>
                <Card.Content>
                  <View style={styles.settingItem}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="theme-light-dark" style={{ backgroundColor: theme.colors.primaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Dark Mode</Text>
                        <Text variant="bodySmall">Switch between light and dark themes</Text>
                      </View>
                    </View>
                    <Switch
                      value={isDarkMode}
                      onValueChange={setIsDarkMode}
                    />
                  </View>
                </Card.Content>
              </Card>
            </View>

            {/* Business Management */}
            <View style={styles.settingsSection}>
              <Text variant="titleMedium" style={styles.settingsSectionTitle}>
                Business Management
              </Text>

              <Card style={styles.settingsCard}>
                <Card.Content>
                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('customers')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="account-group" style={{ backgroundColor: theme.colors.secondaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Customers</Text>
                        <Text variant="bodySmall">Manage customer information</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('employees')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="account-tie" style={{ backgroundColor: theme.colors.tertiaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Staff Management</Text>
                        <Text variant="bodySmall">Manage employees and assignments</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('products')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="hanger" style={{ backgroundColor: theme.colors.errorContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Products</Text>
                        <Text variant="bodySmall">Manage garment catalog</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>
                </Card.Content>
              </Card>
            </View>

            {/* Account & Profile */}
            <View style={styles.settingsSection}>
              <Text variant="titleMedium" style={styles.settingsSectionTitle}>
                Account & Profile
              </Text>

              <Card style={styles.settingsCard}>
                <Card.Content>
                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('profile')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Text size={40} label={employee?.avatar || 'U'} style={{ backgroundColor: theme.colors.primaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">My Profile</Text>
                        <Text variant="bodySmall">View and edit your profile</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('editProfile')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="account-edit" style={{ backgroundColor: theme.colors.secondaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Edit Profile</Text>
                        <Text variant="bodySmall">Update your information</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('security')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="shield-lock" style={{ backgroundColor: theme.colors.tertiaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Security Settings</Text>
                        <Text variant="bodySmall">Password and security options</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>
                </Card.Content>
              </Card>
            </View>

            {/* Data & Reports */}
            <View style={styles.settingsSection}>
              <Text variant="titleMedium" style={styles.settingsSectionTitle}>
                Data & Reports
              </Text>

              <Card style={styles.settingsCard}>
                <Card.Content>
                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('reports')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="chart-line" style={{ backgroundColor: theme.colors.primaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Reports</Text>
                        <Text variant="bodySmall">Business analytics and insights</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('importData')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="database-import" style={{ backgroundColor: theme.colors.secondaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Data Backup</Text>
                        <Text variant="bodySmall">Import and export data</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('activityLog')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="history" style={{ backgroundColor: theme.colors.tertiaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Activity Log</Text>
                        <Text variant="bodySmall">View system activity</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>
                </Card.Content>
              </Card>
            </View>

            {/* Support */}
            <View style={styles.settingsSection}>
              <Text variant="titleMedium" style={styles.settingsSectionTitle}>
                Support
              </Text>

              <Card style={styles.settingsCard}>
                <Card.Content>
                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('help')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="help-circle" style={{ backgroundColor: theme.colors.primaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Help & FAQ</Text>
                        <Text variant="bodySmall">Get help and find answers</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('contact')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="email" style={{ backgroundColor: theme.colors.secondaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Contact Support</Text>
                        <Text variant="bodySmall">Get in touch with our team</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('about')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="information" style={{ backgroundColor: theme.colors.tertiaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">About</Text>
                        <Text variant="bodySmall">App version and information</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>
                </Card.Content>
              </Card>
            </View>

            {/* Logout */}
            <View style={styles.settingsSection}>
              <Card style={styles.settingsCard}>
                <Card.Content>
                  <TouchableOpacity style={styles.settingItem} onPress={handleLogout}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="logout" style={{ backgroundColor: theme.colors.errorContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium" style={{ color: theme.colors.error }}>Logout</Text>
                        <Text variant="bodySmall">Sign out of your account</Text>
                      </View>
                    </View>
                  </TouchableOpacity>
                </Card.Content>
              </Card>
            </View>

            {/* Footer */}
            <View style={styles.settingsFooter}>
              <Text variant="bodySmall" style={styles.footerText}>
                Elite Tailor Employee App v1.0.0
              </Text>
              <Text variant="bodySmall" style={styles.footerText}>
                Made with ❤️ for tailor shop employees
              </Text>
            </View>
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="qrcode-scan"
              onPress={() => handleNavigation('scan')}
              style={styles.navButton}
            >
              Scan
            </Button>
            <Button
              mode="text"
              icon="plus-circle"
              onPress={() => handleNavigation('quickActions')}
              style={[styles.navButton, styles.plusButton]}
            >
              Add
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="cog"
              onPress={() => handleNavigation('settings')}
              style={[styles.navButton, currentScreen === 'settings' && styles.activeNavButton]}
            >
              Settings
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Scan Screen
  if (currentScreen === 'scan') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content title="QR Scanner" />
          <Appbar.Action
            icon="flashlight"
            onPress={() => Alert.alert('Flash', 'Flashlight toggle coming soon!')}
          />
        </Appbar.Header>

        <ScrollView style={styles.content}>
          <View style={styles.scanContainer}>
            <Card style={styles.scanCard}>
              <Card.Content style={styles.scanCardContent}>
                <Avatar.Icon
                  size={80}
                  icon="qrcode-scan"
                  style={{ backgroundColor: theme.colors.primaryContainer }}
                />
                <Text variant="headlineSmall" style={styles.scanTitle}>
                  QR Code Scanner
                </Text>
                <Text variant="bodyMedium" style={styles.scanSubtitle}>
                  Point camera at order QR code to quickly access order details
                </Text>

                <Button
                  mode="contained"
                  onPress={() => {
                    // Simulate scanning an order
                    const randomOrder = mockOrders[Math.floor(Math.random() * mockOrders.length)];
                    openBottomSheet('order', randomOrder);
                  }}
                  style={styles.scanButton}
                  icon="camera"
                >
                  Start Scanning
                </Button>

                <Button
                  mode="outlined"
                  onPress={() => {
                    Alert.alert('Demo Scan', 'Simulating QR scan...', [
                      { text: 'Cancel' },
                      {
                        text: 'Scan Order',
                        onPress: () => {
                          const randomOrder = mockOrders[Math.floor(Math.random() * mockOrders.length)];
                          openBottomSheet('order', randomOrder);
                        }
                      }
                    ]);
                  }}
                  style={styles.demoScanButton}
                  icon="play"
                >
                  Demo Scan
                </Button>
              </Card.Content>
            </Card>

            {/* Recent Scans */}
            <View style={styles.recentScansContainer}>
              <Text variant="titleLarge" style={styles.sectionTitle}>
                Recent Scans
              </Text>

              {mockOrders.slice(0, 3).map((order) => (
                <Card
                  key={order.id}
                  style={styles.recentScanCard}
                  onPress={() => openBottomSheet('order', order)}
                >
                  <Card.Content>
                    <View style={styles.recentScanHeader}>
                      <Text variant="titleMedium">#{order.id}</Text>
                      <Text variant="bodySmall">{order.orderDate}</Text>
                    </View>
                    <Text variant="bodyMedium">{order.customerName}</Text>
                    <Text variant="bodySmall">{order.garmentType}</Text>
                  </Card.Content>
                </Card>
              ))}
            </View>
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="qrcode-scan"
              onPress={() => handleNavigation('scan')}
              style={[styles.navButton, currentScreen === 'scan' && styles.activeNavButton]}
            >
              Scan
            </Button>
            <Button
              mode="text"
              icon="plus-circle"
              onPress={() => handleNavigation('quickActions')}
              style={[styles.navButton, styles.plusButton]}
            >
              Add
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="cog"
              onPress={() => handleNavigation('settings')}
              style={styles.navButton}
            >
              Settings
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Quick Actions Screen
  if (currentScreen === 'quickActions') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content title="Quick Actions" />
        </Appbar.Header>

        <ScrollView style={styles.content}>
          <View style={styles.quickActionsContainer}>
            <Text variant="titleLarge" style={styles.sectionTitle}>
              Create New
            </Text>

            <View style={styles.quickActionsGrid}>
              <Card style={styles.quickActionCard} onPress={() => handleNavigation('addOrder')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="plus" style={{ backgroundColor: theme.colors.primaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>New Order</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>Create tailoring order</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('addCustomer')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="account-plus" style={{ backgroundColor: theme.colors.secondaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Add Customer</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>New customer profile</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('addProduct')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="hanger" style={{ backgroundColor: theme.colors.tertiaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Add Product</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>New garment type</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('addEmployee')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="account-tie" style={{ backgroundColor: theme.colors.errorContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Add Employee</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>New staff member</Text>
                </Card.Content>
              </Card>
            </View>

            <Text variant="titleLarge" style={[styles.sectionTitle, { marginTop: 24 }]}>
              Management
            </Text>

            <View style={styles.quickActionsGrid}>
              <Card style={styles.quickActionCard} onPress={() => handleNavigation('employees')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="account-group" style={{ backgroundColor: theme.colors.primaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Employees</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>Manage staff</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('reports')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="chart-line" style={{ backgroundColor: theme.colors.secondaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Reports</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>Business insights</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('importData')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="database-import" style={{ backgroundColor: theme.colors.tertiaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Data Backup</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>Import/Export</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('help')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="help-circle" style={{ backgroundColor: theme.colors.errorContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Help</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>FAQ & Support</Text>
                </Card.Content>
              </Card>
            </View>
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="qrcode-scan"
              onPress={() => handleNavigation('scan')}
              style={styles.navButton}
            >
              Scan
            </Button>
            <Button
              mode="text"
              icon="plus-circle"
              onPress={() => handleNavigation('quickActions')}
              style={[styles.navButton, styles.plusButton, currentScreen === 'quickActions' && styles.activeNavButton]}
            >
              Add
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="cog"
              onPress={() => handleNavigation('settings')}
              style={styles.navButton}
            >
              Settings
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Add Order Screen
  if (currentScreen === 'addOrder') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.BackAction onPress={() => handleNavigation('quickActions')} />
          <Appbar.Content title="New Order" />
          <Appbar.Action icon="check" onPress={() => {
            Alert.alert('Success', 'Order created successfully!', [
              { text: 'OK', onPress: () => handleNavigation('orders') }
            ]);
          }} />
        </Appbar.Header>

        <ScrollView style={styles.content}>
          <View style={styles.formContainer}>

            {/* Customer Selection */}
            <Card style={styles.formCard}>
              <Card.Content>
                <Text variant="titleMedium" style={styles.formSectionTitle}>Customer Information</Text>
                <TextInput
                  mode="outlined"
                  label="Customer Name"
                  placeholder="Select or enter customer name"
                  style={styles.formInput}
                  right={<TextInput.Icon icon="account-plus" onPress={() => handleNavigation('addCustomer')} />}
                />
                <TextInput
                  mode="outlined"
                  label="Phone Number"
                  placeholder="Customer phone number"
                  style={styles.formInput}
                  keyboardType="phone-pad"
                />
              </Card.Content>
            </Card>

            {/* Garment Details */}
            <Card style={styles.formCard}>
              <Card.Content>
                <Text variant="titleMedium" style={styles.formSectionTitle}>Garment Details</Text>
                <TextInput
                  mode="outlined"
                  label="Garment Type"
                  placeholder="e.g., Shirt, Pants, Suit"
                  style={styles.formInput}
                />
                <TextInput
                  mode="outlined"
                  label="Fabric"
                  placeholder="e.g., Cotton, Silk, Wool"
                  style={styles.formInput}
                />
                <TextInput
                  mode="outlined"
                  label="Color"
                  placeholder="Fabric color"
                  style={styles.formInput}
                />
                <View style={styles.formRow}>
                  <TextInput
                    mode="outlined"
                    label="Quantity"
                    placeholder="1"
                    style={[styles.formInput, { flex: 1, marginRight: 8 }]}
                    keyboardType="numeric"
                  />
                  <TextInput
                    mode="outlined"
                    label="Size"
                    placeholder="e.g., M, L, XL"
                    style={[styles.formInput, { flex: 1, marginLeft: 8 }]}
                  />
                </View>
              </Card.Content>
            </Card>

            {/* Measurements */}
            <Card style={styles.formCard}>
              <Card.Content>
                <Text variant="titleMedium" style={styles.formSectionTitle}>Measurements</Text>
                <View style={styles.formRow}>
                  <TextInput
                    mode="outlined"
                    label="Chest/Bust"
                    placeholder="inches"
                    style={[styles.formInput, { flex: 1, marginRight: 8 }]}
                    keyboardType="numeric"
                  />
                  <TextInput
                    mode="outlined"
                    label="Waist"
                    placeholder="inches"
                    style={[styles.formInput, { flex: 1, marginLeft: 8 }]}
                  />
                </View>
                <View style={styles.formRow}>
                  <TextInput
                    mode="outlined"
                    label="Length"
                    placeholder="inches"
                    style={[styles.formInput, { flex: 1, marginRight: 8 }]}
                    keyboardType="numeric"
                  />
                  <TextInput
                    mode="outlined"
                    label="Sleeve"
                    placeholder="inches"
                    style={[styles.formInput, { flex: 1, marginLeft: 8 }]}
                  />
                </View>
                <Button
                  mode="outlined"
                  onPress={() => Alert.alert('Measurements', 'Full measurement form coming soon!')}
                  style={styles.measurementButton}
                  icon="ruler"
                >
                  Full Measurements
                </Button>
              </Card.Content>
            </Card>

            {/* Pricing & Timeline */}
            <Card style={styles.formCard}>
              <Card.Content>
                <Text variant="titleMedium" style={styles.formSectionTitle}>Pricing & Timeline</Text>
                <View style={styles.formRow}>
                  <TextInput
                    mode="outlined"
                    label="Estimated Price"
                    placeholder="৳0"
                    style={[styles.formInput, { flex: 1, marginRight: 8 }]}
                    keyboardType="numeric"
                  />
                  <TextInput
                    mode="outlined"
                    label="Advance Payment"
                    placeholder="৳0"
                    style={[styles.formInput, { flex: 1, marginLeft: 8 }]}
                    keyboardType="numeric"
                  />
                </View>
                <TextInput
                  mode="outlined"
                  label="Due Date"
                  placeholder="Select delivery date"
                  style={styles.formInput}
                  right={<TextInput.Icon icon="calendar" />}
                />
                <View style={styles.priorityContainer}>
                  <Text variant="bodyMedium" style={styles.priorityLabel}>Priority:</Text>
                  <View style={styles.priorityChips}>
                    <Chip mode="outlined" selected style={styles.priorityChip}>Normal</Chip>
                    <Chip mode="outlined" style={styles.priorityChip}>Urgent</Chip>
                    <Chip mode="outlined" style={styles.priorityChip}>Express</Chip>
                  </View>
                </View>
              </Card.Content>
            </Card>

            {/* Notes */}
            <Card style={styles.formCard}>
              <Card.Content>
                <Text variant="titleMedium" style={styles.formSectionTitle}>Additional Notes</Text>
                <TextInput
                  mode="outlined"
                  label="Special Instructions"
                  placeholder="Any special requirements or notes..."
                  style={styles.formInput}
                  multiline
                  numberOfLines={3}
                />
              </Card.Content>
            </Card>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <Button
                mode="outlined"
                onPress={() => handleNavigation('quickActions')}
                style={[styles.actionButton, { marginRight: 8 }]}
              >
                Cancel
              </Button>
              <Button
                mode="contained"
                onPress={() => {
                  Alert.alert('Success', 'Order created successfully!', [
                    { text: 'OK', onPress: () => handleNavigation('orders') }
                  ]);
                }}
                style={[styles.actionButton, { marginLeft: 8 }]}
                icon="check"
              >
                Create Order
              </Button>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }

  return (
    <View style={{ flex: 1 }}>
      {/* Simple Bottom Sheet Modal */}
      {showBottomSheet && (
        <View style={styles.bottomSheetOverlay}>
          <View style={styles.bottomSheetBackdrop} onTouchEnd={closeBottomSheet} />
          <View style={[styles.bottomSheetContainer, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.bottomSheetHandle} />

            {bottomSheetType === 'order' && selectedOrder && (
              <ScrollView style={styles.bottomSheetContent}>
                <Text variant="headlineSmall" style={styles.bottomSheetTitle}>
                  Order Details
                </Text>

                <Card style={styles.detailCard}>
                  <Card.Content>
                    <Text variant="titleMedium">Order #{selectedOrder.id}</Text>
                    <Text variant="bodyLarge">{selectedOrder.customerName}</Text>
                    <Text variant="bodyMedium">{selectedOrder.garmentType}</Text>
                    <Text variant="bodySmall">Due: {selectedOrder.dueDate}</Text>
                    <Text variant="bodySmall">Price: ৳{selectedOrder.estimatedPrice}</Text>
                    <Text variant="bodySmall">Advance: ৳{selectedOrder.advancePayment}</Text>
                    <Text variant="bodySmall">Status: {selectedOrder.status}</Text>
                    <Text variant="bodySmall">Priority: {selectedOrder.priority}</Text>
                    <Text variant="bodySmall">Assigned to: {selectedOrder.assignedTo}</Text>
                    {selectedOrder.notes && (
                      <Text variant="bodySmall" style={{ marginTop: 8 }}>
                        Notes: {selectedOrder.notes}
                      </Text>
                    )}
                  </Card.Content>
                </Card>

                <Button
                  mode="contained"
                  onPress={() => {
                    updateOrderStatus(selectedOrder.id, 'updated');
                    closeBottomSheet();
                  }}
                  style={styles.bottomSheetButton}
                >
                  Update Status
                </Button>
              </ScrollView>
            )}

            {bottomSheetType === 'customer' && selectedCustomer && (
              <ScrollView style={styles.bottomSheetContent}>
                <Text variant="headlineSmall" style={styles.bottomSheetTitle}>
                  Customer Details
                </Text>

                <Card style={styles.detailCard}>
                  <Card.Content>
                    <Text variant="titleMedium">{selectedCustomer.name}</Text>
                    <Text variant="bodyMedium">{selectedCustomer.phone}</Text>
                    <Text variant="bodyMedium">{selectedCustomer.email}</Text>
                    <Text variant="bodySmall">{selectedCustomer.address}</Text>
                    <Text variant="bodySmall">Total Orders: {selectedCustomer.totalOrders}</Text>
                    <Text variant="bodySmall">Member since: {new Date(selectedCustomer.joinDate).toLocaleDateString()}</Text>
                  </Card.Content>
                </Card>

                <Button
                  mode="contained"
                  onPress={() => {
                    Alert.alert('Contact', `Calling ${selectedCustomer.name}...`);
                    closeBottomSheet();
                  }}
                  style={styles.bottomSheetButton}
                >
                  Call Customer
                </Button>
              </ScrollView>
            )}
          </View>
        </View>
      )}
    </View>
  );
};

// Main App Component with Theme Provider
export default function App() {
  const [isDarkMode, setIsDarkMode] = useState(false);

  const theme = isDarkMode ? MD3DarkTheme : MD3LightTheme;

  return (
    <SafeAreaProvider>
      <PaperProvider theme={theme}>
        <AppContent />
      </PaperProvider>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  // Login Screen Styles
  loginContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 24,
  },
  loginHeader: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    marginTop: 16,
    textAlign: 'center',
    color: 'white',
  },
  subtitle: {
    marginTop: 8,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  loginCard: {
    borderRadius: 28,
    marginHorizontal: 8,
  },
  loginTitle: {
    textAlign: 'center',
    marginBottom: 24,
  },
  textInput: {
    marginBottom: 16,
  },
  loginButton: {
    marginTop: 8,
    marginBottom: 16,
    borderRadius: 20,
  },
  demoText: {
    textAlign: 'center',
    opacity: 0.7,
  },

  // Tasks Screen Styles
  content: {
    flex: 1,
  },
  statsContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  statCard: {
    flex: 1,
    borderRadius: 16,
  },
  statCardContent: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  statNumber: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  ordersSection: {
    paddingHorizontal: 16,
    paddingBottom: 100,
  },
  sectionTitle: {
    marginBottom: 16,
    fontWeight: '600',
  },
  emptyCard: {
    borderRadius: 16,
    marginTop: 32,
  },
  emptyCardContent: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  orderCard: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  customerName: {
    fontWeight: '600',
    marginBottom: 4,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  bottomNav: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  navContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  activeNavButton: {
    backgroundColor: 'rgba(0,0,0,0.1)',
  },

  // Search Container
  searchContainer: {
    padding: 16,
  },
  searchInput: {
    marginBottom: 0,
  },

  // Quick Actions
  quickActionsContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  quickActionCard: {
    flex: 1,
    minWidth: '45%',
    borderRadius: 16,
  },
  quickActionContent: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  quickActionTitle: {
    marginTop: 8,
    textAlign: 'center',
    fontSize: 12,
  },

  // Recent Orders
  recentOrdersContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },

  // Customer Styles
  customerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  customerInfo: {
    marginLeft: 12,
    flex: 1,
  },
  customerAddress: {
    marginTop: 8,
    opacity: 0.8,
  },

  // Product Styles
  productHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  productInfo: {
    marginLeft: 12,
    flex: 1,
  },
  productPrice: {
    alignItems: 'flex-end',
  },
  productDescription: {
    marginTop: 8,
    opacity: 0.8,
  },
  priceText: {
    fontWeight: '600',
    marginBottom: 4,
  },

  // Bottom Sheet Styles
  bottomSheetOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'flex-end',
    zIndex: 1000,
  },
  bottomSheetBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  bottomSheetContainer: {
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    minHeight: '50%',
    maxHeight: '80%',
    paddingBottom: 20,
  },
  bottomSheetHandle: {
    width: 32,
    height: 4,
    backgroundColor: 'rgba(0,0,0,0.3)',
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 12,
    marginBottom: 16,
  },
  bottomSheetContent: {
    flex: 1,
    paddingHorizontal: 24,
  },
  bottomSheetTitle: {
    marginBottom: 16,
    fontWeight: '600',
  },
  detailCard: {
    marginBottom: 16,
    borderRadius: 16,
  },
  bottomSheetButton: {
    marginTop: 16,
    borderRadius: 20,
  },

  // Plus Button Style
  plusButton: {
    backgroundColor: 'rgba(0,0,0,0.05)',
  },

  // Scan Screen Styles
  scanContainer: {
    padding: 16,
  },
  scanCard: {
    borderRadius: 16,
    marginBottom: 24,
  },
  scanCardContent: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  scanTitle: {
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  scanSubtitle: {
    textAlign: 'center',
    marginBottom: 24,
    opacity: 0.7,
  },
  scanButton: {
    marginBottom: 12,
    borderRadius: 20,
    paddingHorizontal: 32,
  },
  demoScanButton: {
    borderRadius: 20,
    paddingHorizontal: 32,
  },
  recentScansContainer: {
    marginTop: 16,
  },
  recentScanCard: {
    marginBottom: 8,
    borderRadius: 12,
  },
  recentScanHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },

  // Quick Action Subtitle
  quickActionSubtitle: {
    textAlign: 'center',
    opacity: 0.7,
    marginTop: 4,
  },

  // Settings Screen Styles
  settingsContainer: {
    padding: 16,
  },
  settingsSection: {
    marginBottom: 24,
  },
  settingsSectionTitle: {
    marginBottom: 12,
    fontWeight: '600',
  },
  settingsCard: {
    borderRadius: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingItemText: {
    marginLeft: 16,
    flex: 1,
  },
  settingsFooter: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  footerText: {
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: 4,
  },

  // Plus Button Style
  plusButton: {
    backgroundColor: 'rgba(0,0,0,0.05)',
  },

  // Scan Screen Styles
  scanContainer: {
    padding: 16,
  },
  scanCard: {
    borderRadius: 16,
    marginBottom: 24,
  },
  scanCardContent: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  scanTitle: {
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  scanSubtitle: {
    textAlign: 'center',
    marginBottom: 24,
    opacity: 0.7,
  },
  scanButton: {
    marginBottom: 12,
    borderRadius: 20,
    paddingHorizontal: 32,
  },
  demoScanButton: {
    borderRadius: 20,
    paddingHorizontal: 32,
  },
  recentScansContainer: {
    marginTop: 16,
  },
  recentScanCard: {
    marginBottom: 8,
    borderRadius: 12,
  },
  recentScanHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },

  // Quick Action Subtitle
  quickActionSubtitle: {
    textAlign: 'center',
    opacity: 0.7,
    marginTop: 4,
  },

  // Form Styles
  formContainer: {
    padding: 16,
    paddingBottom: 100,
  },
  formCard: {
    marginBottom: 16,
    borderRadius: 16,
  },
  formSectionTitle: {
    marginBottom: 16,
    fontWeight: '600',
  },
  formInput: {
    marginBottom: 12,
  },
  formRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  measurementButton: {
    marginTop: 8,
    borderRadius: 20,
  },
  priorityContainer: {
    marginTop: 12,
  },
  priorityLabel: {
    marginBottom: 8,
    fontWeight: '500',
  },
  priorityChips: {
    flexDirection: 'row',
    gap: 8,
  },
  priorityChip: {
    marginRight: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    marginTop: 24,
    marginBottom: 32,
  },
  actionButton: {
    flex: 1,
    borderRadius: 20,
  },
});
