import React, { useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  FlatList,
  Alert,
  SafeAreaView
} from 'react-native';

// Simple Employee App - Completely separate from Manager App
export default function App() {
  const [currentScreen, setCurrentScreen] = useState('login');
  const [employee, setEmployee] = useState(null);
  const [employeeId, setEmployeeId] = useState('');
  const [pin, setPin] = useState('');

  // Mock employee data
  const employees = {
    'EMP001': { id: 'EMP001', name: '<PERSON>', role: 'Senior Tailor', pin: '1234' },
    'EMP002': { id: 'EMP002', name: '<PERSON><PERSON>', role: 'Junior <PERSON>lor', pin: '1234' },
    'EMP003': { id: 'EMP003', name: '<PERSON>', role: 'Cutter', pin: '1234' },
  };

  // Mock orders data
  const mockOrders = [
    {
      id: 'ORD001',
      customerName: '<PERSON>',
      garmentType: 'Suit',
      status: 'cutting',
      priority: 'urgent',
      dueDate: '2025-05-30',
      assignedTo: 'EMP001'
    },
    {
      id: 'ORD002',
      customerName: 'Fatima Khan',
      garmentType: 'Dress',
      status: 'stitching',
      priority: 'normal',
      dueDate: '2025-06-02',
      assignedTo: 'EMP002'
    },
    {
      id: 'ORD003',
      customerName: 'Mohammad Ali',
      garmentType: 'Shirt',
      status: 'fitting',
      priority: 'express',
      dueDate: '2025-05-28',
      assignedTo: 'EMP001'
    },
  ];

  const handleLogin = () => {
    const emp = employees[employeeId];
    if (emp && emp.pin === pin) {
      setEmployee(emp);
      setCurrentScreen('tasks');
    } else {
      Alert.alert('Error', 'Invalid Employee ID or PIN');
    }
  };

  const handleLogout = () => {
    setEmployee(null);
    setEmployeeId('');
    setPin('');
    setCurrentScreen('login');
  };

  const getMyOrders = () => {
    return mockOrders.filter(order => order.assignedTo === employee?.id);
  };

  const updateOrderStatus = (orderId, newStatus) => {
    Alert.alert('Success', `Order ${orderId} updated to ${newStatus}`);
  };

  const getStatusColor = (status) => {
    const colors = {
      'cutting': '#9C27B0',
      'stitching': '#FF5722',
      'fitting': '#607D8B',
      'ready': '#4CAF50',
    };
    return colors[status] || '#757575';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'normal': '#4CAF50',
      'urgent': '#FF9800',
      'express': '#F44336',
    };
    return colors[priority] || '#757575';
  };

  // Login Screen
  if (currentScreen === 'login') {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="auto" />
        <View style={styles.loginContainer}>
          <Text style={styles.title}>Elite Tailor</Text>
          <Text style={styles.subtitle}>Employee App</Text>

          <View style={styles.formContainer}>
            <Text style={styles.label}>Employee ID</Text>
            <TextInput
              style={styles.input}
              value={employeeId}
              onChangeText={setEmployeeId}
              placeholder="Enter Employee ID (e.g., EMP001)"
              autoCapitalize="characters"
            />

            <Text style={styles.label}>PIN</Text>
            <TextInput
              style={styles.input}
              value={pin}
              onChangeText={setPin}
              placeholder="Enter PIN"
              secureTextEntry
              keyboardType="numeric"
              maxLength={4}
            />

            <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
              <Text style={styles.loginButtonText}>Login</Text>
            </TouchableOpacity>

            <View style={styles.demoContainer}>
              <Text style={styles.demoTitle}>Demo Accounts:</Text>
              <Text style={styles.demoText}>EMP001 / 1234 - Senior Tailor</Text>
              <Text style={styles.demoText}>EMP002 / 1234 - Junior Tailor</Text>
              <Text style={styles.demoText}>EMP003 / 1234 - Cutter</Text>
            </View>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  // Tasks Screen
  if (currentScreen === 'tasks') {
    const myOrders = getMyOrders();

    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="auto" />

        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.headerTitle}>My Tasks</Text>
            <Text style={styles.headerSubtitle}>Welcome, {employee.name}</Text>
          </View>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Text style={styles.logoutButtonText}>Logout</Text>
          </TouchableOpacity>
        </View>

        {/* Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{myOrders.length}</Text>
            <Text style={styles.statLabel}>Total Tasks</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>
              {myOrders.filter(o => o.priority === 'urgent' || o.priority === 'express').length}
            </Text>
            <Text style={styles.statLabel}>Urgent</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>
              {myOrders.filter(o => o.status === 'ready').length}
            </Text>
            <Text style={styles.statLabel}>Completed</Text>
          </View>
        </View>

        {/* Orders List */}
        <FlatList
          data={myOrders}
          keyExtractor={(item) => item.id}
          style={styles.ordersList}
          renderItem={({ item }) => (
            <View style={styles.orderCard}>
              <View style={styles.orderHeader}>
                <Text style={styles.orderId}>#{item.id}</Text>
                <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(item.priority) }]}>
                  <Text style={styles.priorityText}>{item.priority.toUpperCase()}</Text>
                </View>
              </View>

              <Text style={styles.customerName}>{item.customerName}</Text>
              <Text style={styles.garmentType}>{item.garmentType}</Text>
              <Text style={styles.dueDate}>Due: {item.dueDate}</Text>

              <View style={styles.statusContainer}>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
                  <Text style={styles.statusText}>{item.status.toUpperCase()}</Text>
                </View>

                <TouchableOpacity
                  style={[styles.updateButton, { backgroundColor: getStatusColor(item.status) }]}
                  onPress={() => updateOrderStatus(item.id, 'next_status')}
                >
                  <Text style={styles.updateButtonText}>Update Status</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>No tasks assigned</Text>
            </View>
          }
        />
      </SafeAreaView>
    );
  }

  return null;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },

  // Login Screen Styles
  loginContainer: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
    backgroundColor: '#2196F3',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginBottom: 40,
  },
  formContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    marginHorizontal: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    marginTop: 16,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  loginButton: {
    backgroundColor: '#2196F3',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 24,
  },
  loginButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  demoContainer: {
    marginTop: 24,
    padding: 16,
    backgroundColor: '#f0f8ff',
    borderRadius: 8,
  },
  demoTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  demoText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },

  // Tasks Screen Styles
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#2196F3',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
  },
  logoutButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
  },
  logoutButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },

  // Stats Styles
  statsContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },

  // Orders List Styles
  ordersList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  orderCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  orderId: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: 'white',
  },
  customerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  garmentType: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  dueDate: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: 'white',
  },
  updateButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
  },
  updateButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: 'white',
  },

  // Empty State
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});
