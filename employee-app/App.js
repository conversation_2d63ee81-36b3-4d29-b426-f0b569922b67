import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import {
  StyleSheet,
  View,
  Alert,
  ScrollView,
  RefreshControl,
} from 'react-native';
import {
  PaperProvider,
  MD3LightTheme,
  MD3DarkTheme,
  Surface,
  Text,
  TextInput,
  Button,
  Card,
  Avatar,
  Chip,
  Appbar,
  useTheme,
} from 'react-native-paper';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';

// Enhanced Employee App with Material Design 3
const AppContent = () => {
  const [currentScreen, setCurrentScreen] = useState('login');
  const [employee, setEmployee] = useState(null);
  const [employeeId, setEmployeeId] = useState('');
  const [pin, setPin] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const theme = useTheme();

  // Enhanced mock employee data
  const employees = {
    'EMP001': {
      id: 'EMP001',
      name: '<PERSON>',
      role: 'Senior Tailor',
      pin: '1234',
      avatar: 'AH',
      skills: ['Suit Making', 'Shirt Tailoring', 'Alterations'],
      experience: '8 years',
      rating: 4.9,
      completedOrders: 156,
      phone: '+880 1700-000001',
      joinDate: '2016-03-15'
    },
    'EMP002': {
      id: 'EMP002',
      name: 'Fatima Rahman',
      role: 'Junior Tailor',
      pin: '1234',
      avatar: 'FR',
      skills: ['Dress Making', 'Blouse Tailoring', 'Embroidery'],
      experience: '3 years',
      rating: 4.7,
      completedOrders: 89,
      phone: '+880 1700-000002',
      joinDate: '2021-07-20'
    },
    'EMP003': {
      id: 'EMP003',
      name: 'Mohammad Ali',
      role: 'Cutter',
      pin: '1234',
      avatar: 'MA',
      skills: ['Pattern Making', 'Fabric Cutting', 'Quality Control'],
      experience: '5 years',
      rating: 4.8,
      completedOrders: 234,
      phone: '+880 1700-000003',
      joinDate: '2019-01-10'
    },
  };

  // Enhanced mock orders data
  const mockOrders = [
    {
      id: 'ORD001',
      customerName: 'Ahmed Rahman',
      customerPhone: '+880 1700-111001',
      garmentType: 'Three-Piece Suit',
      fabric: 'Wool Blend',
      color: 'Navy Blue',
      status: 'cutting',
      priority: 'urgent',
      dueDate: '2025-05-30',
      orderDate: '2025-05-20',
      assignedTo: 'EMP001',
      estimatedPrice: 8500,
      advancePayment: 4000,
      measurements: {
        chest: '42"',
        waist: '36"',
        length: '30"',
        shoulder: '18"'
      },
      notes: 'Customer prefers slim fit. Extra attention to collar stitching.',
      images: ['suit1.jpg', 'suit2.jpg']
    },
    {
      id: 'ORD002',
      customerName: 'Fatima Khan',
      customerPhone: '+880 1700-111002',
      garmentType: 'Evening Dress',
      fabric: 'Silk Chiffon',
      color: 'Emerald Green',
      status: 'stitching',
      priority: 'normal',
      dueDate: '2025-06-02',
      orderDate: '2025-05-18',
      assignedTo: 'EMP002',
      estimatedPrice: 6500,
      advancePayment: 3000,
      measurements: {
        bust: '36"',
        waist: '28"',
        hips: '38"',
        length: '58"'
      },
      notes: 'Wedding guest dress. Needs to be ready by June 1st.',
      images: ['dress1.jpg']
    },
    {
      id: 'ORD003',
      customerName: 'Mohammad Ali',
      customerPhone: '+880 1700-111003',
      garmentType: 'Formal Shirt',
      fabric: 'Cotton Blend',
      color: 'White',
      status: 'fitting',
      priority: 'express',
      dueDate: '2025-05-28',
      orderDate: '2025-05-25',
      assignedTo: 'EMP001',
      estimatedPrice: 1200,
      advancePayment: 600,
      measurements: {
        chest: '40"',
        waist: '34"',
        sleeve: '24"',
        collar: '16"'
      },
      notes: 'Business meeting tomorrow. Rush order.',
      images: ['shirt1.jpg']
    },
    {
      id: 'ORD004',
      customerName: 'Rashida Begum',
      customerPhone: '+880 1700-111004',
      garmentType: 'Saree Blouse',
      fabric: 'Silk',
      color: 'Gold',
      status: 'measurement_taken',
      priority: 'normal',
      dueDate: '2025-06-05',
      orderDate: '2025-05-22',
      assignedTo: 'EMP002',
      estimatedPrice: 2500,
      advancePayment: 1000,
      measurements: {
        bust: '38"',
        waist: '32"',
        length: '14"',
        sleeve: '12"'
      },
      notes: 'Traditional design with heavy embroidery work.',
      images: ['blouse1.jpg']
    },
  ];

  // Simple handlers
  const handleLogin = () => {
    const emp = employees[employeeId];
    if (emp && emp.pin === pin) {
      setEmployee(emp);
      setCurrentScreen('tasks');
    } else {
      Alert.alert('Error', 'Invalid Employee ID or PIN');
    }
  };

  const handleLogout = () => {
    setEmployee(null);
    setEmployeeId('');
    setPin('');
    setCurrentScreen('login');
  };

  const getMyOrders = () => {
    return mockOrders.filter(order => order.assignedTo === employee?.id);
  };

  const updateOrderStatus = (orderId, newStatus) => {
    Alert.alert('Success', `Order ${orderId} updated!`);
  };

  const handleRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const getStatusColor = (status) => {
    const colors = {
      'cutting': '#9C27B0',
      'stitching': '#FF5722',
      'fitting': '#607D8B',
      'ready': '#4CAF50',
    };
    return colors[status] || '#757575';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'normal': '#4CAF50',
      'urgent': '#FF9800',
      'express': '#F44336',
    };
    return colors[priority] || '#757575';
  };

  // Simple Login Screen with Material Design 3
  if (currentScreen === 'login') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.primary }]}>
        <StatusBar style="light" />
        <ScrollView contentContainerStyle={styles.loginContainer}>
          <View style={styles.loginHeader}>
            <Avatar.Icon
              size={80}
              icon="account-hard-hat"
              style={{ backgroundColor: theme.colors.primaryContainer }}
            />
            <Text variant="displaySmall" style={styles.title}>
              Elite Tailor
            </Text>
            <Text variant="titleMedium" style={styles.subtitle}>
              Employee Portal
            </Text>
          </View>

          <Card style={styles.loginCard}>
            <Card.Content>
              <Text variant="headlineSmall" style={styles.loginTitle}>
                Welcome Back
              </Text>

              <TextInput
                mode="outlined"
                label="Employee ID"
                value={employeeId}
                onChangeText={setEmployeeId}
                placeholder="e.g., EMP001"
                autoCapitalize="characters"
                style={styles.textInput}
              />

              <TextInput
                mode="outlined"
                label="PIN"
                value={pin}
                onChangeText={setPin}
                placeholder="Enter 4-digit PIN"
                secureTextEntry
                keyboardType="numeric"
                maxLength={4}
                style={styles.textInput}
              />

              <Button
                mode="contained"
                onPress={handleLogin}
                style={styles.loginButton}
              >
                Sign In
              </Button>

              <Text variant="bodySmall" style={styles.demoText}>
                Demo: EMP001/1234, EMP002/1234, EMP003/1234
              </Text>
            </Card.Content>
          </Card>
        </ScrollView>
      </SafeAreaView>
    );
  }

  // Simple Tasks Screen with Material Design 3
  if (currentScreen === 'tasks') {
    const myOrders = getMyOrders();

    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content
            title="My Tasks"
            subtitle={`Welcome, ${employee.name}`}
          />
          <Appbar.Action
            icon="logout"
            onPress={handleLogout}
          />
        </Appbar.Header>

        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        >
          <View style={styles.statsContainer}>
            <Card style={styles.statCard}>
              <Card.Content style={styles.statCardContent}>
                <Text variant="displaySmall" style={styles.statNumber}>
                  {myOrders.length}
                </Text>
                <Text variant="bodyMedium">Total Tasks</Text>
              </Card.Content>
            </Card>

            <Card style={styles.statCard}>
              <Card.Content style={styles.statCardContent}>
                <Text variant="displaySmall" style={styles.statNumber}>
                  {myOrders.filter(o => o.priority === 'urgent' || o.priority === 'express').length}
                </Text>
                <Text variant="bodyMedium">Urgent</Text>
              </Card.Content>
            </Card>
          </View>

          <View style={styles.ordersSection}>
            <Text variant="titleLarge" style={styles.sectionTitle}>
              Active Orders
            </Text>

            {myOrders.length === 0 ? (
              <Card style={styles.emptyCard}>
                <Card.Content style={styles.emptyCardContent}>
                  <Text variant="headlineSmall">No Tasks Assigned</Text>
                  <Text variant="bodyMedium">Check back later for new orders</Text>
                </Card.Content>
              </Card>
            ) : (
              myOrders.map((order) => (
                <Card key={order.id} style={styles.orderCard}>
                  <Card.Content>
                    <View style={styles.orderHeader}>
                      <Text variant="titleMedium">#{order.id}</Text>
                      <Chip mode="flat">
                        {order.priority.toUpperCase()}
                      </Chip>
                    </View>
                    <Text variant="bodyLarge" style={styles.customerName}>
                      {order.customerName}
                    </Text>
                    <Text variant="bodyMedium">
                      {order.garmentType}
                    </Text>
                    <Text variant="bodySmall">
                      Due: {order.dueDate}
                    </Text>
                    <View style={styles.statusContainer}>
                      <Chip mode="flat">
                        {order.status.replace('_', ' ').toUpperCase()}
                      </Chip>
                      <Button
                        mode="contained"
                        compact
                        onPress={() => updateOrderStatus(order.id, 'next_status')}
                      >
                        Update
                      </Button>
                    </View>
                  </Card.Content>
                </Card>
              ))
            )}
          </View>
        </ScrollView>

        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="assignment"
              onPress={() => setCurrentScreen('tasks')}
            >
              Tasks
            </Button>
            <Button
              mode="text"
              icon="qr-code-scanner"
              onPress={() => Alert.alert('Scan', 'QR Scanner coming soon!')}
            >
              Scan
            </Button>
            <Button
              mode="text"
              icon="account-circle"
              onPress={() => Alert.alert('Profile', `${employee.name}\n${employee.role}`)}
            >
              Profile
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  return null;
};

// Main App Component with Theme Provider
export default function App() {
  const [isDarkMode, setIsDarkMode] = useState(false);

  const theme = isDarkMode ? MD3DarkTheme : MD3LightTheme;

  return (
    <SafeAreaProvider>
      <PaperProvider theme={theme}>
        <AppContent />
      </PaperProvider>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  // Login Screen Styles
  loginContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 24,
  },
  loginHeader: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    marginTop: 16,
    textAlign: 'center',
    color: 'white',
  },
  subtitle: {
    marginTop: 8,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  loginCard: {
    borderRadius: 28,
    marginHorizontal: 8,
  },
  loginTitle: {
    textAlign: 'center',
    marginBottom: 24,
  },
  textInput: {
    marginBottom: 16,
  },
  loginButton: {
    marginTop: 8,
    marginBottom: 16,
    borderRadius: 20,
  },
  demoText: {
    textAlign: 'center',
    opacity: 0.7,
  },

  // Tasks Screen Styles
  content: {
    flex: 1,
  },
  statsContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  statCard: {
    flex: 1,
    borderRadius: 16,
  },
  statCardContent: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  statNumber: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  ordersSection: {
    paddingHorizontal: 16,
    paddingBottom: 100,
  },
  sectionTitle: {
    marginBottom: 16,
    fontWeight: '600',
  },
  emptyCard: {
    borderRadius: 16,
    marginTop: 32,
  },
  emptyCardContent: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  orderCard: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  customerName: {
    fontWeight: '600',
    marginBottom: 4,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  bottomNav: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  navContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
});
