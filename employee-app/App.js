import React, { useState, useEffect, useMemo } from 'react';
import { StatusBar } from 'expo-status-bar';
import {
  StyleSheet,
  View,
  Alert,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import {
  PaperProvider,
  MD3LightTheme,
  MD3DarkTheme,
  Surface,
  Text,
  TextInput,
  Button,
  Card,
  Avatar,
  Chip,
  Appbar,
  useTheme,
  Switch,
  Divider,
  List,
  IconButton,
  Searchbar,
  Title,
  Paragraph,
  Menu,
  FAB,
  Badge,
} from 'react-native-paper';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

// CommonHeader Component (exactly like Manager App)
const CommonHeader = ({
  title = "Elite Tailor",
  subtitle,
  searchPlaceholder = "Search...",
  searchValue = "",
  onSearchChange,
  showSearch = true,
  showNotifications = true,
  showProfile = true,
  notificationCount = 3,
  onNotificationPress,
  onProfilePress,
  backgroundColor,
  elevation = 4,
  style,
  searchType = "global",
  searchData = [],
  searchFields = ["name", "title", "description"],
  onSearchResult,
}) => {
  const theme = useTheme();
  const [localSearchValue, setLocalSearchValue] = useState(searchValue);

  const handleSearchChange = (value) => {
    setLocalSearchValue(value);
    if (onSearchChange) {
      onSearchChange(value);
    }
  };

  const headerBackgroundColor = backgroundColor || theme.colors.surface;

  return (
    <Surface
      style={[
        {
          backgroundColor: headerBackgroundColor,
          paddingTop: 44,
          paddingBottom: 8,
          borderBottomWidth: 1,
          borderBottomColor: theme.colors.outline + '20',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 0.5 },
          shadowOpacity: 0.03,
          shadowRadius: 1,
        },
        style
      ]}
      elevation={elevation}
    >
      {/* Main Header Row */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 12,
        marginHorizontal: 8,
        marginBottom: 6,
      }}>
        {/* Left Section - Title */}
        <View style={{ flex: 1, marginRight: 12 }}>
          <Text
            variant="headlineSmall"
            style={{
              color: theme.colors.onSurface,
              fontWeight: '700',
              letterSpacing: 0.3,
              fontSize: 18,
            }}
            numberOfLines={1}
          >
            {title}
          </Text>
          {subtitle && (
            <Text
              variant="bodySmall"
              style={{
                color: theme.colors.onSurfaceVariant,
                marginTop: 1,
                opacity: 0.8,
                fontSize: 12,
              }}
              numberOfLines={1}
            >
              {subtitle}
            </Text>
          )}
        </View>

        {/* Right Section - Icons */}
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          {showNotifications && (
            <View style={{ position: 'relative' }}>
              <IconButton
                icon="bell-outline"
                size={20}
                iconColor={theme.colors.onSurface}
                style={{
                  margin: 0,
                  marginHorizontal: 2,
                  width: 36,
                  height: 36,
                  borderRadius: 18,
                }}
                onPress={onNotificationPress}
              />
              {notificationCount > 0 && (
                <Badge
                  style={{
                    position: 'absolute',
                    top: 4,
                    right: 4,
                    minWidth: 14,
                    height: 14,
                    borderRadius: 7,
                    fontSize: 8,
                    fontWeight: '600',
                    backgroundColor: theme.colors.error,
                  }}
                >
                  {notificationCount}
                </Badge>
              )}
            </View>
          )}

          {showProfile && (
            <TouchableOpacity
              style={{ position: 'relative', marginHorizontal: 2 }}
              onPress={onProfilePress}
            >
              <Avatar.Text
                size={32}
                label="TS"
                style={{
                  backgroundColor: theme.colors.primary,
                  borderWidth: 1.5,
                  borderColor: 'rgba(255, 255, 255, 0.2)',
                }}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Search Row */}
      {showSearch && (
        <View style={{ paddingHorizontal: 12, marginHorizontal: 8 }}>
          <Searchbar
            placeholder={searchPlaceholder}
            value={localSearchValue}
            onChangeText={handleSearchChange}
            style={{
              borderRadius: 20,
              height: 40,
              elevation: 1,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 1 },
              shadowOpacity: 0.05,
              shadowRadius: 1,
            }}
            inputStyle={{ fontSize: 14 }}
            iconColor={theme.colors.onSurfaceVariant}
          />
        </View>
      )}
    </Surface>
  );
};

// StatCardGroup Component (exactly like Manager App)
const StatCardGroup = ({
  title,
  cards = [],
  columns = 2,
  showTitle = true,
  titleStyle = {},
  containerStyle = {},
  onCardPress
}) => {
  const theme = useTheme();

  const handleCardPress = (card, index) => {
    if (onCardPress) {
      onCardPress(card, index);
    } else if (card.onPress) {
      card.onPress();
    }
  };

  const isOddCount = cards.length % columns !== 0;
  const lastCardIndex = cards.length - 1;

  return (
    <View style={[{ marginBottom: 16 }, containerStyle]}>
      {showTitle && title && (
        <Text variant="titleLarge" style={[{ marginBottom: 12, fontWeight: '600' }, titleStyle]}>
          {title}
        </Text>
      )}

      <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8 }}>
        {cards.map((card, index) => {
          const isLastCard = index === lastCardIndex;
          const shouldSpanFullWidth = isOddCount && isLastCard;

          return (
            <TouchableOpacity
              key={card.key || index}
              style={{
                width: shouldSpanFullWidth ? '100%' : `${(100 / columns) - 2}%`,
              }}
              onPress={() => handleCardPress(card, index)}
            >
              <Card style={{ borderRadius: 16 }}>
                <Card.Content style={{ alignItems: 'center', paddingVertical: 16 }}>
                  {card.icon && (
                    <View style={{
                      width: 40,
                      height: 40,
                      borderRadius: 20,
                      backgroundColor: (card.iconColor || theme.colors.primary) + '15',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 8,
                    }}>
                      <Icon name={card.icon} size={24} color={card.iconColor || theme.colors.primary} />
                    </View>
                  )}
                  <Text variant="titleLarge" style={{ fontWeight: '700', marginBottom: 4 }}>
                    {card.value}
                  </Text>
                  <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center' }}>
                    {card.title}
                  </Text>
                </Card.Content>
              </Card>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

// UnifiedFilterChips Component (exactly like Manager App)
const UnifiedFilterChips = ({
  filters = [],
  selectedFilter,
  onFilterChange,
  style,
  chipStyle,
  showCounts = false,
  data = [],
  countField = 'status',
}) => {
  const theme = useTheme();

  const getFilterCount = (filter) => {
    if (!showCounts || !data.length) return null;

    if (filter === 'All') {
      return data.length;
    }

    return data.filter(item => {
      const fieldValue = item[countField];
      return fieldValue === filter;
    }).length;
  };

  const getChipLabel = (filter) => {
    const count = getFilterCount(filter);
    return count !== null ? `${filter} (${count})` : filter;
  };

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={[{ flexGrow: 0 }, style]}
      contentContainerStyle={{ paddingHorizontal: 16, paddingVertical: 8, gap: 8 }}
    >
      {filters.map((filter) => (
        <Chip
          key={filter}
          mode={selectedFilter === filter ? "flat" : "outlined"}
          selected={selectedFilter === filter}
          onPress={() => onFilterChange?.(filter)}
          style={[
            {
              backgroundColor: selectedFilter === filter ? theme.colors.primary : theme.colors.surface,
              borderColor: selectedFilter === filter ? theme.colors.primary : theme.colors.outline,
            },
            chipStyle
          ]}
          textStyle={{
            color: selectedFilter === filter ? theme.colors.onPrimary : theme.colors.onSurface,
          }}
        >
          {getChipLabel(filter)}
        </Chip>
      ))}
    </ScrollView>
  );
};

// UnifiedEmptyState Component (exactly like Manager App)
const UnifiedEmptyState = ({
  icon = 'inbox-outline',
  title = 'No items found',
  description,
  actionLabel,
  onActionPress,
  type,
  style,
  iconSize = 64,
  iconColor,
  searchQuery,
}) => {
  const theme = useTheme();

  const getTypeConfig = () => {
    switch (type) {
      case 'products':
        return {
          icon: 'hanger',
          title: 'No products found',
          description: searchQuery ? `No products match "${searchQuery}"` : 'Start by adding your first product',
          actionLabel: 'Add Product',
        };
      case 'orders':
        return {
          icon: 'clipboard-list-outline',
          title: 'No orders found',
          description: searchQuery ? `No orders match "${searchQuery}"` : 'Orders will appear here once created',
          actionLabel: 'Create Order',
        };
      case 'customers':
        return {
          icon: 'account-group-outline',
          title: 'No customers found',
          description: searchQuery ? `No customers match "${searchQuery}"` : 'Add customers to get started',
          actionLabel: 'Add Customer',
        };
      default:
        return { icon, title, description, actionLabel };
    }
  };

  const config = getTypeConfig();

  return (
    <View style={[{
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 48,
      paddingHorizontal: 24,
    }, style]}>
      <Icon
        name={config.icon}
        size={iconSize}
        color={iconColor || theme.colors.onSurfaceVariant}
        style={{ marginBottom: 16 }}
      />
      <Text variant="headlineSmall" style={{
        color: theme.colors.onSurface,
        textAlign: 'center',
        marginBottom: 8,
        fontWeight: '600'
      }}>
        {config.title}
      </Text>
      {config.description && (
        <Text variant="bodyMedium" style={{
          color: theme.colors.onSurfaceVariant,
          textAlign: 'center',
          marginBottom: 24,
          lineHeight: 20,
        }}>
          {config.description}
        </Text>
      )}
      {config.actionLabel && onActionPress && (
        <Button
          mode="contained"
          onPress={onActionPress}
          style={{ borderRadius: 20 }}
        >
          {config.actionLabel}
        </Button>
      )}
    </View>
  );
};

// Enhanced Employee App with Full Features (exactly like Manager App)
const AppContent = () => {
  const [currentScreen, setCurrentScreen] = useState('login');
  const [employee, setEmployee] = useState(null);
  const [employeeId, setEmployeeId] = useState('');
  const [pin, setPin] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [showBottomSheet, setShowBottomSheet] = useState(false);
  const [bottomSheetType, setBottomSheetType] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState('All');
  const [selectedCustomerType, setSelectedCustomerType] = useState('All');
  const theme = useTheme();

  // Enhanced mock employee data (moved to top)
  const employees = {
    'EMP001': {
      id: 'EMP001',
      name: 'Ahmed Hassan',
      role: 'Senior Tailor',
      pin: '1234',
      avatar: 'AH',
      skills: ['Suit Making', 'Shirt Tailoring', 'Alterations'],
      experience: '8 years',
      rating: 4.9,
      completedOrders: 156,
      phone: '+880 1700-000001',
      joinDate: '2016-03-15'
    },
    'EMP002': {
      id: 'EMP002',
      name: 'Fatima Rahman',
      role: 'Junior Tailor',
      pin: '1234',
      avatar: 'FR',
      skills: ['Dress Making', 'Blouse Tailoring', 'Embroidery'],
      experience: '3 years',
      rating: 4.7,
      completedOrders: 89,
      phone: '+880 1700-000002',
      joinDate: '2021-07-20'
    },
    'EMP003': {
      id: 'EMP003',
      name: 'Mohammad Ali',
      role: 'Cutter',
      pin: '1234',
      avatar: 'MA',
      skills: ['Pattern Making', 'Fabric Cutting', 'Quality Control'],
      experience: '5 years',
      rating: 4.8,
      completedOrders: 234,
      phone: '+880 1700-000003',
      joinDate: '2019-01-10'
    },
  };

  // Comprehensive mock data (moved to top)
  const mockCustomers = [
    {
      id: 'CUST001',
      name: 'Ahmed Rahman',
      phone: '+880 1700-111001',
      email: '<EMAIL>',
      address: 'House 123, Road 5, Dhanmondi, Dhaka',
      joinDate: '2024-01-15',
      totalOrders: 5,
      measurements: {
        chest: '42"', waist: '36"', length: '30"', shoulder: '18"'
      }
    },
    {
      id: 'CUST002',
      name: 'Fatima Khan',
      phone: '+880 1700-111002',
      email: '<EMAIL>',
      address: 'Apartment 4B, Gulshan Avenue, Dhaka',
      joinDate: '2024-02-20',
      totalOrders: 3,
      measurements: {
        bust: '36"', waist: '28"', hips: '38"', length: '58"'
      }
    },
    {
      id: 'CUST003',
      name: 'Mohammad Ali',
      phone: '+880 1700-111003',
      email: '<EMAIL>',
      address: 'Plot 67, Banani, Dhaka',
      joinDate: '2024-03-10',
      totalOrders: 8,
      measurements: {
        chest: '40"', waist: '34"', sleeve: '24"', collar: '16"'
      }
    }
  ];

  const mockProducts = [
    {
      id: 'PROD001',
      name: 'Premium Wool Suit',
      category: 'Suits',
      price: 8500,
      fabric: 'Wool Blend',
      colors: ['Navy Blue', 'Charcoal', 'Black'],
      sizes: ['S', 'M', 'L', 'XL', 'XXL'],
      inStock: true,
      description: 'High-quality wool blend suit perfect for formal occasions'
    },
    {
      id: 'PROD002',
      name: 'Silk Evening Dress',
      category: 'Dresses',
      price: 6500,
      fabric: 'Pure Silk',
      colors: ['Emerald Green', 'Royal Blue', 'Burgundy'],
      sizes: ['XS', 'S', 'M', 'L', 'XL'],
      inStock: true,
      description: 'Elegant silk dress for special occasions'
    },
    {
      id: 'PROD003',
      name: 'Cotton Formal Shirt',
      category: 'Shirts',
      price: 1200,
      fabric: 'Cotton Blend',
      colors: ['White', 'Light Blue', 'Pink'],
      sizes: ['S', 'M', 'L', 'XL'],
      inStock: true,
      description: 'Professional cotton shirt for business wear'
    }
  ];

  const mockOrders = [
    {
      id: 'ORD001',
      customerId: 'CUST001',
      customerName: 'Ahmed Rahman',
      customerPhone: '+880 1700-111001',
      productId: 'PROD001',
      garmentType: 'Three-Piece Suit',
      fabric: 'Wool Blend',
      color: 'Navy Blue',
      status: 'cutting',
      priority: 'urgent',
      dueDate: '2025-05-30',
      orderDate: '2025-05-20',
      assignedTo: 'EMP001',
      estimatedPrice: 8500,
      advancePayment: 4000,
      measurements: {
        chest: '42"', waist: '36"', length: '30"', shoulder: '18"'
      },
      notes: 'Customer prefers slim fit. Extra attention to collar stitching.',
      images: ['suit1.jpg', 'suit2.jpg']
    },
    {
      id: 'ORD002',
      customerId: 'CUST002',
      customerName: 'Fatima Khan',
      customerPhone: '+880 1700-111002',
      productId: 'PROD002',
      garmentType: 'Evening Dress',
      fabric: 'Silk Chiffon',
      color: 'Emerald Green',
      status: 'stitching',
      priority: 'normal',
      dueDate: '2025-06-02',
      orderDate: '2025-05-18',
      assignedTo: 'EMP002',
      estimatedPrice: 6500,
      advancePayment: 3000,
      measurements: {
        bust: '36"', waist: '28"', hips: '38"', length: '58"'
      },
      notes: 'Wedding guest dress. Needs to be ready by June 1st.',
      images: ['dress1.jpg']
    },
    {
      id: 'ORD003',
      customerId: 'CUST003',
      customerName: 'Mohammad Ali',
      customerPhone: '+880 1700-111003',
      productId: 'PROD003',
      garmentType: 'Formal Shirt',
      fabric: 'Cotton Blend',
      color: 'White',
      status: 'fitting',
      priority: 'express',
      dueDate: '2025-05-28',
      orderDate: '2025-05-25',
      assignedTo: 'EMP001',
      estimatedPrice: 1200,
      advancePayment: 600,
      measurements: {
        chest: '40"', waist: '34"', sleeve: '24"', collar: '16"'
      },
      notes: 'Business meeting tomorrow. Rush order.',
      images: ['shirt1.jpg']
    },
    {
      id: 'ORD004',
      customerId: 'CUST002',
      customerName: 'Rashida Begum',
      customerPhone: '+880 1700-111004',
      productId: 'PROD002',
      garmentType: 'Saree Blouse',
      fabric: 'Silk',
      color: 'Gold',
      status: 'measurement_taken',
      priority: 'normal',
      dueDate: '2025-06-05',
      orderDate: '2025-05-22',
      assignedTo: 'EMP002',
      estimatedPrice: 2500,
      advancePayment: 1000,
      measurements: {
        bust: '38"', waist: '32"', length: '14"', sleeve: '12"'
      },
      notes: 'Traditional design with heavy embroidery work.',
      images: ['blouse1.jpg']
    },
    {
      id: 'ORD005',
      customerId: 'CUST001',
      customerName: 'Ahmed Rahman',
      customerPhone: '+880 1700-111001',
      productId: 'PROD003',
      garmentType: 'Business Shirt',
      fabric: 'Cotton',
      color: 'Light Blue',
      status: 'ready',
      priority: 'normal',
      dueDate: '2025-05-25',
      orderDate: '2025-05-15',
      assignedTo: 'EMP001',
      estimatedPrice: 1200,
      advancePayment: 1200,
      measurements: {
        chest: '42"', waist: '36"', sleeve: '24"', collar: '16"'
      },
      notes: 'Regular customer. Standard measurements.',
      images: ['shirt2.jpg']
    }
  ];

  // Pre-compute all data at the top level to avoid hooks in conditionals
  const today = new Date().toISOString().split('T')[0];

  // Dashboard stats (computed at top level)
  const dashboardStats = useMemo(() => {
    const safeOrders = mockOrders || [];
    const safeProducts = mockProducts || [];
    const safeCustomers = mockCustomers || [];

    const todaysSales = safeOrders
      .filter(order => order.orderDate === today && order.status === 'ready')
      .reduce((sum, order) => sum + (order.estimatedPrice || 0), 0);

    return {
      todaysSales,
      totalOrders: safeOrders.length,
      totalProducts: safeProducts.length,
      totalCustomers: safeCustomers.length,
    };
  }, [mockOrders, mockProducts, mockCustomers, today]);

  // Dashboard cards (computed at top level)
  const dashboardCards = useMemo(() => [
    {
      key: 'sales',
      title: 'Today\'s Sales',
      value: `৳${dashboardStats.todaysSales.toFixed(0)}`,
      icon: 'cash-multiple',
      iconColor: theme.colors.primary,
      onPress: () => handleNavigation('orders')
    },
    {
      key: 'orders',
      title: 'Total Orders',
      value: dashboardStats.totalOrders.toString(),
      icon: 'clipboard-list-outline',
      iconColor: theme.colors.secondary,
      onPress: () => handleNavigation('orders')
    },
    {
      key: 'products',
      title: 'Total Products',
      value: dashboardStats.totalProducts.toString(),
      icon: 'hanger',
      iconColor: theme.colors.tertiary,
      onPress: () => handleNavigation('products')
    },
    {
      key: 'customers',
      title: 'Total Customers',
      value: dashboardStats.totalCustomers.toString(),
      icon: 'account-group-outline',
      iconColor: theme.colors.primary,
      onPress: () => handleNavigation('customers')
    },
    {
      key: 'tasks',
      title: 'My Tasks',
      value: ((mockOrders || []).filter(order => order.assignedTo === (employee?.id || 'NONE')) || []).length.toString(),
      icon: 'clipboard-check-outline',
      iconColor: "#10B981",
      onPress: () => handleNavigation('tasks')
    },
    {
      key: 'urgent',
      title: 'Urgent Orders',
      value: (mockOrders || []).filter(o => o.priority === 'urgent' || o.priority === 'express').length.toString(),
      icon: 'alert-circle-outline',
      iconColor: "#F59E0B",
      onPress: () => handleNavigation('orders')
    },
    {
      key: 'reports',
      title: 'View Reports',
      value: 'Analytics',
      icon: 'file-chart',
      iconColor: "#8B5CF6",
      onPress: () => handleNavigation('reports')
    },
  ], [dashboardStats, theme.colors, mockOrders, employee]);

  // Recent orders (computed at top level)
  const recentOrders = useMemo(() => {
    const safeOrders = mockOrders || [];
    return safeOrders
      .sort((a, b) => new Date(b.orderDate || 0) - new Date(a.orderDate || 0))
      .slice(0, 2);
  }, [mockOrders]);

  // Filtered orders (computed at top level)
  const filteredOrders = useMemo(() => {
    const safeOrders = mockOrders || [];
    return safeOrders.filter(order => {
      const matchesSearch = searchQuery === '' ||
        order.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.garmentType.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesStatus = selectedStatus === 'All' ||
        (selectedStatus === 'Pending' && order.status === 'measurement_pending') ||
        (selectedStatus === 'In Progress' && ['measurement_taken', 'cutting', 'stitching', 'fitting', 'finishing'].includes(order.status)) ||
        (selectedStatus === 'Completed' && order.status === 'ready') ||
        (selectedStatus === 'Cancelled' && order.status === 'cancelled');

      return matchesSearch && matchesStatus;
    });
  }, [mockOrders, searchQuery, selectedStatus]);

  // Order stats (computed at top level)
  const orderStats = useMemo(() => ({
    total: filteredOrders.length,
    pending: filteredOrders.filter(o => o.status === 'measurement_pending').length,
    inProgress: filteredOrders.filter(o => ['measurement_taken', 'cutting', 'stitching', 'fitting', 'finishing'].includes(o.status)).length,
    completed: filteredOrders.filter(o => o.status === 'ready').length,
    revenue: filteredOrders.reduce((sum, order) => sum + order.estimatedPrice, 0),
  }), [filteredOrders]);

  // Filtered customers (computed at top level)
  const filteredCustomers = useMemo(() => {
    const safeCustomers = mockCustomers || [];
    return safeCustomers.filter(customer => {
      const matchesSearch = searchQuery === '' ||
        customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        customer.phone.includes(searchQuery) ||
        customer.address.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesType = selectedCustomerType === 'All' ||
        (selectedCustomerType === 'Active' && customer.totalOrders > 0) ||
        (selectedCustomerType === 'New' && customer.totalOrders === 0) ||
        (selectedCustomerType === 'VIP' && customer.totalOrders >= 5);

      return matchesSearch && matchesType;
    });
  }, [mockCustomers, searchQuery, selectedCustomerType]);

  // Enhanced handlers for all features
  const handleLogin = () => {
    const emp = employees[employeeId];
    if (emp && emp.pin === pin) {
      setEmployee(emp);
      setCurrentScreen('dashboard');
    } else {
      Alert.alert('Error', 'Invalid Employee ID or PIN');
    }
  };

  const handleLogout = () => {
    setEmployee(null);
    setEmployeeId('');
    setPin('');
    setCurrentScreen('login');
  };

  const getMyOrders = () => {
    return mockOrders.filter(order => order.assignedTo === employee?.id);
  };

  const getAllOrders = () => {
    return searchQuery
      ? mockOrders.filter(order =>
          order.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
          order.garmentType.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : mockOrders;
  };

  const getCustomers = () => {
    return searchQuery
      ? mockCustomers.filter(customer =>
          customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          customer.phone.includes(searchQuery) ||
          customer.email.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : mockCustomers;
  };

  const getProducts = () => {
    return searchQuery
      ? mockProducts.filter(product =>
          product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.category.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : mockProducts;
  };

  const updateOrderStatus = (orderId, newStatus) => {
    Alert.alert('Success', `Order ${orderId} updated!`);
  };

  const handleRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const openBottomSheet = (type, data = null) => {
    setBottomSheetType(type);
    if (type === 'order' && data) setSelectedOrder(data);
    if (type === 'customer' && data) setSelectedCustomer(data);
    setShowBottomSheet(true);
  };

  const closeBottomSheet = () => {
    setShowBottomSheet(false);
    setBottomSheetType('');
    setSelectedOrder(null);
    setSelectedCustomer(null);
  };

  const handleNavigation = (screen) => {
    setCurrentScreen(screen);
    setSearchQuery('');
  };

  // Utility functions
  const getStatusColor = (status) => {
    const colors = {
      'measurement_pending': '#9C27B0',
      'measurement_taken': '#2196F3',
      'cutting': '#9C27B0',
      'stitching': '#FF5722',
      'fitting': '#607D8B',
      'finishing': '#795548',
      'ready': '#4CAF50',
      'delivered': '#4CAF50',
    };
    return colors[status] || '#757575';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'normal': '#4CAF50',
      'urgent': '#FF9800',
      'express': '#F44336',
    };
    return colors[priority] || '#757575';
  };



  // Enhanced Login Screen
  if (currentScreen === 'login') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.primary }]}>
        <StatusBar style="light" />
        <ScrollView contentContainerStyle={styles.loginContainer}>
          <View style={styles.loginHeader}>
            <Avatar.Icon
              size={80}
              icon="account-hard-hat"
              style={{ backgroundColor: theme.colors.primaryContainer }}
            />
            <Text variant="displaySmall" style={styles.title}>
              Elite Tailor
            </Text>
            <Text variant="titleMedium" style={styles.subtitle}>
              Employee Portal
            </Text>
          </View>

          <Card style={styles.loginCard}>
            <Card.Content>
              <Text variant="headlineSmall" style={styles.loginTitle}>
                Welcome Back
              </Text>

              <TextInput
                mode="outlined"
                label="Employee ID"
                value={employeeId}
                onChangeText={setEmployeeId}
                placeholder="e.g., EMP001"
                autoCapitalize="characters"
                style={styles.textInput}
              />

              <TextInput
                mode="outlined"
                label="PIN"
                value={pin}
                onChangeText={setPin}
                placeholder="Enter 4-digit PIN"
                secureTextEntry
                keyboardType="numeric"
                maxLength={4}
                style={styles.textInput}
              />

              <Button
                mode="contained"
                onPress={handleLogin}
                style={styles.loginButton}
              >
                Sign In
              </Button>

              <Text variant="bodySmall" style={styles.demoText}>
                Demo: EMP001/1234, EMP002/1234, EMP003/1234
              </Text>
            </Card.Content>
          </Card>
        </ScrollView>
      </SafeAreaView>
    );
  }

  // Dashboard Screen (exactly like Manager App)
  if (currentScreen === 'dashboard') {

    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <CommonHeader
          title="Dashboard"
          subtitle={`Welcome, ${employee?.name || 'Employee'}`}
          showSearch={false}
          onNotificationPress={() => console.log('Notifications pressed')}
          onProfilePress={() => handleNavigation('profile')}
        />

        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
        >
          <View style={{ padding: 16 }}>
            <StatCardGroup
              cards={dashboardCards}
              columns={2}
              showTitle={false}
            />

            <View style={{ marginTop: 24 }}>
              <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: 16,
              }}>
                <Text variant="titleLarge" style={{
                  color: theme.colors.onBackground,
                  fontWeight: '600',
                }}>
                  Recent Orders
                </Text>
                <Button
                  mode="text"
                  onPress={() => handleNavigation('orders')}
                  textColor={theme.colors.primary}
                  compact
                >
                  View All
                </Button>
              </View>

              <View>
                {recentOrders.map((order, index) => (
                  <TouchableOpacity
                    key={order.id}
                    onPress={() => openBottomSheet('order', order)}
                  >
                    <Surface
                      style={{
                        backgroundColor: theme.colors.surface,
                        borderRadius: 16,
                        marginBottom: 12,
                        borderWidth: 1,
                        borderColor: theme.colors.outline + '20',
                      }}
                      elevation={0}
                    >
                      <View style={{ padding: 16 }}>
                        <View style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignItems: 'flex-start',
                          marginBottom: 8,
                        }}>
                          <View style={{ flex: 1 }}>
                            <Text variant="titleMedium" style={{ fontWeight: '600', marginBottom: 4 }}>
                              #{order.id}
                            </Text>
                            <Text variant="bodyLarge" style={{ color: theme.colors.onSurface, marginBottom: 2 }}>
                              {order.customerName}
                            </Text>
                            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                              {order.garmentType}
                            </Text>
                          </View>
                          <View style={{ alignItems: 'flex-end' }}>
                            <Chip
                              mode="flat"
                              style={{
                                backgroundColor: getStatusColor(order.status) + '20',
                                marginBottom: 4,
                              }}
                              textStyle={{ fontSize: 12 }}
                            >
                              {order.status.replace('_', ' ').toUpperCase()}
                            </Chip>
                            <Text variant="titleMedium" style={{ fontWeight: '700' }}>
                              ৳{order.estimatedPrice}
                            </Text>
                          </View>
                        </View>
                        <View style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}>
                          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                            Due: {order.dueDate}
                          </Text>
                          <Chip
                            mode="outlined"
                            style={{
                              backgroundColor: getPriorityColor(order.priority) + '15',
                              borderColor: getPriorityColor(order.priority),
                            }}
                            textStyle={{ fontSize: 11, color: getPriorityColor(order.priority) }}
                          >
                            {order.priority.toUpperCase()}
                          </Chip>
                        </View>
                      </View>
                    </Surface>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={[styles.navButton, currentScreen === 'dashboard' && styles.activeNavButton]}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="qrcode-scan"
              onPress={() => handleNavigation('scan')}
              style={styles.navButton}
            >
              Scan
            </Button>
            <Button
              mode="text"
              icon="plus-circle"
              onPress={() => handleNavigation('quickActions')}
              style={[styles.navButton, styles.plusButton]}
            >
              Add
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="cog"
              onPress={() => handleNavigation('settings')}
              style={styles.navButton}
            >
              Settings
            </Button>
          </View>
        </Surface>
      </View>
    );
  }

  // Orders Screen (exactly like Manager App)
  if (currentScreen === 'orders') {
    const statuses = ['All', 'Pending', 'In Progress', 'Completed', 'Cancelled'];

    const handleAddOrder = () => {
      console.log('Navigating to Add Order page...');
      handleNavigation('addOrder');
    };

    const handleEditOrder = (order) => {
      console.log('Editing order:', order.id);
      Alert.alert('Edit Order', `Edit order ${order.id} functionality coming soon!`);
    };

    const handleDeleteOrder = (order) => {
      Alert.alert(
        'Delete Order',
        `Are you sure you want to delete order ${order.id}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: () => Alert.alert('Success', `Order ${order.id} deleted!`),
          },
        ]
      );
    };

    const handleUpdateOrderStatus = (order, newStatus) => {
      Alert.alert('Success', `Order ${order.id} status updated to ${newStatus}!`);
    };

    const handleViewInvoice = (order) => {
      Alert.alert('Invoice', `Viewing invoice for order ${order.id}`);
    };

    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <CommonHeader
          title="Orders"
          subtitle="Track and manage orders"
          searchPlaceholder="Search orders..."
          searchType="orders"
          searchData={mockOrders || []}
          searchFields={["customerName", "customer", "id", "status"]}
          onSearchChange={setSearchQuery}
          onSearchResult={(order) => {
            console.log('Order selected from search:', order);
            openBottomSheet('order', order);
          }}
          onNotificationPress={() => console.log('Notifications pressed')}
          onProfilePress={() => handleNavigation('profile')}
        />

        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
          ListHeaderComponent={() => (
            <View>
              {/* Status Filter */}
              <UnifiedFilterChips
                filters={statuses}
                selectedFilter={selectedStatus}
                onFilterChange={setSelectedStatus}
                showCounts={true}
                data={mockOrders || []}
                countField="status"
                style={{ marginBottom: 16 }}
              />

              {/* Stats Row */}
              <StatCardGroup
                cards={[
                  {
                    key: 'total',
                    title: 'Total Orders',
                    value: orderStats.total.toString(),
                    icon: 'clipboard-list-outline',
                    iconColor: theme.colors.primary,
                    elevation: 1,
                  },
                  {
                    key: 'revenue',
                    title: 'Revenue',
                    value: `৳${orderStats.revenue.toFixed(0)}`,
                    icon: 'currency-usd',
                    iconColor: theme.colors.secondary,
                    elevation: 1,
                  },
                ]}
                columns={2}
                showTitle={false}
                containerStyle={{ marginBottom: 16, paddingHorizontal: 16 }}
              />
            </View>
          )}
        >
          <View style={{ paddingHorizontal: 16, paddingBottom: 100 }}>
            {filteredOrders.length === 0 ? (
              <UnifiedEmptyState
                type="orders"
                searchQuery={searchQuery}
                onActionPress={handleAddOrder}
              />
            ) : (
              filteredOrders.map((order) => (
                <TouchableOpacity
                  key={order.id}
                  onPress={() => openBottomSheet('order', order)}
                >
                  <Surface
                    style={{
                      backgroundColor: theme.colors.surface,
                      borderRadius: 16,
                      marginBottom: 12,
                      borderWidth: 1,
                      borderColor: theme.colors.outline + '20',
                    }}
                    elevation={0}
                  >
                    <View style={{ padding: 16 }}>
                      <View style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'flex-start',
                        marginBottom: 8,
                      }}>
                        <View style={{ flex: 1 }}>
                          <Text variant="titleMedium" style={{ fontWeight: '600', marginBottom: 4 }}>
                            #{order.id}
                          </Text>
                          <Text variant="bodyLarge" style={{ color: theme.colors.onSurface, marginBottom: 2 }}>
                            {order.customerName}
                          </Text>
                          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                            {order.garmentType} • {order.fabric}
                          </Text>
                          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 4 }}>
                            Due: {order.dueDate} • Assigned: {order.assignedTo}
                          </Text>
                        </View>
                        <View style={{ alignItems: 'flex-end' }}>
                          <Chip
                            mode="flat"
                            style={{
                              backgroundColor: getStatusColor(order.status) + '20',
                              marginBottom: 4,
                            }}
                            textStyle={{ fontSize: 12 }}
                          >
                            {order.status.replace('_', ' ').toUpperCase()}
                          </Chip>
                          <Text variant="titleMedium" style={{ fontWeight: '700' }}>
                            ৳{order.estimatedPrice}
                          </Text>
                        </View>
                      </View>
                      <View style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}>
                        <Chip
                          mode="outlined"
                          style={{
                            backgroundColor: getPriorityColor(order.priority) + '15',
                            borderColor: getPriorityColor(order.priority),
                          }}
                          textStyle={{ fontSize: 11, color: getPriorityColor(order.priority) }}
                        >
                          {order.priority.toUpperCase()}
                        </Chip>
                        <View style={{ flexDirection: 'row', gap: 8 }}>
                          <IconButton
                            icon="pencil-outline"
                            size={20}
                            iconColor={theme.colors.primary}
                            onPress={() => handleEditOrder(order)}
                            style={{ margin: 0 }}
                          />
                          <IconButton
                            icon="file-document-outline"
                            size={20}
                            iconColor={theme.colors.secondary}
                            onPress={() => handleViewInvoice(order)}
                            style={{ margin: 0 }}
                          />
                          <IconButton
                            icon="delete-outline"
                            size={20}
                            iconColor={theme.colors.error}
                            onPress={() => handleDeleteOrder(order)}
                            style={{ margin: 0 }}
                          />
                        </View>
                      </View>
                    </View>
                  </Surface>
                </TouchableOpacity>
              ))
            )}
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface
          style={[
            styles.bottomNav,
            {
              backgroundColor: theme.colors.surface,
              borderTopColor: theme.colors.outline + '20'
            }
          ]}
          elevation={8}
        >
          <TouchableOpacity
            style={styles.navButton}
            onPress={() => handleNavigation('dashboard')}
          >
            <Icon
              name="view-dashboard"
              size={24}
              color={currentScreen === 'dashboard' ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
            <Text
              variant="labelSmall"
              style={{
                color: currentScreen === 'dashboard' ? theme.colors.primary : theme.colors.onSurfaceVariant,
                marginTop: 2,
                fontWeight: currentScreen === 'dashboard' ? '600' : '400',
                fontSize: 10
              }}
            >
              Dashboard
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navButton}
            onPress={() => handleNavigation('scan')}
          >
            <Icon
              name="qrcode-scan"
              size={24}
              color={currentScreen === 'scan' ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
            <Text
              variant="labelSmall"
              style={{
                color: currentScreen === 'scan' ? theme.colors.primary : theme.colors.onSurfaceVariant,
                marginTop: 2,
                fontWeight: currentScreen === 'scan' ? '600' : '400',
                fontSize: 10
              }}
            >
              Scan
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.plusButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => handleNavigation('quickActions')}
          >
            <Icon
              name="plus"
              size={24}
              color={theme.colors.onPrimary}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navButton}
            onPress={() => handleNavigation('orders')}
          >
            <Icon
              name="clipboard-list"
              size={24}
              color={currentScreen === 'orders' ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
            <Text
              variant="labelSmall"
              style={{
                color: currentScreen === 'orders' ? theme.colors.primary : theme.colors.onSurfaceVariant,
                marginTop: 2,
                fontWeight: currentScreen === 'orders' ? '600' : '400',
                fontSize: 10
              }}
            >
              Orders
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navButton}
            onPress={() => handleNavigation('settings')}
          >
            <Icon
              name="cog"
              size={24}
              color={currentScreen === 'settings' ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
            <Text
              variant="labelSmall"
              style={{
                color: currentScreen === 'settings' ? theme.colors.primary : theme.colors.onSurfaceVariant,
                marginTop: 2,
                fontWeight: currentScreen === 'settings' ? '600' : '400',
                fontSize: 10
              }}
            >
              Settings
            </Text>
          </TouchableOpacity>
        </Surface>
      </View>
    );
  }

  // Enhanced Tasks Screen (My Tasks)
  if (currentScreen === 'tasks') {
    const myOrders = (mockOrders || []).filter(order => order.assignedTo === employee?.id) || [];

    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content
            title="My Tasks"
            subtitle={`${myOrders.length} assigned orders`}
          />
        </Appbar.Header>

        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        >
          <View style={styles.statsContainer}>
            <Card style={styles.statCard}>
              <Card.Content style={styles.statCardContent}>
                <Text variant="displaySmall" style={styles.statNumber}>
                  {myOrders.length}
                </Text>
                <Text variant="bodyMedium">Total Tasks</Text>
              </Card.Content>
            </Card>

            <Card style={styles.statCard}>
              <Card.Content style={styles.statCardContent}>
                <Text variant="displaySmall" style={styles.statNumber}>
                  {myOrders.filter(o => o.priority === 'urgent' || o.priority === 'express').length}
                </Text>
                <Text variant="bodyMedium">Urgent</Text>
              </Card.Content>
            </Card>
          </View>

          <View style={styles.ordersSection}>
            <Text variant="titleLarge" style={styles.sectionTitle}>
              My Assigned Orders
            </Text>

            {myOrders.length === 0 ? (
              <Card style={styles.emptyCard}>
                <Card.Content style={styles.emptyCardContent}>
                  <Text variant="headlineSmall">No Tasks Assigned</Text>
                  <Text variant="bodyMedium">Check back later for new orders</Text>
                </Card.Content>
              </Card>
            ) : (
              myOrders.map((order) => (
                <Card
                  key={order.id}
                  style={styles.orderCard}
                  onPress={() => openBottomSheet('order', order)}
                >
                  <Card.Content>
                    <View style={styles.orderHeader}>
                      <Text variant="titleMedium">#{order.id}</Text>
                      <Chip mode="flat" style={{ backgroundColor: getPriorityColor(order.priority) + '20' }}>
                        {order.priority.toUpperCase()}
                      </Chip>
                    </View>
                    <Text variant="bodyLarge" style={styles.customerName}>
                      {order.customerName}
                    </Text>
                    <Text variant="bodyMedium">
                      {order.garmentType}
                    </Text>
                    <Text variant="bodySmall">
                      Due: {order.dueDate}
                    </Text>
                    <View style={styles.statusContainer}>
                      <Chip mode="flat" style={{ backgroundColor: getStatusColor(order.status) + '20' }}>
                        {order.status.replace('_', ' ').toUpperCase()}
                      </Chip>
                      <Button
                        mode="contained"
                        compact
                        onPress={() => updateOrderStatus(order.id, 'next_status')}
                      >
                        Update
                      </Button>
                    </View>
                  </Card.Content>
                </Card>
              ))
            )}
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="account-group"
              onPress={() => handleNavigation('customers')}
              style={styles.navButton}
            >
              Customers
            </Button>
            <Button
              mode="text"
              icon="hanger"
              onPress={() => handleNavigation('products')}
              style={styles.navButton}
            >
              Products
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Customers Screen (exactly like Manager App)
  if (currentScreen === 'customers') {
    const customerTypes = ['All', 'Active', 'New', 'VIP'];

    const handleAddCustomer = () => {
      console.log('Navigating to Add Customer page...');
      handleNavigation('addCustomer');
    };

    const handleEditCustomer = (customer) => {
      console.log('Editing customer:', customer.id);
      Alert.alert('Edit Customer', `Edit customer ${customer.name} functionality coming soon!`);
    };

    const handleViewOrders = (customer) => {
      console.log('Viewing orders for customer:', customer.id);
      Alert.alert('Customer Orders', `Viewing orders for ${customer.name}`);
    };

    const handleDeleteCustomer = (customer) => {
      Alert.alert(
        'Delete Customer',
        `Are you sure you want to delete customer ${customer.name}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: () => Alert.alert('Success', `Customer ${customer.name} deleted!`),
          },
        ]
      );
    };

    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <CommonHeader
          title="Customers"
          subtitle="Manage customer relationships"
          searchPlaceholder="Search customers..."
          searchType="customers"
          searchData={mockCustomers || []}
          searchFields={["name", "phone", "address"]}
          onSearchChange={setSearchQuery}
          onSearchResult={(customer) => {
            console.log('Customer selected from search:', customer);
            openBottomSheet('customer', customer);
          }}
          onNotificationPress={() => console.log('Notifications pressed')}
          onProfilePress={() => handleNavigation('profile')}
        />

        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
          ListHeaderComponent={() => (
            <View>
              {/* Customer Type Filter */}
              <UnifiedFilterChips
                filters={customerTypes}
                selectedFilter={selectedCustomerType}
                onFilterChange={setSelectedCustomerType}
                showCounts={true}
                data={mockCustomers || []}
                countField="type"
                style={{ marginBottom: 16 }}
              />
            </View>
          )}
        >
          <View style={{ paddingHorizontal: 16, paddingBottom: 100 }}>
            {filteredCustomers.length === 0 ? (
              <UnifiedEmptyState
                type="customers"
                searchQuery={searchQuery}
                onActionPress={handleAddCustomer}
              />
            ) : (
              filteredCustomers.map((customer) => (
                <TouchableOpacity
                  key={customer.id}
                  onPress={() => openBottomSheet('customer', customer)}
                >
                  <Surface
                    style={{
                      backgroundColor: theme.colors.surface,
                      borderRadius: 16,
                      marginBottom: 12,
                      borderWidth: 1,
                      borderColor: theme.colors.outline + '20',
                    }}
                    elevation={0}
                  >
                    <View style={{ padding: 16 }}>
                      <View style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'flex-start',
                        marginBottom: 8,
                      }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                          <Avatar.Text
                            size={48}
                            label={customer.name.charAt(0).toUpperCase()}
                            style={{
                              backgroundColor: theme.colors.primaryContainer,
                              marginRight: 12,
                            }}
                          />
                          <View style={{ flex: 1 }}>
                            <Text variant="titleMedium" style={{ fontWeight: '600', marginBottom: 2 }}>
                              {customer.name}
                            </Text>
                            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, marginBottom: 2 }}>
                              {customer.phone}
                            </Text>
                            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                              {customer.address}
                            </Text>
                          </View>
                        </View>
                        <View style={{ alignItems: 'flex-end' }}>
                          <Chip
                            mode="flat"
                            style={{
                              backgroundColor: customer.totalOrders >= 5 ? '#FFD700' + '20' : theme.colors.primaryContainer,
                              marginBottom: 4,
                            }}
                            textStyle={{ fontSize: 12 }}
                          >
                            {customer.totalOrders >= 5 ? 'VIP' : customer.totalOrders === 0 ? 'New' : 'Active'}
                          </Chip>
                          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                            {customer.totalOrders} orders
                          </Text>
                        </View>
                      </View>
                      <View style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}>
                        <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                          Member since: {new Date(customer.joinDate).toLocaleDateString()}
                        </Text>
                        <View style={{ flexDirection: 'row', gap: 8 }}>
                          <IconButton
                            icon="pencil-outline"
                            size={20}
                            iconColor={theme.colors.primary}
                            onPress={() => handleEditCustomer(customer)}
                            style={{ margin: 0 }}
                          />
                          <IconButton
                            icon="clipboard-list-outline"
                            size={20}
                            iconColor={theme.colors.secondary}
                            onPress={() => handleViewOrders(customer)}
                            style={{ margin: 0 }}
                          />
                          <IconButton
                            icon="delete-outline"
                            size={20}
                            iconColor={theme.colors.error}
                            onPress={() => handleDeleteCustomer(customer)}
                            style={{ margin: 0 }}
                          />
                        </View>
                      </View>
                    </View>
                  </Surface>
                </TouchableOpacity>
              ))
            )}
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="qrcode-scan"
              onPress={() => handleNavigation('scan')}
              style={styles.navButton}
            >
              Scan
            </Button>
            <Button
              mode="text"
              icon="plus-circle"
              onPress={() => handleNavigation('quickActions')}
              style={[styles.navButton, styles.plusButton]}
            >
              Add
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="cog"
              onPress={() => handleNavigation('settings')}
              style={styles.navButton}
            >
              Settings
            </Button>
          </View>
        </Surface>
      </View>
    );
  }

  // Products Screen
  if (currentScreen === 'products') {
    const products = getProducts();

    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content title="Products" />
          <Appbar.Action
            icon="plus"
            onPress={() => Alert.alert('Add Product', 'Add new product functionality coming soon!')}
          />
        </Appbar.Header>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <TextInput
            mode="outlined"
            label="Search products..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.searchInput}
            left={<TextInput.Icon icon="magnify" />}
            right={searchQuery ? <TextInput.Icon icon="close" onPress={() => setSearchQuery('')} /> : null}
          />
        </View>

        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        >
          <View style={styles.ordersSection}>
            {products.length === 0 ? (
              <Card style={styles.emptyCard}>
                <Card.Content style={styles.emptyCardContent}>
                  <Text variant="headlineSmall">No Products Found</Text>
                  <Text variant="bodyMedium">Try adjusting your search</Text>
                </Card.Content>
              </Card>
            ) : (
              products.map((product) => (
                <Card
                  key={product.id}
                  style={styles.orderCard}
                  onPress={() => Alert.alert('Product Details', `${product.name}\n${product.description}\nPrice: ৳${product.price}`)}
                >
                  <Card.Content>
                    <View style={styles.orderHeader}>
                      <View style={styles.productHeader}>
                        <Avatar.Icon
                          size={40}
                          icon="hanger"
                          style={{ backgroundColor: theme.colors.tertiaryContainer }}
                        />
                        <View style={styles.productInfo}>
                          <Text variant="titleMedium">{product.name}</Text>
                          <Text variant="bodySmall">{product.category}</Text>
                        </View>
                      </View>
                      <View style={styles.productPrice}>
                        <Text variant="titleMedium" style={styles.priceText}>৳{product.price}</Text>
                        <Chip mode="flat" style={{ backgroundColor: product.inStock ? '#4CAF50' : '#F44336' }}>
                          {product.inStock ? 'In Stock' : 'Out of Stock'}
                        </Chip>
                      </View>
                    </View>
                    <Text variant="bodyMedium" style={styles.productDescription}>
                      {product.description}
                    </Text>
                    <Text variant="bodySmall">
                      Fabric: {product.fabric} • Colors: {product.colors.join(', ')}
                    </Text>
                  </Card.Content>
                </Card>
              ))
            )}
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="account-group"
              onPress={() => handleNavigation('customers')}
              style={styles.navButton}
            >
              Customers
            </Button>
            <Button
              mode="text"
              icon="hanger"
              onPress={() => handleNavigation('products')}
              style={[styles.navButton, currentScreen === 'products' && styles.activeNavButton]}
            >
              Products
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Scan Screen
  if (currentScreen === 'scan') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content title="QR Scanner" />
          <Appbar.Action
            icon="flashlight"
            onPress={() => Alert.alert('Flash', 'Flashlight toggle coming soon!')}
          />
        </Appbar.Header>

        <ScrollView style={styles.content}>
          <View style={styles.scanContainer}>
            <Card style={styles.scanCard}>
              <Card.Content style={styles.scanCardContent}>
                <Avatar.Icon
                  size={80}
                  icon="qrcode-scan"
                  style={{ backgroundColor: theme.colors.primaryContainer }}
                />
                <Text variant="headlineSmall" style={styles.scanTitle}>
                  QR Code Scanner
                </Text>
                <Text variant="bodyMedium" style={styles.scanSubtitle}>
                  Point camera at order QR code to quickly access order details
                </Text>

                <Button
                  mode="contained"
                  onPress={() => {
                    // Simulate scanning an order
                    const randomOrder = mockOrders[Math.floor(Math.random() * mockOrders.length)];
                    openBottomSheet('order', randomOrder);
                  }}
                  style={styles.scanButton}
                  icon="camera"
                >
                  Start Scanning
                </Button>

                <Button
                  mode="outlined"
                  onPress={() => {
                    Alert.alert('Demo Scan', 'Simulating QR scan...', [
                      { text: 'Cancel' },
                      {
                        text: 'Scan Order',
                        onPress: () => {
                          const randomOrder = mockOrders[Math.floor(Math.random() * mockOrders.length)];
                          openBottomSheet('order', randomOrder);
                        }
                      }
                    ]);
                  }}
                  style={styles.demoScanButton}
                  icon="play"
                >
                  Demo Scan
                </Button>
              </Card.Content>
            </Card>

            {/* Recent Scans */}
            <View style={styles.recentScansContainer}>
              <Text variant="titleLarge" style={styles.sectionTitle}>
                Recent Scans
              </Text>

              {mockOrders.slice(0, 3).map((order) => (
                <Card
                  key={order.id}
                  style={styles.recentScanCard}
                  onPress={() => openBottomSheet('order', order)}
                >
                  <Card.Content>
                    <View style={styles.recentScanHeader}>
                      <Text variant="titleMedium">#{order.id}</Text>
                      <Text variant="bodySmall">{order.orderDate}</Text>
                    </View>
                    <Text variant="bodyMedium">{order.customerName}</Text>
                    <Text variant="bodySmall">{order.garmentType}</Text>
                  </Card.Content>
                </Card>
              ))}
            </View>
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="qrcode-scan"
              onPress={() => handleNavigation('scan')}
              style={[styles.navButton, currentScreen === 'scan' && styles.activeNavButton]}
            >
              Scan
            </Button>
            <Button
              mode="text"
              icon="plus-circle"
              onPress={() => handleNavigation('quickActions')}
              style={[styles.navButton, styles.plusButton]}
            >
              Add
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="cog"
              onPress={() => handleNavigation('settings')}
              style={styles.navButton}
            >
              Settings
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Quick Actions Screen
  if (currentScreen === 'quickActions') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content title="Quick Actions" />
        </Appbar.Header>

        <ScrollView style={styles.content}>
          <View style={styles.quickActionsContainer}>
            <Text variant="titleLarge" style={styles.sectionTitle}>
              Create New
            </Text>

            <View style={styles.quickActionsGrid}>
              <Card style={styles.quickActionCard} onPress={() => handleNavigation('addOrder')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="plus" style={{ backgroundColor: theme.colors.primaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>New Order</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>Create tailoring order</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('addCustomer')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="account-plus" style={{ backgroundColor: theme.colors.secondaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Add Customer</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>New customer profile</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('addProduct')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="hanger" style={{ backgroundColor: theme.colors.tertiaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Add Product</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>New garment type</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('addEmployee')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="account-tie" style={{ backgroundColor: theme.colors.errorContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Add Employee</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>New staff member</Text>
                </Card.Content>
              </Card>
            </View>

            <Text variant="titleLarge" style={[styles.sectionTitle, { marginTop: 24 }]}>
              Management
            </Text>

            <View style={styles.quickActionsGrid}>
              <Card style={styles.quickActionCard} onPress={() => handleNavigation('employees')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="account-group" style={{ backgroundColor: theme.colors.primaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Employees</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>Manage staff</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('measurements')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="ruler" style={{ backgroundColor: theme.colors.secondaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Measurements</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>Size management</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('reports')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="chart-line" style={{ backgroundColor: theme.colors.tertiaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Reports</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>Business insights</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('search')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="magnify" style={{ backgroundColor: theme.colors.errorContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Search</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>Find anything</Text>
                </Card.Content>
              </Card>
            </View>
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="qrcode-scan"
              onPress={() => handleNavigation('scan')}
              style={styles.navButton}
            >
              Scan
            </Button>
            <Button
              mode="text"
              icon="plus-circle"
              onPress={() => handleNavigation('quickActions')}
              style={[styles.navButton, styles.plusButton, currentScreen === 'quickActions' && styles.activeNavButton]}
            >
              Add
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="cog"
              onPress={() => handleNavigation('settings')}
              style={styles.navButton}
            >
              Settings
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Comprehensive Settings Screen
  if (currentScreen === 'settings') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content title="Settings" />
        </Appbar.Header>

        <ScrollView style={styles.content}>
          <View style={styles.settingsContainer}>

            {/* App Preferences */}
            <View style={styles.settingsSection}>
              <Text variant="titleMedium" style={styles.settingsSectionTitle}>
                App Preferences
              </Text>

              <Card style={styles.settingsCard}>
                <Card.Content>
                  <View style={styles.settingItem}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="theme-light-dark" style={{ backgroundColor: theme.colors.primaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Dark Mode</Text>
                        <Text variant="bodySmall">Switch between light and dark themes</Text>
                      </View>
                    </View>
                    <Switch
                      value={isDarkMode}
                      onValueChange={setIsDarkMode}
                    />
                  </View>
                </Card.Content>
              </Card>
            </View>

            {/* Business Management */}
            <View style={styles.settingsSection}>
              <Text variant="titleMedium" style={styles.settingsSectionTitle}>
                Business Management
              </Text>

              <Card style={styles.settingsCard}>
                <Card.Content>
                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('customers')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="account-group" style={{ backgroundColor: theme.colors.secondaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Customers</Text>
                        <Text variant="bodySmall">Manage customer information</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('employees')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="account-tie" style={{ backgroundColor: theme.colors.tertiaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Staff Management</Text>
                        <Text variant="bodySmall">Manage employees and assignments</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('products')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="hanger" style={{ backgroundColor: theme.colors.errorContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Products</Text>
                        <Text variant="bodySmall">Manage garment catalog</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>
                </Card.Content>
              </Card>
            </View>

            {/* Account & Profile */}
            <View style={styles.settingsSection}>
              <Text variant="titleMedium" style={styles.settingsSectionTitle}>
                Account & Profile
              </Text>

              <Card style={styles.settingsCard}>
                <Card.Content>
                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('profile')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Text size={40} label={employee?.avatar || 'U'} style={{ backgroundColor: theme.colors.primaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">My Profile</Text>
                        <Text variant="bodySmall">View and edit your profile</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('editProfile')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="account-edit" style={{ backgroundColor: theme.colors.secondaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Edit Profile</Text>
                        <Text variant="bodySmall">Update your information</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('security')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="shield-lock" style={{ backgroundColor: theme.colors.tertiaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Security Settings</Text>
                        <Text variant="bodySmall">Password and security options</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>
                </Card.Content>
              </Card>
            </View>

            {/* Data & Reports */}
            <View style={styles.settingsSection}>
              <Text variant="titleMedium" style={styles.settingsSectionTitle}>
                Data & Reports
              </Text>

              <Card style={styles.settingsCard}>
                <Card.Content>
                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('reports')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="chart-line" style={{ backgroundColor: theme.colors.primaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Reports</Text>
                        <Text variant="bodySmall">Business analytics and insights</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('importData')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="database-import" style={{ backgroundColor: theme.colors.secondaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Data Backup</Text>
                        <Text variant="bodySmall">Import and export data</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('activityLog')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="history" style={{ backgroundColor: theme.colors.tertiaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Activity Log</Text>
                        <Text variant="bodySmall">View system activity</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>
                </Card.Content>
              </Card>
            </View>

            {/* Support */}
            <View style={styles.settingsSection}>
              <Text variant="titleMedium" style={styles.settingsSectionTitle}>
                Support
              </Text>

              <Card style={styles.settingsCard}>
                <Card.Content>
                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('help')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="help-circle" style={{ backgroundColor: theme.colors.primaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Help & FAQ</Text>
                        <Text variant="bodySmall">Get help and find answers</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('contact')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="email" style={{ backgroundColor: theme.colors.secondaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">Contact Support</Text>
                        <Text variant="bodySmall">Get in touch with our team</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.settingItem} onPress={() => handleNavigation('about')}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="information" style={{ backgroundColor: theme.colors.tertiaryContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium">About</Text>
                        <Text variant="bodySmall">App version and information</Text>
                      </View>
                    </View>
                    <Avatar.Icon size={24} icon="chevron-right" style={{ backgroundColor: 'transparent' }} />
                  </TouchableOpacity>
                </Card.Content>
              </Card>
            </View>

            {/* Logout */}
            <View style={styles.settingsSection}>
              <Card style={styles.settingsCard}>
                <Card.Content>
                  <TouchableOpacity style={styles.settingItem} onPress={handleLogout}>
                    <View style={styles.settingItemLeft}>
                      <Avatar.Icon size={40} icon="logout" style={{ backgroundColor: theme.colors.errorContainer }} />
                      <View style={styles.settingItemText}>
                        <Text variant="titleMedium" style={{ color: theme.colors.error }}>Logout</Text>
                        <Text variant="bodySmall">Sign out of your account</Text>
                      </View>
                    </View>
                  </TouchableOpacity>
                </Card.Content>
              </Card>
            </View>

            {/* Footer */}
            <View style={styles.settingsFooter}>
              <Text variant="bodySmall" style={styles.footerText}>
                Elite Tailor Employee App v1.0.0
              </Text>
              <Text variant="bodySmall" style={styles.footerText}>
                Made with ❤️ for tailor shop employees
              </Text>
            </View>
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="qrcode-scan"
              onPress={() => handleNavigation('scan')}
              style={styles.navButton}
            >
              Scan
            </Button>
            <Button
              mode="text"
              icon="plus-circle"
              onPress={() => handleNavigation('quickActions')}
              style={[styles.navButton, styles.plusButton]}
            >
              Add
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="cog"
              onPress={() => handleNavigation('settings')}
              style={[styles.navButton, currentScreen === 'settings' && styles.activeNavButton]}
            >
              Settings
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Scan Screen
  if (currentScreen === 'scan') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content title="QR Scanner" />
          <Appbar.Action
            icon="flashlight"
            onPress={() => Alert.alert('Flash', 'Flashlight toggle coming soon!')}
          />
        </Appbar.Header>

        <ScrollView style={styles.content}>
          <View style={styles.scanContainer}>
            <Card style={styles.scanCard}>
              <Card.Content style={styles.scanCardContent}>
                <Avatar.Icon
                  size={80}
                  icon="qrcode-scan"
                  style={{ backgroundColor: theme.colors.primaryContainer }}
                />
                <Text variant="headlineSmall" style={styles.scanTitle}>
                  QR Code Scanner
                </Text>
                <Text variant="bodyMedium" style={styles.scanSubtitle}>
                  Point camera at order QR code to quickly access order details
                </Text>

                <Button
                  mode="contained"
                  onPress={() => {
                    // Simulate scanning an order
                    const randomOrder = mockOrders[Math.floor(Math.random() * mockOrders.length)];
                    openBottomSheet('order', randomOrder);
                  }}
                  style={styles.scanButton}
                  icon="camera"
                >
                  Start Scanning
                </Button>

                <Button
                  mode="outlined"
                  onPress={() => {
                    Alert.alert('Demo Scan', 'Simulating QR scan...', [
                      { text: 'Cancel' },
                      {
                        text: 'Scan Order',
                        onPress: () => {
                          const randomOrder = mockOrders[Math.floor(Math.random() * mockOrders.length)];
                          openBottomSheet('order', randomOrder);
                        }
                      }
                    ]);
                  }}
                  style={styles.demoScanButton}
                  icon="play"
                >
                  Demo Scan
                </Button>
              </Card.Content>
            </Card>

            {/* Recent Scans */}
            <View style={styles.recentScansContainer}>
              <Text variant="titleLarge" style={styles.sectionTitle}>
                Recent Scans
              </Text>

              {mockOrders.slice(0, 3).map((order) => (
                <Card
                  key={order.id}
                  style={styles.recentScanCard}
                  onPress={() => openBottomSheet('order', order)}
                >
                  <Card.Content>
                    <View style={styles.recentScanHeader}>
                      <Text variant="titleMedium">#{order.id}</Text>
                      <Text variant="bodySmall">{order.orderDate}</Text>
                    </View>
                    <Text variant="bodyMedium">{order.customerName}</Text>
                    <Text variant="bodySmall">{order.garmentType}</Text>
                  </Card.Content>
                </Card>
              ))}
            </View>
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="qrcode-scan"
              onPress={() => handleNavigation('scan')}
              style={[styles.navButton, currentScreen === 'scan' && styles.activeNavButton]}
            >
              Scan
            </Button>
            <Button
              mode="text"
              icon="plus-circle"
              onPress={() => handleNavigation('quickActions')}
              style={[styles.navButton, styles.plusButton]}
            >
              Add
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="cog"
              onPress={() => handleNavigation('settings')}
              style={styles.navButton}
            >
              Settings
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Quick Actions Screen
  if (currentScreen === 'quickActions') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.Content title="Quick Actions" />
        </Appbar.Header>

        <ScrollView style={styles.content}>
          <View style={styles.quickActionsContainer}>
            <Text variant="titleLarge" style={styles.sectionTitle}>
              Create New
            </Text>

            <View style={styles.quickActionsGrid}>
              <Card style={styles.quickActionCard} onPress={() => handleNavigation('addOrder')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="plus" style={{ backgroundColor: theme.colors.primaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>New Order</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>Create tailoring order</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('addCustomer')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="account-plus" style={{ backgroundColor: theme.colors.secondaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Add Customer</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>New customer profile</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('addProduct')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="hanger" style={{ backgroundColor: theme.colors.tertiaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Add Product</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>New garment type</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('addEmployee')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="account-tie" style={{ backgroundColor: theme.colors.errorContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Add Employee</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>New staff member</Text>
                </Card.Content>
              </Card>
            </View>

            <Text variant="titleLarge" style={[styles.sectionTitle, { marginTop: 24 }]}>
              Management
            </Text>

            <View style={styles.quickActionsGrid}>
              <Card style={styles.quickActionCard} onPress={() => handleNavigation('products')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="hanger" style={{ backgroundColor: theme.colors.primaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Products</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>View inventory</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('settings')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="cog" style={{ backgroundColor: theme.colors.secondaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Settings</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>App preferences</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('importData')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="database-import" style={{ backgroundColor: theme.colors.tertiaryContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Data Backup</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>Import/Export</Text>
                </Card.Content>
              </Card>

              <Card style={styles.quickActionCard} onPress={() => handleNavigation('help')}>
                <Card.Content style={styles.quickActionContent}>
                  <Avatar.Icon size={48} icon="help-circle" style={{ backgroundColor: theme.colors.errorContainer }} />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>Help</Text>
                  <Text variant="bodySmall" style={styles.quickActionSubtitle}>FAQ & Support</Text>
                </Card.Content>
              </Card>
            </View>
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface style={styles.bottomNav} elevation={3}>
          <View style={styles.navContainer}>
            <Button
              mode="text"
              icon="view-dashboard"
              onPress={() => handleNavigation('dashboard')}
              style={styles.navButton}
            >
              Dashboard
            </Button>
            <Button
              mode="text"
              icon="qrcode-scan"
              onPress={() => handleNavigation('scan')}
              style={styles.navButton}
            >
              Scan
            </Button>
            <Button
              mode="text"
              icon="plus-circle"
              onPress={() => handleNavigation('quickActions')}
              style={[styles.navButton, styles.plusButton, currentScreen === 'quickActions' && styles.activeNavButton]}
            >
              Add
            </Button>
            <Button
              mode="text"
              icon="clipboard-list"
              onPress={() => handleNavigation('orders')}
              style={styles.navButton}
            >
              Orders
            </Button>
            <Button
              mode="text"
              icon="cog"
              onPress={() => handleNavigation('settings')}
              style={styles.navButton}
            >
              Settings
            </Button>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  // Add Order Screen
  if (currentScreen === 'addOrder') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <StatusBar style="auto" />

        <Appbar.Header>
          <Appbar.BackAction onPress={() => handleNavigation('quickActions')} />
          <Appbar.Content title="New Order" />
          <Appbar.Action icon="check" onPress={() => {
            Alert.alert('Success', 'Order created successfully!', [
              { text: 'OK', onPress: () => handleNavigation('orders') }
            ]);
          }} />
        </Appbar.Header>

        <ScrollView style={styles.content}>
          <View style={styles.formContainer}>

            {/* Customer Selection */}
            <Card style={styles.formCard}>
              <Card.Content>
                <Text variant="titleMedium" style={styles.formSectionTitle}>Customer Information</Text>
                <TextInput
                  mode="outlined"
                  label="Customer Name"
                  placeholder="Select or enter customer name"
                  style={styles.formInput}
                  right={<TextInput.Icon icon="account-plus" onPress={() => handleNavigation('addCustomer')} />}
                />
                <TextInput
                  mode="outlined"
                  label="Phone Number"
                  placeholder="Customer phone number"
                  style={styles.formInput}
                  keyboardType="phone-pad"
                />
              </Card.Content>
            </Card>

            {/* Garment Details */}
            <Card style={styles.formCard}>
              <Card.Content>
                <Text variant="titleMedium" style={styles.formSectionTitle}>Garment Details</Text>
                <TextInput
                  mode="outlined"
                  label="Garment Type"
                  placeholder="e.g., Shirt, Pants, Suit"
                  style={styles.formInput}
                />
                <TextInput
                  mode="outlined"
                  label="Fabric"
                  placeholder="e.g., Cotton, Silk, Wool"
                  style={styles.formInput}
                />
                <TextInput
                  mode="outlined"
                  label="Color"
                  placeholder="Fabric color"
                  style={styles.formInput}
                />
                <View style={styles.formRow}>
                  <TextInput
                    mode="outlined"
                    label="Quantity"
                    placeholder="1"
                    style={[styles.formInput, { flex: 1, marginRight: 8 }]}
                    keyboardType="numeric"
                  />
                  <TextInput
                    mode="outlined"
                    label="Size"
                    placeholder="e.g., M, L, XL"
                    style={[styles.formInput, { flex: 1, marginLeft: 8 }]}
                  />
                </View>
              </Card.Content>
            </Card>

            {/* Measurements */}
            <Card style={styles.formCard}>
              <Card.Content>
                <Text variant="titleMedium" style={styles.formSectionTitle}>Measurements</Text>
                <View style={styles.formRow}>
                  <TextInput
                    mode="outlined"
                    label="Chest/Bust"
                    placeholder="inches"
                    style={[styles.formInput, { flex: 1, marginRight: 8 }]}
                    keyboardType="numeric"
                  />
                  <TextInput
                    mode="outlined"
                    label="Waist"
                    placeholder="inches"
                    style={[styles.formInput, { flex: 1, marginLeft: 8 }]}
                  />
                </View>
                <View style={styles.formRow}>
                  <TextInput
                    mode="outlined"
                    label="Length"
                    placeholder="inches"
                    style={[styles.formInput, { flex: 1, marginRight: 8 }]}
                    keyboardType="numeric"
                  />
                  <TextInput
                    mode="outlined"
                    label="Sleeve"
                    placeholder="inches"
                    style={[styles.formInput, { flex: 1, marginLeft: 8 }]}
                  />
                </View>
                <Button
                  mode="outlined"
                  onPress={() => Alert.alert('Measurements', 'Full measurement form coming soon!')}
                  style={styles.measurementButton}
                  icon="ruler"
                >
                  Full Measurements
                </Button>
              </Card.Content>
            </Card>

            {/* Pricing & Timeline */}
            <Card style={styles.formCard}>
              <Card.Content>
                <Text variant="titleMedium" style={styles.formSectionTitle}>Pricing & Timeline</Text>
                <View style={styles.formRow}>
                  <TextInput
                    mode="outlined"
                    label="Estimated Price"
                    placeholder="৳0"
                    style={[styles.formInput, { flex: 1, marginRight: 8 }]}
                    keyboardType="numeric"
                  />
                  <TextInput
                    mode="outlined"
                    label="Advance Payment"
                    placeholder="৳0"
                    style={[styles.formInput, { flex: 1, marginLeft: 8 }]}
                    keyboardType="numeric"
                  />
                </View>
                <TextInput
                  mode="outlined"
                  label="Due Date"
                  placeholder="Select delivery date"
                  style={styles.formInput}
                  right={<TextInput.Icon icon="calendar" />}
                />
                <View style={styles.priorityContainer}>
                  <Text variant="bodyMedium" style={styles.priorityLabel}>Priority:</Text>
                  <View style={styles.priorityChips}>
                    <Chip mode="outlined" selected style={styles.priorityChip}>Normal</Chip>
                    <Chip mode="outlined" style={styles.priorityChip}>Urgent</Chip>
                    <Chip mode="outlined" style={styles.priorityChip}>Express</Chip>
                  </View>
                </View>
              </Card.Content>
            </Card>

            {/* Notes */}
            <Card style={styles.formCard}>
              <Card.Content>
                <Text variant="titleMedium" style={styles.formSectionTitle}>Additional Notes</Text>
                <TextInput
                  mode="outlined"
                  label="Special Instructions"
                  placeholder="Any special requirements or notes..."
                  style={styles.formInput}
                  multiline
                  numberOfLines={3}
                />
              </Card.Content>
            </Card>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <Button
                mode="outlined"
                onPress={() => handleNavigation('quickActions')}
                style={[styles.actionButton, { marginRight: 8 }]}
              >
                Cancel
              </Button>
              <Button
                mode="contained"
                onPress={() => {
                  Alert.alert('Success', 'Order created successfully!', [
                    { text: 'OK', onPress: () => handleNavigation('orders') }
                  ]);
                }}
                style={[styles.actionButton, { marginLeft: 8 }]}
                icon="check"
              >
                Create Order
              </Button>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }

  // Products Screen (exactly like Manager App)
  if (currentScreen === 'products') {
    const categories = ['All', 'Suits', 'Dresses', 'Shirts', 'Pants', 'Accessories'];
    const [selectedCategory, setSelectedCategory] = useState('All');

    // Filter products exactly like Manager App
    const filteredProducts = useMemo(() => {
      const safeProducts = mockProducts || [];
      return safeProducts.filter(product => {
        const matchesSearch = searchQuery === '' ||
          product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.description.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesCategory = selectedCategory === 'All' || product.category === selectedCategory;

        return matchesSearch && matchesCategory;
      });
    }, [mockProducts, searchQuery, selectedCategory]);

    // Product stats exactly like Manager App
    const productStats = useMemo(() => ({
      total: filteredProducts.length,
      totalStock: filteredProducts.reduce((sum, product) => sum + (product.stock || 0), 0),
      inStock: filteredProducts.filter(p => (p.stock || 0) > 0).length,
      outOfStock: filteredProducts.filter(p => (p.stock || 0) === 0).length,
    }), [filteredProducts]);

    const handleAddProduct = () => {
      console.log('Navigating to Add Product page...');
      Alert.alert('Add Product', 'Add Product functionality coming soon!');
    };

    const handleEditProduct = (product) => {
      console.log('Editing product:', product.id);
      Alert.alert('Edit Product', `Edit product ${product.name} functionality coming soon!`);
    };

    const handleDeleteProduct = (product) => {
      Alert.alert(
        'Delete Product',
        `Are you sure you want to delete ${product.name}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: () => Alert.alert('Success', `Product ${product.name} deleted!`),
          },
        ]
      );
    };

    const handleViewDetails = (product) => {
      console.log('Viewing product details:', product.id);
      Alert.alert('Product Details', `View details for ${product.name} functionality coming soon!`);
    };

    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <CommonHeader
          title="Products"
          subtitle="Manage your inventory"
          searchPlaceholder="Search products..."
          onSearchChange={setSearchQuery}
          onNotificationPress={() => console.log('Notifications pressed')}
          onProfilePress={() => handleNavigation('profile')}
        />

        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
        >
          <View style={{ padding: 16 }}>
            {/* Category Filter */}
            <UnifiedFilterChips
              filters={categories}
              selectedFilter={selectedCategory}
              onFilterChange={setSelectedCategory}
              showCounts={true}
              data={mockProducts || []}
              countField="category"
              style={{ marginBottom: 16 }}
            />

            {/* Stats Cards */}
            <StatCardGroup
              cards={[
                {
                  key: 'total',
                  title: 'Products',
                  value: productStats.total.toString(),
                  icon: 'hanger',
                  iconColor: theme.colors.primary,
                },
                {
                  key: 'stock',
                  title: 'Total Stock',
                  value: productStats.totalStock.toString(),
                  icon: 'package-variant',
                  iconColor: theme.colors.secondary,
                },
              ]}
              columns={2}
              showTitle={false}
              containerStyle={{ marginBottom: 24 }}
            />

            {/* Products List */}
            {filteredProducts.length > 0 ? (
              <View>
                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: 16,
                }}>
                  <Text variant="titleLarge" style={{
                    color: theme.colors.onBackground,
                    fontWeight: '600',
                  }}>
                    Products ({filteredProducts.length})
                  </Text>
                  <Button
                    mode="outlined"
                    onPress={handleAddProduct}
                    icon="plus"
                    compact
                  >
                    Add Product
                  </Button>
                </View>

                {filteredProducts.map((product, index) => (
                  <TouchableOpacity
                    key={product.id}
                    onPress={() => handleViewDetails(product)}
                  >
                    <Surface
                      style={{
                        backgroundColor: theme.colors.surface,
                        borderRadius: 16,
                        marginBottom: 12,
                        borderWidth: 1,
                        borderColor: theme.colors.outline + '20',
                      }}
                      elevation={0}
                    >
                      <View style={{ padding: 16 }}>
                        <View style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignItems: 'flex-start',
                          marginBottom: 12,
                        }}>
                          <View style={{ flex: 1, marginRight: 12 }}>
                            <Text variant="titleMedium" style={{ fontWeight: '600', marginBottom: 4 }}>
                              {product.name}
                            </Text>
                            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, marginBottom: 4 }}>
                              {product.category} • {product.fabric}
                            </Text>
                            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginBottom: 8 }}>
                              {product.description}
                            </Text>
                            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 12 }}>
                              <Text variant="titleLarge" style={{ fontWeight: '700', color: theme.colors.primary }}>
                                ৳{product.price}
                              </Text>
                              <Chip
                                mode="flat"
                                style={{
                                  backgroundColor: product.inStock ? theme.colors.primary + '20' : theme.colors.error + '20',
                                }}
                                textStyle={{ fontSize: 12 }}
                              >
                                {product.inStock ? 'In Stock' : 'Out of Stock'}
                              </Chip>
                            </View>
                          </View>
                        </View>

                        {/* Action Buttons */}
                        <View style={{
                          flexDirection: 'row',
                          justifyContent: 'flex-end',
                          gap: 8,
                        }}>
                          <Button
                            mode="outlined"
                            onPress={() => handleEditProduct(product)}
                            compact
                            style={{ borderRadius: 20 }}
                          >
                            Edit
                          </Button>
                          <Button
                            mode="contained"
                            onPress={() => handleViewDetails(product)}
                            compact
                            style={{ borderRadius: 20 }}
                          >
                            View Details
                          </Button>
                          <IconButton
                            icon="delete-outline"
                            size={20}
                            iconColor={theme.colors.error}
                            onPress={() => handleDeleteProduct(product)}
                            style={{ margin: 0 }}
                          />
                        </View>
                      </View>
                    </Surface>
                  </TouchableOpacity>
                ))}
              </View>
            ) : (
              <UnifiedEmptyState
                type="products"
                searchQuery={searchQuery}
                onActionPress={handleAddProduct}
              />
            )}
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface
          style={[
            styles.bottomNav,
            {
              backgroundColor: theme.colors.surface,
              borderTopColor: theme.colors.outline + '20'
            }
          ]}
          elevation={8}
        >
          <TouchableOpacity
            style={styles.navButton}
            onPress={() => handleNavigation('dashboard')}
          >
            <Icon
              name="view-dashboard"
              size={24}
              color={currentScreen === 'dashboard' ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
            <Text
              variant="labelSmall"
              style={{
                color: currentScreen === 'dashboard' ? theme.colors.primary : theme.colors.onSurfaceVariant,
                marginTop: 2,
                fontWeight: currentScreen === 'dashboard' ? '600' : '400',
                fontSize: 10
              }}
            >
              Dashboard
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navButton}
            onPress={() => handleNavigation('scan')}
          >
            <Icon
              name="qrcode-scan"
              size={24}
              color={currentScreen === 'scan' ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
            <Text
              variant="labelSmall"
              style={{
                color: currentScreen === 'scan' ? theme.colors.primary : theme.colors.onSurfaceVariant,
                marginTop: 2,
                fontWeight: currentScreen === 'scan' ? '600' : '400',
                fontSize: 10
              }}
            >
              Scan
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.plusButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => handleNavigation('quickActions')}
          >
            <Icon
              name="plus"
              size={24}
              color={theme.colors.onPrimary}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navButton}
            onPress={() => handleNavigation('orders')}
          >
            <Icon
              name="clipboard-list"
              size={24}
              color={currentScreen === 'orders' ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
            <Text
              variant="labelSmall"
              style={{
                color: currentScreen === 'orders' ? theme.colors.primary : theme.colors.onSurfaceVariant,
                marginTop: 2,
                fontWeight: currentScreen === 'orders' ? '600' : '400',
                fontSize: 10
              }}
            >
              Orders
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navButton}
            onPress={() => handleNavigation('settings')}
          >
            <Icon
              name="cog"
              size={24}
              color={currentScreen === 'products' ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
            <Text
              variant="labelSmall"
              style={{
                color: currentScreen === 'products' ? theme.colors.primary : theme.colors.onSurfaceVariant,
                marginTop: 2,
                fontWeight: currentScreen === 'products' ? '600' : '400',
                fontSize: 10
              }}
            >
              Settings
            </Text>
          </TouchableOpacity>
        </Surface>
      </View>
    );
  }

  // Settings Screen (exactly like Manager App)
  if (currentScreen === 'settings') {
    const SettingsSection = ({ title, children }) => (
      <View style={{ marginBottom: 24 }}>
        <Text variant="titleLarge" style={{
          color: theme.colors.onBackground,
          fontWeight: '700',
          marginBottom: 12,
        }}>
          {title}
        </Text>
        <Surface style={{
          backgroundColor: theme.colors.surface,
          borderRadius: 16,
          borderWidth: 1,
          borderColor: theme.colors.outline + '20',
          overflow: 'hidden',
        }} elevation={0}>
          {children}
        </Surface>
      </View>
    );

    const SettingItem = ({ icon, title, subtitle, rightComponent, onPress, iconColor }) => (
      <TouchableOpacity
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingVertical: 16,
          paddingHorizontal: 16,
        }}
        onPress={onPress}
      >
        <View style={{
          width: 32,
          height: 32,
          borderRadius: 8,
          backgroundColor: (iconColor || theme.colors.primary) + '15',
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: 12,
        }}>
          <Icon name={icon} size={20} color={iconColor || theme.colors.primary} />
        </View>
        <View style={{ flex: 1 }}>
          <Text variant="titleMedium" style={{ fontWeight: '600', color: theme.colors.onSurface }}>
            {title}
          </Text>
          {subtitle && (
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 2 }}>
              {subtitle}
            </Text>
          )}
        </View>
        {rightComponent || <Icon name="chevron-right" size={20} color={theme.colors.onSurfaceVariant} />}
      </TouchableOpacity>
    );

    const handleSettingChange = (setting, value) => {
      switch (setting) {
        case 'darkMode':
          setIsDarkMode(value);
          Alert.alert('Theme Changed', `Switched to ${value ? 'dark' : 'light'} mode!`);
          break;
        default:
          break;
      }
    };

    const handleHelpFAQ = () => {
      Alert.alert('Help & FAQ', 'Help and FAQ functionality coming soon!');
    };

    const handleContactSupport = () => {
      Alert.alert('Contact Support', 'Contact support functionality coming soon!');
    };

    const handleAbout = () => {
      Alert.alert('About', 'Elite Tailor Employee App v1.0.0\n\nMade with ❤️ for tailor shop employees');
    };

    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <CommonHeader
          title="Settings"
          subtitle="App preferences"
          showSearch={false}
          onNotificationPress={() => console.log('Notifications pressed')}
          onProfilePress={() => handleNavigation('profile')}
        />

        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
        >
          <View style={{ padding: 16 }}>
            {/* App Preferences */}
            <SettingsSection title="App Preferences">
              <SettingItem
                icon="theme-light-dark"
                title="Dark Mode"
                subtitle="Switch between light and dark themes"
                rightComponent={
                  <Switch
                    value={isDarkMode}
                    onValueChange={(value) => handleSettingChange('darkMode', value)}
                    color={theme.colors.primary}
                  />
                }
              />
            </SettingsSection>

            {/* Business Management */}
            <SettingsSection title="Business Management">
              <SettingItem
                icon="account-group"
                title="Customers"
                subtitle="Manage customer information"
                onPress={() => handleNavigation('customers')}
              />
              <View style={{ height: 1, backgroundColor: theme.colors.outline + '20', marginHorizontal: 16 }} />
              <SettingItem
                icon="hanger"
                title="Products"
                subtitle="Manage inventory and products"
                onPress={() => handleNavigation('products')}
              />
              <View style={{ height: 1, backgroundColor: theme.colors.outline + '20', marginHorizontal: 16 }} />
              <SettingItem
                icon="clipboard-list"
                title="Orders"
                subtitle="View and manage orders"
                onPress={() => handleNavigation('orders')}
              />
            </SettingsSection>

            {/* Employee Features */}
            <SettingsSection title="Employee Features">
              <SettingItem
                icon="account-hard-hat"
                title="My Profile"
                subtitle="View and edit your profile"
                onPress={() => handleNavigation('profile')}
              />
              <View style={{ height: 1, backgroundColor: theme.colors.outline + '20', marginHorizontal: 16 }} />
              <SettingItem
                icon="clipboard-check"
                title="My Tasks"
                subtitle="View assigned orders and tasks"
                onPress={() => Alert.alert('My Tasks', 'My Tasks functionality coming soon!')}
              />
              <View style={{ height: 1, backgroundColor: theme.colors.outline + '20', marginHorizontal: 16 }} />
              <SettingItem
                icon="chart-line"
                title="Performance"
                subtitle="View your work statistics"
                onPress={() => Alert.alert('Performance', 'Performance tracking coming soon!')}
              />
            </SettingsSection>

            {/* Support */}
            <SettingsSection title="Support">
              <SettingItem
                icon="help-circle"
                title="Help & FAQ"
                subtitle="Get help and find answers"
                onPress={handleHelpFAQ}
              />
              <View style={{ height: 1, backgroundColor: theme.colors.outline + '20', marginHorizontal: 16 }} />
              <SettingItem
                icon="email"
                title="Contact Support"
                subtitle="Get in touch with our team"
                onPress={handleContactSupport}
              />
              <View style={{ height: 1, backgroundColor: theme.colors.outline + '20', marginHorizontal: 16 }} />
              <SettingItem
                icon="information"
                title="About"
                subtitle="App version and information"
                onPress={handleAbout}
              />
            </SettingsSection>

            {/* Footer */}
            <View style={{
              marginTop: 32,
              marginBottom: 24,
              alignItems: 'center',
            }}>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center' }}>
                Elite Tailor Employee App v1.0.0
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center', marginTop: 4 }}>
                Made with ❤️ for tailor shop employees
              </Text>
            </View>
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <Surface
          style={[
            styles.bottomNav,
            {
              backgroundColor: theme.colors.surface,
              borderTopColor: theme.colors.outline + '20'
            }
          ]}
          elevation={8}
        >
          <TouchableOpacity
            style={styles.navButton}
            onPress={() => handleNavigation('dashboard')}
          >
            <Icon
              name="view-dashboard"
              size={24}
              color={currentScreen === 'dashboard' ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
            <Text
              variant="labelSmall"
              style={{
                color: currentScreen === 'dashboard' ? theme.colors.primary : theme.colors.onSurfaceVariant,
                marginTop: 2,
                fontWeight: currentScreen === 'dashboard' ? '600' : '400',
                fontSize: 10
              }}
            >
              Dashboard
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navButton}
            onPress={() => handleNavigation('scan')}
          >
            <Icon
              name="qrcode-scan"
              size={24}
              color={currentScreen === 'scan' ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
            <Text
              variant="labelSmall"
              style={{
                color: currentScreen === 'scan' ? theme.colors.primary : theme.colors.onSurfaceVariant,
                marginTop: 2,
                fontWeight: currentScreen === 'scan' ? '600' : '400',
                fontSize: 10
              }}
            >
              Scan
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.plusButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => handleNavigation('quickActions')}
          >
            <Icon
              name="plus"
              size={24}
              color={theme.colors.onPrimary}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navButton}
            onPress={() => handleNavigation('orders')}
          >
            <Icon
              name="clipboard-list"
              size={24}
              color={currentScreen === 'orders' ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
            <Text
              variant="labelSmall"
              style={{
                color: currentScreen === 'orders' ? theme.colors.primary : theme.colors.onSurfaceVariant,
                marginTop: 2,
                fontWeight: currentScreen === 'orders' ? '600' : '400',
                fontSize: 10
              }}
            >
              Orders
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navButton}
            onPress={() => handleNavigation('settings')}
          >
            <Icon
              name="cog"
              size={24}
              color={currentScreen === 'settings' ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
            <Text
              variant="labelSmall"
              style={{
                color: currentScreen === 'settings' ? theme.colors.primary : theme.colors.onSurfaceVariant,
                marginTop: 2,
                fontWeight: currentScreen === 'settings' ? '600' : '400',
                fontSize: 10
              }}
            >
              Settings
            </Text>
          </TouchableOpacity>
        </Surface>
      </View>
    );
  }

  return (
    <View style={{ flex: 1 }}>
      {/* Simple Bottom Sheet Modal */}
      {showBottomSheet && (
        <View style={styles.bottomSheetOverlay}>
          <View style={styles.bottomSheetBackdrop} onTouchEnd={closeBottomSheet} />
          <View style={[styles.bottomSheetContainer, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.bottomSheetHandle} />

            {bottomSheetType === 'order' && selectedOrder && (
              <ScrollView style={styles.bottomSheetContent}>
                <Text variant="headlineSmall" style={styles.bottomSheetTitle}>
                  Order Details
                </Text>

                <Card style={styles.detailCard}>
                  <Card.Content>
                    <Text variant="titleMedium">Order #{selectedOrder.id}</Text>
                    <Text variant="bodyLarge">{selectedOrder.customerName}</Text>
                    <Text variant="bodyMedium">{selectedOrder.garmentType}</Text>
                    <Text variant="bodySmall">Due: {selectedOrder.dueDate}</Text>
                    <Text variant="bodySmall">Price: ৳{selectedOrder.estimatedPrice}</Text>
                    <Text variant="bodySmall">Advance: ৳{selectedOrder.advancePayment}</Text>
                    <Text variant="bodySmall">Status: {selectedOrder.status}</Text>
                    <Text variant="bodySmall">Priority: {selectedOrder.priority}</Text>
                    <Text variant="bodySmall">Assigned to: {selectedOrder.assignedTo}</Text>
                    {selectedOrder.notes && (
                      <Text variant="bodySmall" style={{ marginTop: 8 }}>
                        Notes: {selectedOrder.notes}
                      </Text>
                    )}
                  </Card.Content>
                </Card>

                <Button
                  mode="contained"
                  onPress={() => {
                    updateOrderStatus(selectedOrder.id, 'updated');
                    closeBottomSheet();
                  }}
                  style={styles.bottomSheetButton}
                >
                  Update Status
                </Button>
              </ScrollView>
            )}

            {bottomSheetType === 'customer' && selectedCustomer && (
              <ScrollView style={styles.bottomSheetContent}>
                <Text variant="headlineSmall" style={styles.bottomSheetTitle}>
                  Customer Details
                </Text>

                <Card style={styles.detailCard}>
                  <Card.Content>
                    <Text variant="titleMedium">{selectedCustomer.name}</Text>
                    <Text variant="bodyMedium">{selectedCustomer.phone}</Text>
                    <Text variant="bodyMedium">{selectedCustomer.email}</Text>
                    <Text variant="bodySmall">{selectedCustomer.address}</Text>
                    <Text variant="bodySmall">Total Orders: {selectedCustomer.totalOrders}</Text>
                    <Text variant="bodySmall">Member since: {new Date(selectedCustomer.joinDate).toLocaleDateString()}</Text>
                  </Card.Content>
                </Card>

                <Button
                  mode="contained"
                  onPress={() => {
                    Alert.alert('Contact', `Calling ${selectedCustomer.name}...`);
                    closeBottomSheet();
                  }}
                  style={styles.bottomSheetButton}
                >
                  Call Customer
                </Button>
              </ScrollView>
            )}
          </View>
        </View>
      )}
    </View>
  );
};

// Main App Component with Theme Provider
export default function App() {
  const [isDarkMode, setIsDarkMode] = useState(false);

  const theme = isDarkMode ? MD3DarkTheme : MD3LightTheme;

  return (
    <SafeAreaProvider>
      <PaperProvider theme={theme}>
        <AppContent />
      </PaperProvider>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  // Login Screen Styles
  loginContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 24,
  },
  loginHeader: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    marginTop: 16,
    textAlign: 'center',
    color: 'white',
  },
  subtitle: {
    marginTop: 8,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  loginCard: {
    borderRadius: 28,
    marginHorizontal: 8,
  },
  loginTitle: {
    textAlign: 'center',
    marginBottom: 24,
  },
  textInput: {
    marginBottom: 16,
  },
  loginButton: {
    marginTop: 8,
    marginBottom: 16,
    borderRadius: 20,
  },
  demoText: {
    textAlign: 'center',
    opacity: 0.7,
  },

  // Tasks Screen Styles
  content: {
    flex: 1,
  },
  statsContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  statCard: {
    flex: 1,
    borderRadius: 16,
  },
  statCardContent: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  statNumber: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  ordersSection: {
    paddingHorizontal: 16,
    paddingBottom: 100,
  },
  sectionTitle: {
    marginBottom: 16,
    fontWeight: '600',
  },
  emptyCard: {
    borderRadius: 16,
    marginTop: 32,
  },
  emptyCardContent: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  orderCard: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  customerName: {
    fontWeight: '600',
    marginBottom: 4,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  bottomNav: {
    flexDirection: 'row',
    height: 58,
    paddingTop: 4,
    paddingHorizontal: 12,
    borderTopWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  navContainer: {
    flexDirection: 'row',
    flex: 1,
  },
  navButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
    paddingHorizontal: 2,
  },
  plusButton: {
    width: 44,
    height: 44,
    borderRadius: 12,
    marginTop: 0,
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeNavButton: {
    backgroundColor: 'rgba(0,0,0,0.05)',
  },

  // Search Container
  searchContainer: {
    padding: 16,
  },
  searchInput: {
    marginBottom: 0,
  },

  // Quick Actions
  quickActionsContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  quickActionCard: {
    flex: 1,
    minWidth: '45%',
    borderRadius: 16,
  },
  quickActionContent: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  quickActionTitle: {
    marginTop: 8,
    textAlign: 'center',
    fontSize: 12,
  },

  // Recent Orders
  recentOrdersContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },

  // Customer Styles
  customerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  customerInfo: {
    marginLeft: 12,
    flex: 1,
  },
  customerAddress: {
    marginTop: 8,
    opacity: 0.8,
  },

  // Product Styles
  productHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  productInfo: {
    marginLeft: 12,
    flex: 1,
  },
  productPrice: {
    alignItems: 'flex-end',
  },
  productDescription: {
    marginTop: 8,
    opacity: 0.8,
  },
  priceText: {
    fontWeight: '600',
    marginBottom: 4,
  },

  // Bottom Sheet Styles
  bottomSheetOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'flex-end',
    zIndex: 1000,
  },
  bottomSheetBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  bottomSheetContainer: {
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    minHeight: '50%',
    maxHeight: '80%',
    paddingBottom: 20,
  },
  bottomSheetHandle: {
    width: 32,
    height: 4,
    backgroundColor: 'rgba(0,0,0,0.3)',
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 12,
    marginBottom: 16,
  },
  bottomSheetContent: {
    flex: 1,
    paddingHorizontal: 24,
  },
  bottomSheetTitle: {
    marginBottom: 16,
    fontWeight: '600',
  },
  detailCard: {
    marginBottom: 16,
    borderRadius: 16,
  },
  bottomSheetButton: {
    marginTop: 16,
    borderRadius: 20,
  },

  // Plus Button Style
  plusButton: {
    backgroundColor: 'rgba(0,0,0,0.05)',
  },

  // Scan Screen Styles
  scanContainer: {
    padding: 16,
  },
  scanCard: {
    borderRadius: 16,
    marginBottom: 24,
  },
  scanCardContent: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  scanTitle: {
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  scanSubtitle: {
    textAlign: 'center',
    marginBottom: 24,
    opacity: 0.7,
  },
  scanButton: {
    marginBottom: 12,
    borderRadius: 20,
    paddingHorizontal: 32,
  },
  demoScanButton: {
    borderRadius: 20,
    paddingHorizontal: 32,
  },
  recentScansContainer: {
    marginTop: 16,
  },
  recentScanCard: {
    marginBottom: 8,
    borderRadius: 12,
  },
  recentScanHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },

  // Quick Action Subtitle
  quickActionSubtitle: {
    textAlign: 'center',
    opacity: 0.7,
    marginTop: 4,
  },

  // Settings Screen Styles
  settingsContainer: {
    padding: 16,
  },
  settingsSection: {
    marginBottom: 24,
  },
  settingsSectionTitle: {
    marginBottom: 12,
    fontWeight: '600',
  },
  settingsCard: {
    borderRadius: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingItemText: {
    marginLeft: 16,
    flex: 1,
  },
  settingsFooter: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  footerText: {
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: 4,
  },

  // Plus Button Style
  plusButton: {
    backgroundColor: 'rgba(0,0,0,0.05)',
  },

  // Scan Screen Styles
  scanContainer: {
    padding: 16,
  },
  scanCard: {
    borderRadius: 16,
    marginBottom: 24,
  },
  scanCardContent: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  scanTitle: {
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  scanSubtitle: {
    textAlign: 'center',
    marginBottom: 24,
    opacity: 0.7,
  },
  scanButton: {
    marginBottom: 12,
    borderRadius: 20,
    paddingHorizontal: 32,
  },
  demoScanButton: {
    borderRadius: 20,
    paddingHorizontal: 32,
  },
  recentScansContainer: {
    marginTop: 16,
  },
  recentScanCard: {
    marginBottom: 8,
    borderRadius: 12,
  },
  recentScanHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },

  // Quick Action Subtitle
  quickActionSubtitle: {
    textAlign: 'center',
    opacity: 0.7,
    marginTop: 4,
  },

  // Form Styles
  formContainer: {
    padding: 16,
    paddingBottom: 100,
  },
  formCard: {
    marginBottom: 16,
    borderRadius: 16,
  },
  formSectionTitle: {
    marginBottom: 16,
    fontWeight: '600',
  },
  formInput: {
    marginBottom: 12,
  },
  formRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  measurementButton: {
    marginTop: 8,
    borderRadius: 20,
  },
  priorityContainer: {
    marginTop: 12,
  },
  priorityLabel: {
    marginBottom: 8,
    fontWeight: '500',
  },
  priorityChips: {
    flexDirection: 'row',
    gap: 8,
  },
  priorityChip: {
    marginRight: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    marginTop: 24,
    marginBottom: 32,
  },
  actionButton: {
    flex: 1,
    borderRadius: 20,
  },
});
