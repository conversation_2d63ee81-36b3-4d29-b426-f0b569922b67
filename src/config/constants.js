// Application Constants
export const APP_CONFIG = {
  name: 'Bakery Management',
  version: '1.0.0',
  author: 'Your Company',
  supportEmail: '<EMAIL>',
};

// API Configuration
export const API_CONFIG = {
  baseURL: process.env.EXPO_PUBLIC_API_URL || 'https://api.yourapp.com',
  timeout: 10000,
  retryAttempts: 3,
};

// Storage Keys
export const STORAGE_KEYS = {
  THEME: 'darkMode',
  USER_DATA: 'userData',
  BAKERY_DATA: 'bakeryData',
  SETTINGS: 'appSettings',
  CACHE: 'appCache',
};

// Tailor Shop Business Rules
export const BUSINESS_RULES = {
  MAX_GARMENTS: 2000,
  MAX_ORDERS_PER_DAY: 100,
  MIN_FABRIC_ALERT: 10, // meters
  MAX_DISCOUNT_PERCENT: 30,

  // Tailoring Order Workflow
  ORDER_STATUSES: [
    'Order Placed',
    'Measurement Taken',
    'Cutting',
    'Stitching',
    'First Fitting',
    'Alterations',
    'Final Fitting',
    'Quality Check',
    'Ready for Delivery',
    'Delivered',
    'Cancelled'
  ],

  // Garment Categories
  GARMENT_CATEGORIES: [
    'Men\'s Formal', 'Men\'s Casual', 'Men\'s Traditional',
    'Women\'s Formal', 'Women\'s Casual', 'Women\'s Traditional',
    'Baby Wear', 'School Uniforms', 'Suits', 'Shirts', 'Pants',
    'Readymade Garments', 'Alterations', 'Accessories'
  ],

  // Service Types
  SERVICE_TYPES: [
    'Custom Tailoring',
    'Alterations',
    'Readymade Sales',
    'Repairs',
    'Embroidery',
    'Dry Cleaning'
  ],

  // Employee Roles
  EMPLOYEE_ROLES: [
    'Shop Manager',
    'Senior Tailor',
    'Junior Tailor',
    'Cutter',
    'Helper',
    'Fitting Specialist',
    'Quality Controller',
    'Sales Associate',
    'Delivery Person'
  ],

  // Urgency Levels
  URGENCY_LEVELS: [
    'Rush (1-2 days)',
    'Express (3-5 days)',
    'Normal (7-10 days)',
    'Flexible (15+ days)'
  ],

  PAYMENT_METHODS: ['Cash', 'Card', 'Mobile Banking', 'Bank Transfer', 'Advance Payment'],
};

// UI Constants
export const UI_CONSTANTS = {
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 500,
  PAGINATION_SIZE: 20,
  MAX_SEARCH_RESULTS: 50,
  BOTTOM_SHEET_SNAP_POINTS: ['25%', '50%', '90%'],
};

// Validation Rules
export const VALIDATION_RULES = {
  PRODUCT_NAME: {
    minLength: 2,
    maxLength: 100,
    required: true,
  },
  PRICE: {
    min: 0.01,
    max: 9999.99,
    required: true,
  },
  STOCK: {
    min: 0,
    max: 10000,
    required: true,
  },
  CUSTOMER_NAME: {
    minLength: 2,
    maxLength: 50,
    required: true,
  },
  PHONE: {
    pattern: /^[\+]?[1-9][\d]{0,15}$/,
    required: false,
  },
  EMAIL: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    required: false,
  },
};

// Tailor Shop Financial Management
export const FINANCIAL_CONFIG = {
  EXPENSE_CATEGORIES: [
    'Fabric & Materials',
    'Thread & Accessories',
    'Staff Wages',
    'Rent',
    'Sewing Equipment',
    'Utilities',
    'Marketing',
    'Insurance',
    'Maintenance',
    'Transportation',
    'Other'
  ],
  PAYMENT_METHODS: [
    'Cash',
    'Credit Card',
    'Debit Card',
    'Digital Wallet',
    'Bank Transfer',
    'Check',
    'Store Credit'
  ],
  TAX_RATES: {
    SALES_TAX: 0.08, // 8% default sales tax
    INCOME_TAX: 0.21, // 21% corporate tax rate
    PAYROLL_TAX: 0.153, // 15.3% payroll tax
  },
  CURRENCY: {
    SYMBOL: '৳',
    CODE: 'BDT',
    DECIMAL_PLACES: 2,
  },
  FISCAL_YEAR_START: 1, // January (1-12)
  RECONCILIATION_TOLERANCE: 5.00, // ৳5 tolerance for cash reconciliation
};

// Tailor Shop Specific Constants
export const TAILOR_SHOP_CONFIG = {
  // Measurement Categories
  MEASUREMENT_CATEGORIES: {
    MEN: [
      'Chest', 'Waist', 'Hip', 'Shoulder Width', 'Arm Length',
      'Neck', 'Shirt Length', 'Pant Length', 'Inseam', 'Thigh'
    ],
    WOMEN: [
      'Bust', 'Waist', 'Hip', 'Shoulder Width', 'Arm Length',
      'Neck', 'Blouse Length', 'Skirt Length', 'Dress Length', 'Sleeve Length'
    ],
    BABY: [
      'Chest', 'Length', 'Arm Length', 'Waist', 'Age'
    ]
  },

  // Fabric Types
  FABRIC_TYPES: [
    'Cotton', 'Silk', 'Wool', 'Linen', 'Polyester', 'Blend',
    'Denim', 'Chiffon', 'Georgette', 'Khadi', 'Muslin'
  ],

  // Complexity Levels
  COMPLEXITY_LEVELS: {
    'Simple': { multiplier: 1.0, description: 'Basic stitching, minimal details' },
    'Medium': { multiplier: 1.3, description: 'Moderate details, standard fitting' },
    'Complex': { multiplier: 1.6, description: 'Intricate details, precise fitting' },
    'Premium': { multiplier: 2.0, description: 'Luxury finish, hand-stitched details' }
  },

  // Standard Delivery Times (in days)
  DELIVERY_TIMES: {
    'Custom Tailoring': { min: 7, max: 15 },
    'Alterations': { min: 1, max: 3 },
    'Readymade Sales': { min: 0, max: 1 },
    'Repairs': { min: 2, max: 5 },
    'Embroidery': { min: 3, max: 7 }
  },

  // Employee Skills
  EMPLOYEE_SKILLS: [
    'Men\'s Formal Wear', 'Women\'s Formal Wear', 'Traditional Wear',
    'Baby Clothes', 'School Uniforms', 'Alterations', 'Embroidery',
    'Pattern Making', 'Cutting', 'Hand Stitching', 'Machine Operation',
    'Quality Control', 'Customer Service', 'Measurements'
  ],

  // Quality Check Points
  QUALITY_CHECKPOINTS: [
    'Measurement Accuracy',
    'Stitching Quality',
    'Finishing',
    'Button/Zipper Alignment',
    'Overall Fit',
    'Color Matching',
    'Pressing/Ironing'
  ]
};

// Feature Flags
export const FEATURE_FLAGS = {
  ENABLE_ANALYTICS: true,
  ENABLE_PUSH_NOTIFICATIONS: true,
  ENABLE_OFFLINE_MODE: true,
  ENABLE_MULTI_STORE: false,
  ENABLE_INVENTORY_TRACKING: true,
  ENABLE_CUSTOMER_LOYALTY: false,
  ENABLE_ADVANCED_REPORTS: true,
  ENABLE_FINANCIAL_MANAGEMENT: true,
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  SAVE_ERROR: 'Failed to save data. Please try again.',
  LOAD_ERROR: 'Failed to load data. Please refresh the app.',
  PERMISSION_ERROR: 'Permission denied. Please check app permissions.',
  GENERIC_ERROR: 'Something went wrong. Please try again.',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  SAVE_SUCCESS: 'Data saved successfully!',
  UPDATE_SUCCESS: 'Updated successfully!',
  DELETE_SUCCESS: 'Deleted successfully!',
  EXPORT_SUCCESS: 'Data exported successfully!',
  IMPORT_SUCCESS: 'Data imported successfully!',
};
