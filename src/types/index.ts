// Core Entity Types for Tailor Shop
export interface Garment {
  id: string;
  name: string;
  description: string;
  basePrice: number;
  category: GarmentCategory;
  serviceType: ServiceType;
  fabricCost?: number;
  laborCost?: number;
  complexity: ComplexityLevel;
  estimatedDays: number;
  sku: string;
  barcode?: string;
  image?: string;
  isActive: boolean;
  isReadymade: boolean; // For readymade garments
  stock?: number; // Only for readymade items
  sizes?: string[]; // Available sizes for readymade
  createdAt: string;
  updatedAt: string;
}

// Employee Management
export interface Employee {
  id: string;
  name: string;
  role: EmployeeRole;
  skills: string[];
  contactInfo: {
    phone: string;
    email?: string;
    address: string;
  };
  salary: {
    type: 'hourly' | 'monthly' | 'commission';
    amount: number;
    commissionRate?: number;
  };
  schedule: {
    workingDays: string[];
    startTime: string;
    endTime: string;
  };
  performance: {
    completedOrders: number;
    averageRating: number;
    onTimeDelivery: number;
    qualityScore: number;
  };
  activeOrders: string[];
  isActive: boolean;
  joinDate: string;
  createdAt: string;
  updatedAt: string;
}

// Measurements
export interface CustomerMeasurements {
  id: string;
  customerId: string;
  measurementType: 'MEN' | 'WOMEN' | 'BABY';
  measurements: Record<string, number>;
  takenBy: string; // Employee ID
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Customer {
  id: string;
  name: string;
  email?: string;
  phone: string;
  address: string;
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: string;
  preferredFabrics?: string[];
  measurementHistory: string[]; // Array of measurement IDs
  loyaltyPoints?: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TailoringOrder {
  id: string;
  customerName: string;
  customerId: string;
  email?: string;
  phone: string;
  orderDate: string;
  deliveryDate: string;
  status: TailoringOrderStatus;
  serviceType: ServiceType;
  urgency: UrgencyLevel;

  // Garment Details
  garments: TailoringOrderItem[];

  // Measurements
  measurementId?: string;
  requiresFitting: boolean;
  fittingDates: string[];

  // Assignment
  assignedTailor?: string; // Employee ID
  assignedCutter?: string; // Employee ID

  // Pricing
  subtotal: number;
  fabricCost: number;
  laborCost: number;
  tax: number;
  discount: number;
  advancePayment: number;
  total: number;

  // Additional Info
  notes?: string;
  specialInstructions?: string;
  images?: string[];

  // Progress Tracking
  progressNotes: ProgressNote[];
  qualityChecks: QualityCheck[];

  createdAt: string;
  updatedAt: string;
}

// Progress tracking for orders
export interface ProgressNote {
  id: string;
  orderId: string;
  employeeId: string;
  employeeName: string;
  status: TailoringOrderStatus;
  notes: string;
  images?: string[];
  timestamp: string;
}

// Quality control
export interface QualityCheck {
  id: string;
  orderId: string;
  checkerId: string; // Employee ID
  checkerName: string;
  checkpoints: Record<string, boolean>; // Quality checkpoint results
  overallRating: number; // 1-5 scale
  notes?: string;
  passedQC: boolean;
  timestamp: string;
}

export interface TailoringOrderItem {
  id: string;
  orderId: string;
  garmentId: string;
  garmentName: string;
  quantity: number;
  basePrice: number;
  fabricCost: number;
  laborCost: number;
  complexity: ComplexityLevel;
  fabricType?: string;
  color?: string;
  size?: string;
  customMeasurements?: boolean;
  total: number;
}

// Enum Types for Tailor Shop
export type TailoringOrderStatus =
  | 'Order Placed'
  | 'Measurement Taken'
  | 'Cutting'
  | 'Stitching'
  | 'First Fitting'
  | 'Alterations'
  | 'Final Fitting'
  | 'Quality Check'
  | 'Ready for Delivery'
  | 'Delivered'
  | 'Cancelled';

export type ServiceType =
  | 'Custom Tailoring'
  | 'Alterations'
  | 'Readymade Sales'
  | 'Repairs'
  | 'Embroidery'
  | 'Dry Cleaning';

export type GarmentCategory =
  | 'Men\'s Formal' | 'Men\'s Casual' | 'Men\'s Traditional'
  | 'Women\'s Formal' | 'Women\'s Casual' | 'Women\'s Traditional'
  | 'Baby Wear' | 'School Uniforms' | 'Suits' | 'Shirts' | 'Pants'
  | 'Readymade Garments' | 'Alterations' | 'Accessories';

export type ComplexityLevel = 'Simple' | 'Medium' | 'Complex' | 'Premium';

export type UrgencyLevel =
  | 'Rush (1-2 days)'
  | 'Express (3-5 days)'
  | 'Normal (7-10 days)'
  | 'Flexible (15+ days)';

export type EmployeeRole =
  | 'Shop Manager'
  | 'Senior Tailor'
  | 'Junior Tailor'
  | 'Cutter'
  | 'Helper'
  | 'Fitting Specialist'
  | 'Quality Controller'
  | 'Sales Associate'
  | 'Delivery Person';

// UI Component Types
export interface StatCard {
  id: string;
  title: string;
  value: string | number;
  icon: string;
  color?: string;
  iconColor?: string;
  elevation?: number;
  onPress?: () => void;
  props?: Record<string, any>;
}

export interface UnifiedInfoCardProps {
  type: 'stat' | 'info' | 'action';
  title: string;
  subtitle?: string;
  value?: string | number;
  icon?: string;
  iconColor?: string;
  color?: string;
  elevation?: number;
  onPress?: () => void;
  style?: Record<string, any>;
  children?: React.ReactNode;
}

// Service Response Types
export interface ServiceResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  counts?: {
    products: number;
    customers: number;
    orders: number;
  };
}

// Database Types
export interface DatabaseConfig {
  name: string;
  version: number;
  tables: string[];
}

export interface SQLiteResult {
  insertId?: number;
  rowsAffected: number;
  rows: {
    length: number;
    item: (index: number) => any;
    _array: any[];
  };
}

// Context Types
export interface DataContextType {
  products: Product[];
  orders: Order[];
  customers: Customer[];
  loading: boolean;
  error: string | null;
  refreshData: () => Promise<void>;
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateProduct: (id: string, product: Partial<Product>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  addOrder: (order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateOrder: (id: string, order: Partial<Order>) => Promise<void>;
  deleteOrder: (id: string) => Promise<void>;
  addCustomer: (customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateCustomer: (id: string, customer: Partial<Customer>) => Promise<void>;
  deleteCustomer: (id: string) => Promise<void>;
}

// Analytics Types
export interface AnalyticsData {
  totalRevenue: number;
  totalOrders: number;
  totalCustomers: number;
  totalProducts: number;
  averageOrderValue: number;
  topProducts: Product[];
  recentOrders: Order[];
  monthlyRevenue: number[];
  dailyOrders: number[];
}

// Search Types
export interface SearchResult {
  type: 'product' | 'order' | 'customer';
  id: string;
  title: string;
  subtitle: string;
  data: Product | Order | Customer;
}

// Form Types
export interface ProductFormData {
  name: string;
  description: string;
  price: string;
  category: ProductCategory;
  stock: string;
  image?: string;
}

export interface OrderFormData {
  customerName: string;
  email: string;
  phone: string;
  orderType: OrderType;
  notes?: string;
  image?: string;
  items: OrderItem[];
}

export interface CustomerFormData {
  name: string;
  email: string;
  phone: string;
  address: string;
}

// Navigation Types
export type RootStackParamList = {
  Main: undefined;
  ProductForm: { product?: Product };
  OrderForm: { order?: Order };
  CustomerForm: { customer?: Customer };
  ProductDetails: { productId: string };
  OrderDetails: { orderId: string };
  CustomerDetails: { customerId: string };
  Search: { query?: string };
  Analytics: undefined;
  Reports: undefined;
  Settings: undefined;
  Profile: undefined;
};

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// Performance Types
export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage: number;
  cacheHitRate: number;
  errorRate: number;
}

// Cache Types
export interface CacheEntry<T = any> {
  key: string;
  data: T;
  timestamp: number;
  ttl: number;
}

export interface CacheConfig {
  maxSize: number;
  defaultTTL: number;
  cleanupInterval: number;
}
