import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';

const lightTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: '#2563EB', // Modern blue
    primaryContainer: '#EFF6FF',
    secondary: '#64748B', // Slate gray
    secondaryContainer: '#F1F5F9',
    tertiary: '#059669', // Emerald
    tertiaryContainer: '#ECFDF5',
    surface: '#FFFFFF',
    surfaceVariant: '#F8FAFC',
    background: '#FAFAFA',
    error: '#EF4444',
    errorContainer: '#FEF2F2',
    onPrimary: '#FFFFFF',
    onPrimaryContainer: '#1E40AF',
    onSecondary: '#FFFFFF',
    onSecondaryContainer: '#334155',
    onTertiary: '#FFFFFF',
    onTertiaryContainer: '#047857',
    onSurface: '#0F172A',
    onSurfaceVariant: '#64748B',
    onError: '#FFFFFF',
    onErrorContainer: '#DC2626',
    onBackground: '#0F172A',
    outline: '#CBD5E1',
    outlineVariant: '#E2E8F0',
    inverseSurface: '#1E293B',
    inverseOnSurface: '#F1F5F9',
    inversePrimary: '#60A5FA',
  },
};

const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: '#60A5FA',
    primaryContainer: '#1E40AF',
    secondary: '#94A3B8',
    secondaryContainer: '#334155',
    tertiary: '#34D399',
    tertiaryContainer: '#047857',
    surface: '#0F172A',
    surfaceVariant: '#1E293B',
    background: '#020617',
    error: '#F87171',
    errorContainer: '#DC2626',
    onPrimary: '#1E40AF',
    onPrimaryContainer: '#EFF6FF',
    onSecondary: '#334155',
    onSecondaryContainer: '#F1F5F9',
    onTertiary: '#047857',
    onTertiaryContainer: '#ECFDF5',
    onSurface: '#F1F5F9',
    onSurfaceVariant: '#94A3B8',
    onError: '#DC2626',
    onErrorContainer: '#FEF2F2',
    onBackground: '#F1F5F9',
    outline: '#475569',
    outlineVariant: '#334155',
    inverseSurface: '#F1F5F9',
    inverseOnSurface: '#1E293B',
    inversePrimary: '#2563EB',
  },
};

export { lightTheme, darkTheme };
