import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';

/**
 * Material Design 3 Expressive Color System
 * Enhanced with vibrant colors and improved accessibility
 * Based on Google's latest 2024 specifications
 */

// MD3 Expressive Light Theme
const lightTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,

    // Primary color palette (Vibrant Blue)
    primary: '#1976D2',           // Primary 40
    onPrimary: '#FFFFFF',         // Primary 100
    primaryContainer: '#E3F2FD',  // Primary 90
    onPrimaryContainer: '#0D47A1', // Primary 10

    // Secondary color palette (Teal)
    secondary: '#00796B',         // Secondary 40
    onSecondary: '#FFFFFF',       // Secondary 100
    secondaryContainer: '#E0F2F1', // Secondary 90
    onSecondaryContainer: '#004D40', // Secondary 10

    // Tertiary color palette (Orange)
    tertiary: '#F57C00',          // Tertiary 40
    onTertiary: '#FFFFFF',        // Tertiary 100
    tertiaryContainer: '#FFF3E0', // Tertiary 90
    onTertiaryContainer: '#E65100', // Tertiary 10

    // Error color palette
    error: '#D32F2F',             // Error 40
    onError: '#FFFFFF',           // Error 100
    errorContainer: '#FFEBEE',    // Error 90
    onErrorContainer: '#B71C1C',  // Error 10

    // Surface color palette
    surface: '#FEFEFE',           // Neutral 99
    onSurface: '#1C1B1F',         // Neutral 10
    surfaceVariant: '#F4F4F4',    // Neutral variant 90
    onSurfaceVariant: '#49454F',  // Neutral variant 30

    // Background
    background: '#FEFEFE',        // Neutral 99
    onBackground: '#1C1B1F',      // Neutral 10

    // Outline
    outline: '#79747E',           // Neutral variant 50
    outlineVariant: '#CAC4D0',    // Neutral variant 80

    // Inverse colors
    inverseSurface: '#313033',    // Neutral 20
    inverseOnSurface: '#F4EFF4',  // Neutral 95
    inversePrimary: '#90CAF9',    // Primary 80

    // Surface containers (MD3 Expressive enhancement)
    surfaceContainerLowest: '#FFFFFF',  // Neutral 100
    surfaceContainerLow: '#F7F2FA',     // Neutral 96
    surfaceContainer: '#F1ECF4',        // Neutral 94
    surfaceContainerHigh: '#ECE6F0',    // Neutral 92
    surfaceContainerHighest: '#E6E0E9', // Neutral 90

    // Additional expressive colors
    surfaceTint: '#1976D2',       // Primary 40
    shadow: '#000000',            // Black
    scrim: '#000000',             // Black

    // Custom semantic colors for app-specific use
    success: '#2E7D32',           // Green 600
    onSuccess: '#FFFFFF',
    successContainer: '#E8F5E8',
    onSuccessContainer: '#1B5E20',

    warning: '#F57C00',           // Orange 600
    onWarning: '#FFFFFF',
    warningContainer: '#FFF3E0',
    onWarningContainer: '#E65100',

    info: '#1976D2',              // Blue 600
    onInfo: '#FFFFFF',
    infoContainer: '#E3F2FD',
    onInfoContainer: '#0D47A1',
  },
};

// MD3 Expressive Dark Theme
const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,

    // Primary color palette (Vibrant Blue)
    primary: '#90CAF9',           // Primary 80
    onPrimary: '#0D47A1',         // Primary 10
    primaryContainer: '#1565C0',  // Primary 30
    onPrimaryContainer: '#E3F2FD', // Primary 90

    // Secondary color palette (Teal)
    secondary: '#4DB6AC',         // Secondary 80
    onSecondary: '#004D40',       // Secondary 10
    secondaryContainer: '#00695C', // Secondary 30
    onSecondaryContainer: '#E0F2F1', // Secondary 90

    // Tertiary color palette (Orange)
    tertiary: '#FFB74D',          // Tertiary 80
    onTertiary: '#E65100',        // Tertiary 10
    tertiaryContainer: '#FF8F00', // Tertiary 30
    onTertiaryContainer: '#FFF3E0', // Tertiary 90

    // Error color palette
    error: '#F48FB1',             // Error 80
    onError: '#B71C1C',           // Error 10
    errorContainer: '#C62828',    // Error 30
    onErrorContainer: '#FFEBEE',  // Error 90

    // Surface color palette
    surface: '#141218',           // Neutral 6
    onSurface: '#E6E0E9',         // Neutral 90
    surfaceVariant: '#49454F',    // Neutral variant 30
    onSurfaceVariant: '#CAC4D0',  // Neutral variant 80

    // Background
    background: '#101014',        // Neutral 4
    onBackground: '#E6E0E9',      // Neutral 90

    // Outline
    outline: '#938F99',           // Neutral variant 60
    outlineVariant: '#49454F',    // Neutral variant 30

    // Inverse colors
    inverseSurface: '#E6E0E9',    // Neutral 90
    inverseOnSurface: '#313033',  // Neutral 20
    inversePrimary: '#1976D2',    // Primary 40

    // Surface containers (MD3 Expressive enhancement)
    surfaceContainerLowest: '#0F0D13',  // Neutral 4
    surfaceContainerLow: '#1D1B20',     // Neutral 10
    surfaceContainer: '#211F26',        // Neutral 12
    surfaceContainerHigh: '#2B2930',    // Neutral 17
    surfaceContainerHighest: '#36343B', // Neutral 22

    // Additional expressive colors
    surfaceTint: '#90CAF9',       // Primary 80
    shadow: '#000000',            // Black
    scrim: '#000000',             // Black

    // Custom semantic colors for app-specific use
    success: '#81C784',           // Green 300
    onSuccess: '#1B5E20',
    successContainer: '#2E7D32',
    onSuccessContainer: '#E8F5E8',

    warning: '#FFB74D',           // Orange 300
    onWarning: '#E65100',
    warningContainer: '#F57C00',
    onWarningContainer: '#FFF3E0',

    info: '#90CAF9',              // Blue 300
    onInfo: '#0D47A1',
    infoContainer: '#1976D2',
    onInfoContainer: '#E3F2FD',
  },
};

export { lightTheme, darkTheme };
