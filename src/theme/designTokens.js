/**
 * Material Design 3 Expressive Design Tokens
 * Google's latest design system with vibrant colors, expressive typography, and adaptive components
 * Updated for 2024 specifications
 */

// MD3 Expressive Spacing System (4pt base grid with enhanced scale)
export const SPACING = {
  none: 0,
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  xxxl: 28,
  xxxxl: 32,
  xxxxxl: 40,
  xxxxxxl: 48,
  xxxxxxxl: 56,
  xxxxxxxxl: 64,
  xxxxxxxxxl: 72,
  xxxxxxxxxxl: 80,
};

// MD3 Expressive Shape System (Contrasting and adaptive shapes)
export const SHAPE = {
  // Corner radius tokens
  corner: {
    none: 0,
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    xxl: 24,
    xxxl: 28,
    full: 9999,
  },
  // Shape families for different component types
  family: {
    rounded: {
      small: 8,
      medium: 12,
      large: 16,
      extraLarge: 28,
    },
    expressive: {
      small: 12,
      medium: 16,
      large: 24,
      extraLarge: 32,
    },
    sharp: {
      small: 4,
      medium: 8,
      large: 12,
      extraLarge: 16,
    },
  },
};

// MD3 Expressive Typography Scale (Enhanced with emphasized styles)
export const TYPOGRAPHY = {
  // Display styles (Large, attention-grabbing text)
  display: {
    large: {
      fontSize: 57,
      lineHeight: 64,
      fontWeight: '400',
      letterSpacing: -0.25,
    },
    medium: {
      fontSize: 45,
      lineHeight: 52,
      fontWeight: '400',
      letterSpacing: 0,
    },
    small: {
      fontSize: 36,
      lineHeight: 44,
      fontWeight: '400',
      letterSpacing: 0,
    },
  },

  // Headline styles (High-emphasis, shorter text)
  headline: {
    large: {
      fontSize: 32,
      lineHeight: 40,
      fontWeight: '400',
      letterSpacing: 0,
    },
    medium: {
      fontSize: 28,
      lineHeight: 36,
      fontWeight: '400',
      letterSpacing: 0,
    },
    small: {
      fontSize: 24,
      lineHeight: 32,
      fontWeight: '400',
      letterSpacing: 0,
    },
  },

  // Title styles (Medium-emphasis text)
  title: {
    large: {
      fontSize: 22,
      lineHeight: 28,
      fontWeight: '400',
      letterSpacing: 0,
    },
    medium: {
      fontSize: 16,
      lineHeight: 24,
      fontWeight: '500',
      letterSpacing: 0.15,
    },
    small: {
      fontSize: 14,
      lineHeight: 20,
      fontWeight: '500',
      letterSpacing: 0.1,
    },
  },

  // Label styles (Call-to-action text)
  label: {
    large: {
      fontSize: 14,
      lineHeight: 20,
      fontWeight: '500',
      letterSpacing: 0.1,
    },
    medium: {
      fontSize: 12,
      lineHeight: 16,
      fontWeight: '500',
      letterSpacing: 0.5,
    },
    small: {
      fontSize: 11,
      lineHeight: 16,
      fontWeight: '500',
      letterSpacing: 0.5,
    },
  },

  // Body styles (Plain text)
  body: {
    large: {
      fontSize: 16,
      lineHeight: 24,
      fontWeight: '400',
      letterSpacing: 0.5,
    },
    medium: {
      fontSize: 14,
      lineHeight: 20,
      fontWeight: '400',
      letterSpacing: 0.25,
    },
    small: {
      fontSize: 12,
      lineHeight: 16,
      fontWeight: '400',
      letterSpacing: 0.4,
    },
  },

  // Emphasized variants (MD3 Expressive feature)
  emphasized: {
    display: {
      large: { fontSize: 57, lineHeight: 64, fontWeight: '600', letterSpacing: -0.25 },
      medium: { fontSize: 45, lineHeight: 52, fontWeight: '600', letterSpacing: 0 },
      small: { fontSize: 36, lineHeight: 44, fontWeight: '600', letterSpacing: 0 },
    },
    headline: {
      large: { fontSize: 32, lineHeight: 40, fontWeight: '600', letterSpacing: 0 },
      medium: { fontSize: 28, lineHeight: 36, fontWeight: '600', letterSpacing: 0 },
      small: { fontSize: 24, lineHeight: 32, fontWeight: '600', letterSpacing: 0 },
    },
    title: {
      large: { fontSize: 22, lineHeight: 28, fontWeight: '600', letterSpacing: 0 },
      medium: { fontSize: 16, lineHeight: 24, fontWeight: '700', letterSpacing: 0.15 },
      small: { fontSize: 14, lineHeight: 20, fontWeight: '700', letterSpacing: 0.1 },
    },
    label: {
      large: { fontSize: 14, lineHeight: 20, fontWeight: '700', letterSpacing: 0.1 },
      medium: { fontSize: 12, lineHeight: 16, fontWeight: '700', letterSpacing: 0.5 },
      small: { fontSize: 11, lineHeight: 16, fontWeight: '700', letterSpacing: 0.5 },
    },
    body: {
      large: { fontSize: 16, lineHeight: 24, fontWeight: '600', letterSpacing: 0.5 },
      medium: { fontSize: 14, lineHeight: 20, fontWeight: '600', letterSpacing: 0.25 },
      small: { fontSize: 12, lineHeight: 16, fontWeight: '600', letterSpacing: 0.4 },
    },
  },

  // Font weight scale
  fontWeight: {
    light: '300',
    regular: '400',
    medium: '500',
    semiBold: '600',
    bold: '700',
    extraBold: '800',
    black: '900',
  },
};

// MD3 Expressive Elevation System (Enhanced shadow and surface tones)
export const ELEVATION = {
  // Surface tones for different elevation levels
  surface: {
    level0: 0,    // Surface
    level1: 5,    // Surface container lowest
    level2: 8,    // Surface container low
    level3: 11,   // Surface container
    level4: 12,   // Surface container high
    level5: 14,   // Surface container highest
  },

  // Shadow tokens
  shadow: {
    none: {
      shadowColor: 'transparent',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0,
      shadowRadius: 0,
      elevation: 0,
    },
    level1: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    level2: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.08,
      shadowRadius: 3,
      elevation: 2,
    },
    level3: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.12,
      shadowRadius: 6,
      elevation: 3,
    },
    level4: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.16,
      shadowRadius: 12,
      elevation: 8,
    },
    level5: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.20,
      shadowRadius: 16,
      elevation: 12,
    },
  },
};

// Legacy shadow system for backward compatibility
export const SHADOWS = ELEVATION.shadow;

// MD3 Expressive Component Sizing (Enhanced with more precise tokens)
export const COMPONENT_SIZES = {
  // Button sizing following MD3 specifications
  button: {
    small: { height: 32, paddingHorizontal: 12, minWidth: 64 },
    medium: { height: 40, paddingHorizontal: 16, minWidth: 80 },
    large: { height: 48, paddingHorizontal: 20, minWidth: 96 },
    extraLarge: { height: 56, paddingHorizontal: 24, minWidth: 112 },
  },

  // Input field sizing
  input: {
    small: { height: 32, paddingHorizontal: 12 },
    medium: { height: 40, paddingHorizontal: 16 },
    large: { height: 48, paddingHorizontal: 20 },
    extraLarge: { height: 56, paddingHorizontal: 24 },
  },

  // Icon sizing with MD3 scale
  icon: {
    xs: 12,
    sm: 16,
    md: 18,
    lg: 20,
    xl: 24,
    xxl: 28,
    xxxl: 32,
    xxxxl: 40,
    xxxxxl: 48,
    xxxxxxl: 56,
  },

  // Avatar and profile image sizing
  avatar: {
    xs: 24,
    sm: 32,
    md: 40,
    lg: 48,
    xl: 56,
    xxl: 64,
    xxxl: 80,
    xxxxl: 96,
    xxxxxl: 112,
    xxxxxxl: 128,
  },

  // Touch target sizing (minimum 44x44 for accessibility)
  touchTarget: {
    minimum: 44,
    comfortable: 48,
    spacious: 56,
  },

  // Chip and badge sizing
  chip: {
    small: { height: 24, paddingHorizontal: 8 },
    medium: { height: 32, paddingHorizontal: 12 },
    large: { height: 40, paddingHorizontal: 16 },
  },

  // Card sizing
  card: {
    compact: { minHeight: 72, padding: 12 },
    standard: { minHeight: 96, padding: 16 },
    expanded: { minHeight: 120, padding: 20 },
  },
};

// MD3 Expressive Layout Constants (Enhanced for better spacing)
export const LAYOUT = {
  // Header and navigation
  headerHeight: 64,
  bottomNavHeight: 80,
  tabBarHeight: 48,
  statusBarHeight: 44, // iOS default

  // Screen and content spacing
  safeAreaPadding: 16,
  screenPadding: 16,
  screenPaddingHorizontal: 16,
  screenPaddingVertical: 24,

  // Card and component spacing
  cardPadding: 16,
  cardMargin: 12,
  sectionSpacing: 24,
  itemSpacing: 12,

  // Grid and layout
  gridGutter: 16,
  maxContentWidth: 1200,

  // Interactive elements
  minTouchTarget: 44,
  recommendedTouchTarget: 48,
};

// MD3 Expressive Motion System (Intuitive motion patterns)
export const MOTION = {
  // Duration tokens
  duration: {
    instant: 0,
    fast: 100,
    medium: 200,
    slow: 300,
    slower: 400,
    slowest: 500,

    // Specific use cases
    fadeIn: 150,
    fadeOut: 100,
    slideIn: 250,
    slideOut: 200,
    scaleIn: 200,
    scaleOut: 150,

    // Complex animations
    pageTransition: 300,
    modalTransition: 250,
    bottomSheetTransition: 300,
  },

  // Easing curves (Material Design motion)
  easing: {
    // Standard curves
    linear: 'linear',
    standard: 'cubic-bezier(0.2, 0.0, 0, 1.0)',
    decelerate: 'cubic-bezier(0.0, 0.0, 0.2, 1)',
    accelerate: 'cubic-bezier(0.4, 0.0, 1, 1)',

    // Expressive curves
    emphasized: 'cubic-bezier(0.2, 0.0, 0, 1.0)',
    emphasizedDecelerate: 'cubic-bezier(0.05, 0.7, 0.1, 1.0)',
    emphasizedAccelerate: 'cubic-bezier(0.3, 0.0, 0.8, 0.15)',

    // Legacy support
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },

  // Spring configurations for react-native-reanimated
  spring: {
    gentle: { damping: 15, stiffness: 150 },
    standard: { damping: 20, stiffness: 200 },
    bouncy: { damping: 10, stiffness: 100 },
    snappy: { damping: 25, stiffness: 300 },
  },
};

// Legacy animation constants for backward compatibility
export const ANIMATIONS = MOTION;

// MD3 Expressive State System (Enhanced opacity and interaction states)
export const STATE = {
  // Opacity states
  opacity: {
    disabled: 0.38,
    inactive: 0.60,
    active: 1.0,

    // Interaction states
    hover: 0.08,
    focus: 0.12,
    pressed: 0.16,
    dragged: 0.16,

    // Overlay states
    overlay: 0.50,
    backdrop: 0.32,
    scrim: 0.32,
  },

  // Color overlay states (for interaction feedback)
  overlay: {
    hover: 0.04,
    focus: 0.08,
    pressed: 0.10,
    selected: 0.08,
    activated: 0.12,
  },
};

// Legacy opacity values for backward compatibility
export const OPACITY = STATE.opacity;

// MD3 Expressive Z-Index System (Enhanced layering)
export const Z_INDEX = {
  base: 0,
  raised: 1,
  dropdown: 10,
  sticky: 20,
  fixed: 30,
  modal: 40,
  popover: 50,
  tooltip: 60,
  notification: 70,
  overlay: 80,
  max: 9999,
};

// Responsive Breakpoints (Enhanced for modern devices)
export const BREAKPOINTS = {
  xs: 0,      // Mobile portrait
  sm: 576,    // Mobile landscape
  md: 768,    // Tablet portrait
  lg: 992,    // Tablet landscape
  xl: 1200,   // Desktop
  xxl: 1400,  // Large desktop
  xxxl: 1920, // Ultra-wide
};

// MD3 Expressive Component Styles (Enhanced with new tokens)
export const COMPONENT_STYLES = {
  // Card styles using new shape system
  card: {
    standard: {
      borderRadius: SHAPE.family.rounded.medium,
      padding: SPACING.lg,
      marginBottom: SPACING.md,
    },
    elevated: {
      borderRadius: SHAPE.family.rounded.large,
      padding: SPACING.xl,
      marginBottom: SPACING.lg,
    },
    expressive: {
      borderRadius: SHAPE.family.expressive.medium,
      padding: SPACING.lg,
      marginBottom: SPACING.md,
    },
  },

  // Button styles with MD3 specifications
  button: {
    filled: {
      borderRadius: SHAPE.family.rounded.large,
      paddingHorizontal: SPACING.xl,
      paddingVertical: SPACING.md,
      minHeight: COMPONENT_SIZES.button.medium.height,
    },
    outlined: {
      borderRadius: SHAPE.family.rounded.large,
      paddingHorizontal: SPACING.xl,
      paddingVertical: SPACING.md,
      borderWidth: 1,
      minHeight: COMPONENT_SIZES.button.medium.height,
    },
    text: {
      borderRadius: SHAPE.family.rounded.large,
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.sm,
      minHeight: COMPONENT_SIZES.button.medium.height,
    },
  },

  // Input field styles
  input: {
    filled: {
      borderRadius: SHAPE.family.rounded.small,
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.md,
      minHeight: COMPONENT_SIZES.input.medium.height,
    },
    outlined: {
      borderRadius: SHAPE.family.rounded.small,
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.md,
      borderWidth: 1,
      minHeight: COMPONENT_SIZES.input.medium.height,
    },
  },

  // Layout helpers
  layout: {
    section: {
      marginBottom: SPACING.xxxl,
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    column: {
      flexDirection: 'column',
    },
    center: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    spaceBetween: {
      justifyContent: 'space-between',
    },
    flex1: {
      flex: 1,
    },
  },
};

// Legacy common styles for backward compatibility
export const COMMON_STYLES = COMPONENT_STYLES.layout;

// Legacy border radius for backward compatibility
export const BORDER_RADIUS = SHAPE.corner;

// MD3 Expressive Helper Functions
export const getElevationStyle = (level, theme) => {
  const elevation = ELEVATION.shadow[`level${level}`] || ELEVATION.shadow.level1;
  return {
    ...elevation,
    shadowColor: theme?.colors?.shadow || theme?.colors?.onSurface || '#000',
  };
};

export const getShapeStyle = (family, size) => {
  return {
    borderRadius: SHAPE.family[family]?.[size] || SHAPE.family.rounded.medium,
  };
};

export const getTypographyStyle = (scale, size, emphasized = false) => {
  const typography = emphasized
    ? TYPOGRAPHY.emphasized[scale]?.[size]
    : TYPOGRAPHY[scale]?.[size];

  return typography || TYPOGRAPHY.body.medium;
};

// Legacy typography mappings for backward compatibility
export const LEGACY_TYPOGRAPHY = {
  cardTitle: TYPOGRAPHY.title.medium,
  cardSubtitle: TYPOGRAPHY.body.medium,
  buttonText: TYPOGRAPHY.label.large,
  caption: TYPOGRAPHY.body.small,
  heading: TYPOGRAPHY.headline.medium,
  subheading: TYPOGRAPHY.title.large,
};

export const getStateStyle = (state, color) => {
  const opacity = STATE.overlay[state] || 0;
  return {
    backgroundColor: `${color}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`,
  };
};

// Legacy helper functions for backward compatibility
export const getBorderColor = (theme, opacity = 0.05) => {
  return theme.colors.onSurface + Math.round(opacity * 255).toString(16).padStart(2, '0');
};

export const getThemedShadow = (shadowLevel, theme) => {
  const shadow = SHADOWS[shadowLevel] || SHADOWS.level1;
  return {
    ...shadow,
    shadowColor: theme.colors.onSurface,
  };
};

// Export all tokens
export default {
  // Core tokens
  SPACING,
  SHAPE,
  TYPOGRAPHY,
  ELEVATION,
  MOTION,
  STATE,
  COMPONENT_SIZES,
  LAYOUT,
  Z_INDEX,
  BREAKPOINTS,
  COMPONENT_STYLES,

  // Legacy tokens
  BORDER_RADIUS,
  SHADOWS,
  ANIMATIONS,
  OPACITY,
  COMMON_STYLES,
  LEGACY_TYPOGRAPHY,

  // Helper functions
  getElevationStyle,
  getShapeStyle,
  getTypographyStyle,
  getStateStyle,
  getBorderColor,
  getThemedShadow,
};
