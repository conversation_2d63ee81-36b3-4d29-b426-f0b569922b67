/**
 * MD3ThemeProvider - Enhanced Material Design 3 theme provider
 * Provides comprehensive theming with MD3 Expressive design tokens
 */

import React, { createContext, useContext, useMemo } from 'react';
import { useColorScheme } from 'react-native';
import { MD3LightTheme, MD3DarkTheme, configureFonts } from 'react-native-paper';
import {
  TYPOGRAPHY,
  ELEVATION,
  SHAPE,
  SPACING,
  getElevationStyle,
  getTypographyStyle,
  getShapeStyle
} from './designTokens';

// MD3 Expressive Color Palette
const MD3_COLORS = {
  light: {
    // Primary colors
    primary: '#6750A4',
    onPrimary: '#FFFFFF',
    primaryContainer: '#EADDFF',
    onPrimaryContainer: '#21005D',

    // Secondary colors
    secondary: '#625B71',
    onSecondary: '#FFFFFF',
    secondaryContainer: '#E8DEF8',
    onSecondaryContainer: '#1D192B',

    // Tertiary colors
    tertiary: '#7D5260',
    onTertiary: '#FFFFFF',
    tertiaryContainer: '#FFD8E4',
    onTertiaryContainer: '#31111D',

    // Error colors
    error: '#BA1A1A',
    onError: '#FFFFFF',
    errorContainer: '#FFDAD6',
    onErrorContainer: '#410002',

    // Background colors
    background: '#FFFBFE',
    onBackground: '#1C1B1F',

    // Surface colors
    surface: '#FFFBFE',
    onSurface: '#1C1B1F',
    surfaceVariant: '#E7E0EC',
    onSurfaceVariant: '#49454F',

    // Surface containers (MD3 Expressive)
    surfaceDim: '#DDD8E1',
    surfaceBright: '#FFFBFE',
    surfaceContainerLowest: '#FFFFFF',
    surfaceContainerLow: '#F7F2FA',
    surfaceContainer: '#F1ECF4',
    surfaceContainerHigh: '#ECE6F0',
    surfaceContainerHighest: '#E6E0E9',

    // Additional colors
    outline: '#79747E',
    outlineVariant: '#CAC4D0',
    shadow: '#000000',
    scrim: '#000000',
    inverseSurface: '#313033',
    inverseOnSurface: '#F4EFF4',
    inversePrimary: '#D0BCFF',
  },
  dark: {
    // Primary colors - Enhanced for better contrast
    primary: '#BB86FC',
    onPrimary: '#1A0033',
    primaryContainer: '#3700B3',
    onPrimaryContainer: '#E1C4FF',

    // Secondary colors - Improved visibility
    secondary: '#03DAC6',
    onSecondary: '#00201A',
    secondaryContainer: '#005048',
    onSecondaryContainer: '#70F2E8',

    // Tertiary colors - Better contrast
    tertiary: '#FF6B6B',
    onTertiary: '#330000',
    tertiaryContainer: '#8B0000',
    onTertiaryContainer: '#FFD6D6',

    // Error colors
    error: '#CF6679',
    onError: '#1A0000',
    errorContainer: '#8B0000',
    onErrorContainer: '#FFDAD6',

    // Background colors - Improved depth
    background: '#0A0A0A',
    onBackground: '#F5F5F5',

    // Surface colors - Better layering
    surface: '#121212',
    onSurface: '#FFFFFF',
    surfaceVariant: '#2A2A2A',
    onSurfaceVariant: '#E0E0E0',

    // Surface containers - Enhanced hierarchy
    surfaceDim: '#0F0F0F',
    surfaceBright: '#2A2A2A',
    surfaceContainerLowest: '#080808',
    surfaceContainerLow: '#1A1A1A',
    surfaceContainer: '#1E1E1E',
    surfaceContainerHigh: '#242424',
    surfaceContainerHighest: '#2E2E2E',

    // Additional colors - Improved contrast
    outline: '#8A8A8A',
    outlineVariant: '#404040',
    shadow: '#000000',
    scrim: 'rgba(0, 0, 0, 0.6)',
    inverseSurface: '#F5F5F5',
    inverseOnSurface: '#1A1A1A',
    inversePrimary: '#6200EE',
  },
};

// Enhanced font configuration for MD3
const fontConfig = {
  web: {
    regular: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      fontWeight: '400',
    },
    medium: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      fontWeight: '500',
    },
    light: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      fontWeight: '300',
    },
    thin: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      fontWeight: '100',
    },
  },
  ios: {
    regular: {
      fontFamily: 'System',
      fontWeight: '400',
    },
    medium: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    light: {
      fontFamily: 'System',
      fontWeight: '300',
    },
    thin: {
      fontFamily: 'System',
      fontWeight: '100',
    },
  },
  android: {
    regular: {
      fontFamily: 'Roboto',
      fontWeight: 'normal',
    },
    medium: {
      fontFamily: 'Roboto',
      fontWeight: '500',
    },
    light: {
      fontFamily: 'Roboto',
      fontWeight: '300',
    },
    thin: {
      fontFamily: 'Roboto',
      fontWeight: '100',
    },
  },
};

const MD3ThemeContext = createContext(null);

export const MD3ThemeProvider = ({ children, forcedTheme = null }) => {
  const systemColorScheme = useColorScheme();
  const isDark = forcedTheme ? forcedTheme === 'dark' : systemColorScheme === 'dark';

  const theme = useMemo(() => {
    const baseTheme = isDark ? MD3DarkTheme : MD3LightTheme;
    const colors = isDark ? MD3_COLORS.dark : MD3_COLORS.light;

    return {
      ...baseTheme,
      colors: {
        ...baseTheme.colors,
        ...colors,
      },
      fonts: configureFonts({ config: fontConfig }),

      // Enhanced MD3 properties
      md3: {
        typography: TYPOGRAPHY,
        elevation: ELEVATION,
        shape: SHAPE,
        spacing: SPACING,

        // Helper functions
        getElevation: (level) => getElevationStyle(level, { colors }),
        getTypography: (scale, size, emphasized = false) => getTypographyStyle(scale, size, emphasized),
        getShape: (family, size) => getShapeStyle(family, size),

        // Theme utilities
        isDark,
        colorScheme: isDark ? 'dark' : 'light',
      },
    };
  }, [isDark]);

  return (
    <MD3ThemeContext.Provider value={theme}>
      {children}
    </MD3ThemeContext.Provider>
  );
};

export const useMD3Theme = () => {
  const context = useContext(MD3ThemeContext);
  if (!context) {
    throw new Error('useMD3Theme must be used within MD3ThemeProvider');
  }
  return context;
};

export default MD3ThemeProvider;
