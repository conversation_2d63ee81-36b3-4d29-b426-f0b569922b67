/**
 * MD3ThemeProvider - Enhanced Material Design 3 theme provider
 * Provides comprehensive theming with MD3 Expressive design tokens
 */

import React, { createContext, useContext, useMemo } from 'react';
import { useColorScheme } from 'react-native';
import { MD3LightTheme, MD3DarkTheme, configureFonts } from 'react-native-paper';
import { 
  TYPOGRAPHY, 
  ELEVATION, 
  SHAPE, 
  SPACING,
  getElevationStyle,
  getTypographyStyle,
  getShapeStyle 
} from './designTokens';

// MD3 Expressive Color Palette
const MD3_COLORS = {
  light: {
    // Primary colors
    primary: '#6750A4',
    onPrimary: '#FFFFFF',
    primaryContainer: '#EADDFF',
    onPrimaryContainer: '#21005D',
    
    // Secondary colors
    secondary: '#625B71',
    onSecondary: '#FFFFFF',
    secondaryContainer: '#E8DEF8',
    onSecondaryContainer: '#1D192B',
    
    // Tertiary colors
    tertiary: '#7D5260',
    onTertiary: '#FFFFFF',
    tertiaryContainer: '#FFD8E4',
    onTertiaryContainer: '#31111D',
    
    // Error colors
    error: '#BA1A1A',
    onError: '#FFFFFF',
    errorContainer: '#FFDAD6',
    onErrorContainer: '#410002',
    
    // Background colors
    background: '#FFFBFE',
    onBackground: '#1C1B1F',
    
    // Surface colors
    surface: '#FFFBFE',
    onSurface: '#1C1B1F',
    surfaceVariant: '#E7E0EC',
    onSurfaceVariant: '#49454F',
    
    // Surface containers (MD3 Expressive)
    surfaceDim: '#DDD8E1',
    surfaceBright: '#FFFBFE',
    surfaceContainerLowest: '#FFFFFF',
    surfaceContainerLow: '#F7F2FA',
    surfaceContainer: '#F1ECF4',
    surfaceContainerHigh: '#ECE6F0',
    surfaceContainerHighest: '#E6E0E9',
    
    // Additional colors
    outline: '#79747E',
    outlineVariant: '#CAC4D0',
    shadow: '#000000',
    scrim: '#000000',
    inverseSurface: '#313033',
    inverseOnSurface: '#F4EFF4',
    inversePrimary: '#D0BCFF',
  },
  dark: {
    // Primary colors
    primary: '#D0BCFF',
    onPrimary: '#381E72',
    primaryContainer: '#4F378B',
    onPrimaryContainer: '#EADDFF',
    
    // Secondary colors
    secondary: '#CCC2DC',
    onSecondary: '#332D41',
    secondaryContainer: '#4A4458',
    onSecondaryContainer: '#E8DEF8',
    
    // Tertiary colors
    tertiary: '#EFB8C8',
    onTertiary: '#492532',
    tertiaryContainer: '#633B48',
    onTertiaryContainer: '#FFD8E4',
    
    // Error colors
    error: '#FFB4AB',
    onError: '#690005',
    errorContainer: '#93000A',
    onErrorContainer: '#FFDAD6',
    
    // Background colors
    background: '#10131C',
    onBackground: '#E6E1E5',
    
    // Surface colors
    surface: '#10131C',
    onSurface: '#E6E1E5',
    surfaceVariant: '#49454F',
    onSurfaceVariant: '#CAC4D0',
    
    // Surface containers (MD3 Expressive)
    surfaceDim: '#10131C',
    surfaceBright: '#362F42',
    surfaceContainerLowest: '#0B0E17',
    surfaceContainerLow: '#1D1B20',
    surfaceContainer: '#211F26',
    surfaceContainerHigh: '#2B2930',
    surfaceContainerHighest: '#36343B',
    
    // Additional colors
    outline: '#938F99',
    outlineVariant: '#49454F',
    shadow: '#000000',
    scrim: '#000000',
    inverseSurface: '#E6E1E5',
    inverseOnSurface: '#313033',
    inversePrimary: '#6750A4',
  },
};

// Enhanced font configuration for MD3
const fontConfig = {
  web: {
    regular: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      fontWeight: '400',
    },
    medium: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      fontWeight: '500',
    },
    light: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      fontWeight: '300',
    },
    thin: {
      fontFamily: 'Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif',
      fontWeight: '100',
    },
  },
  ios: {
    regular: {
      fontFamily: 'System',
      fontWeight: '400',
    },
    medium: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    light: {
      fontFamily: 'System',
      fontWeight: '300',
    },
    thin: {
      fontFamily: 'System',
      fontWeight: '100',
    },
  },
  android: {
    regular: {
      fontFamily: 'Roboto',
      fontWeight: 'normal',
    },
    medium: {
      fontFamily: 'Roboto',
      fontWeight: '500',
    },
    light: {
      fontFamily: 'Roboto',
      fontWeight: '300',
    },
    thin: {
      fontFamily: 'Roboto',
      fontWeight: '100',
    },
  },
};

const MD3ThemeContext = createContext(null);

export const MD3ThemeProvider = ({ children, forcedTheme = null }) => {
  const systemColorScheme = useColorScheme();
  const isDark = forcedTheme ? forcedTheme === 'dark' : systemColorScheme === 'dark';

  const theme = useMemo(() => {
    const baseTheme = isDark ? MD3DarkTheme : MD3LightTheme;
    const colors = isDark ? MD3_COLORS.dark : MD3_COLORS.light;

    return {
      ...baseTheme,
      colors: {
        ...baseTheme.colors,
        ...colors,
      },
      fonts: configureFonts({ config: fontConfig }),
      
      // Enhanced MD3 properties
      md3: {
        typography: TYPOGRAPHY,
        elevation: ELEVATION,
        shape: SHAPE,
        spacing: SPACING,
        
        // Helper functions
        getElevation: (level) => getElevationStyle(level, { colors }),
        getTypography: (scale, size, emphasized = false) => getTypographyStyle(scale, size, emphasized),
        getShape: (family, size) => getShapeStyle(family, size),
        
        // Theme utilities
        isDark,
        colorScheme: isDark ? 'dark' : 'light',
      },
    };
  }, [isDark]);

  return (
    <MD3ThemeContext.Provider value={theme}>
      {children}
    </MD3ThemeContext.Provider>
  );
};

export const useMD3Theme = () => {
  const context = useContext(MD3ThemeContext);
  if (!context) {
    throw new Error('useMD3Theme must be used within MD3ThemeProvider');
  }
  return context;
};

export default MD3ThemeProvider;
