/**
 * MeasurementsScreen - Customer Measurement Management
 * Digital measurement forms with body diagrams and history
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  Image,
} from 'react-native';
import {
  Text,
  Card,
  TextInput,
  Button,
  SegmentedButtons,
  useTheme,
  Chip,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import CommonHeader from '../components/CommonHeader';
import { useData } from '../context/DataContext';
import { SPACING, SHAPE, TYPOGRAPHY } from '../theme/designTokens';
import { TAILOR_SHOP_CONFIG } from '../config/constants';

const MeasurementsScreen = ({ navigation, route }) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const { customers, employees, addMeasurement, updateMeasurement } = useData();

  // Route params
  const { customerId, measurementId, orderId } = route.params || {};
  const isEditing = !!measurementId;

  // State management
  const [measurementType, setMeasurementType] = useState('MEN');
  const [measurements, setMeasurements] = useState({});
  const [notes, setNotes] = useState('');
  const [takenBy, setTakenBy] = useState('');
  const [loading, setLoading] = useState(false);

  // Get customer info
  const customer = customers?.find(c => c.id === customerId);

  // Get measurement categories based on type
  const measurementCategories = TAILOR_SHOP_CONFIG.MEASUREMENT_CATEGORIES[measurementType] || [];

  // Initialize measurements
  useEffect(() => {
    if (isEditing) {
      // Load existing measurement data
      // This would come from your data context
    } else {
      // Initialize empty measurements
      const initialMeasurements = {};
      measurementCategories.forEach(category => {
        initialMeasurements[category] = '';
      });
      setMeasurements(initialMeasurements);
    }
  }, [measurementType, isEditing]);

  // Handle measurement input change
  const handleMeasurementChange = (category, value) => {
    setMeasurements(prev => ({
      ...prev,
      [category]: value
    }));
  };

  // Validate measurements
  const validateMeasurements = () => {
    const requiredFields = measurementCategories.slice(0, 5); // First 5 are required
    const missingFields = requiredFields.filter(field => !measurements[field] || measurements[field].trim() === '');

    if (missingFields.length > 0) {
      Alert.alert(
        'Missing Measurements',
        `Please fill in the following required measurements: ${missingFields.join(', ')}`
      );
      return false;
    }
    return true;
  };

  // Save measurements
  const handleSave = async () => {
    if (!validateMeasurements()) return;

    setLoading(true);
    try {
      const measurementData = {
        customerId,
        measurementType,
        measurements: Object.fromEntries(
          Object.entries(measurements).filter(([_, value]) => value && value.trim() !== '')
        ),
        takenBy,
        notes,
        orderId, // Link to order if provided
      };

      if (isEditing) {
        await updateMeasurement(measurementId, measurementData);
        Alert.alert('Success', 'Measurements updated successfully!');
      } else {
        await addMeasurement(measurementData);
        Alert.alert('Success', 'Measurements saved successfully!');
      }

      navigation.goBack();
    } catch (error) {
      console.error('Error saving measurements:', error);
      Alert.alert('Error', 'Failed to save measurements. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Render measurement input
  const renderMeasurementInput = (category, index) => {
    const isRequired = index < 5; // First 5 measurements are required

    return (
      <View key={category} style={styles.measurementRow}>
        <View style={styles.measurementLabel}>
          <Text style={[styles.labelText, { color: theme.colors.onSurface }]}>
            {category}
            {isRequired && <Text style={{ color: theme.colors.error }}> *</Text>}
          </Text>
          <Text style={[styles.unitText, { color: theme.colors.onSurfaceVariant }]}>
            (inches)
          </Text>
        </View>
        <TextInput
          mode="outlined"
          value={measurements[category] || ''}
          onChangeText={(value) => handleMeasurementChange(category, value)}
          keyboardType="numeric"
          placeholder="0.0"
          style={styles.measurementInput}
          contentStyle={styles.inputContent}
          dense
        />
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title={isEditing ? 'Edit Measurements' : 'Take Measurements'}
        subtitle={customer ? customer.name : 'Customer measurements'}
        showBack
        onBackPress={() => navigation.goBack()}
        showSearch={false}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => {
          console.log('Profile pressed - navigating to MyProfile');
          try {
            navigation.navigate('MyProfile');
          } catch (error) {
            console.error('Failed to navigate to MyProfile:', error);
          }
        }}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Customer Info */}
        {customer && (
          <Card style={[styles.customerCard, { backgroundColor: theme.colors.surface }]}>
            <Card.Content>
              <Text style={[styles.customerName, { color: theme.colors.onSurface }]}>
                {customer.name}
              </Text>
              <Text style={[styles.customerPhone, { color: theme.colors.onSurfaceVariant }]}>
                {customer.phone}
              </Text>
            </Card.Content>
          </Card>
        )}

        {/* Measurement Type Selector */}
        <Card style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Measurement Type
            </Text>
            <SegmentedButtons
              value={measurementType}
              onValueChange={setMeasurementType}
              buttons={[
                { value: 'MEN', label: 'Men', icon: 'account' },
                { value: 'WOMEN', label: 'Women', icon: 'account-outline' },
                { value: 'BABY', label: 'Baby', icon: 'baby' },
              ]}
              style={styles.segmentedButtons}
            />
          </Card.Content>
        </Card>

        {/* Body Diagram */}
        <Card style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Measurement Guide
            </Text>
            <View style={styles.diagramContainer}>
              <Icon
                name={measurementType === 'MEN' ? 'account' : measurementType === 'WOMEN' ? 'account-outline' : 'baby'}
                size={120}
                color={theme.colors.primary}
              />
              <Text style={[styles.diagramText, { color: theme.colors.onSurfaceVariant }]}>
                Use a measuring tape for accurate measurements
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Measurements Form */}
        <Card style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Measurements
            </Text>
            <Text style={[styles.requiredNote, { color: theme.colors.onSurfaceVariant }]}>
              * Required measurements
            </Text>

            {measurementCategories.map((category, index) =>
              renderMeasurementInput(category, index)
            )}
          </Card.Content>
        </Card>

        {/* Taken By */}
        <Card style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Measured By
            </Text>
            <View style={styles.employeeChips}>
              {employees?.filter(emp => emp.isActive).map(employee => (
                <Chip
                  key={employee.id}
                  selected={takenBy === employee.id}
                  onPress={() => setTakenBy(employee.id)}
                  style={styles.employeeChip}
                >
                  {employee.name}
                </Chip>
              ))}
            </View>
          </Card.Content>
        </Card>

        {/* Notes */}
        <Card style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Notes
            </Text>
            <TextInput
              mode="outlined"
              value={notes}
              onChangeText={setNotes}
              placeholder="Any special notes or instructions..."
              multiline
              numberOfLines={3}
              style={styles.notesInput}
            />
          </Card.Content>
        </Card>

        {/* Save Button */}
        <View style={styles.saveButtonContainer}>
          <Button
            mode="contained"
            onPress={handleSave}
            loading={loading}
            disabled={loading}
            style={styles.saveButton}
            contentStyle={styles.saveButtonContent}
          >
            {isEditing ? 'Update Measurements' : 'Save Measurements'}
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.md,
  },
  customerCard: {
    marginVertical: SPACING.md,
    borderRadius: SHAPE.corner.lg,
  },
  customerName: {
    fontSize: TYPOGRAPHY.title.medium.fontSize,
    fontWeight: TYPOGRAPHY.title.medium.fontWeight,
  },
  customerPhone: {
    fontSize: TYPOGRAPHY.body.medium.fontSize,
    marginTop: SPACING.xs,
  },
  sectionCard: {
    marginBottom: SPACING.md,
    borderRadius: SHAPE.corner.lg,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.title.medium.fontSize,
    fontWeight: TYPOGRAPHY.title.medium.fontWeight,
    marginBottom: SPACING.md,
  },
  segmentedButtons: {
    marginTop: SPACING.sm,
  },
  diagramContainer: {
    alignItems: 'center',
    paddingVertical: SPACING.lg,
  },
  diagramText: {
    fontSize: TYPOGRAPHY.body.small.fontSize,
    marginTop: SPACING.md,
    textAlign: 'center',
  },
  requiredNote: {
    fontSize: TYPOGRAPHY.body.small.fontSize,
    fontStyle: 'italic',
    marginBottom: SPACING.md,
  },
  measurementRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  measurementLabel: {
    flex: 1,
    marginRight: SPACING.md,
  },
  labelText: {
    fontSize: TYPOGRAPHY.body.medium.fontSize,
    fontWeight: '500',
  },
  unitText: {
    fontSize: TYPOGRAPHY.body.small.fontSize,
  },
  measurementInput: {
    width: 100,
  },
  inputContent: {
    textAlign: 'center',
  },
  employeeChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  employeeChip: {
    marginBottom: SPACING.xs,
  },
  notesInput: {
    marginTop: SPACING.sm,
  },
  saveButtonContainer: {
    paddingVertical: SPACING.xl,
  },
  saveButton: {
    borderRadius: SHAPE.corner.lg,
  },
  saveButtonContent: {
    paddingVertical: SPACING.sm,
  },
});

export default MeasurementsScreen;
