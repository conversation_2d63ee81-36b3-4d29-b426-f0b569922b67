import React from 'react';
import { ScrollView, View, StyleSheet, Linking } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  Text,
  Surface,
  Button,
  List,
  Divider,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../theme/designTokens';

const AboutScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();

  const appInfo = {
    name: 'Bakery Management App',
    version: '2.1.0',
    buildNumber: '2024.12.1',
    releaseDate: 'December 2024',
    platform: Platform.OS === 'ios' ? 'iOS' : 'Android',
    developer: 'Bakery Solutions Inc.',
    website: 'https://bakeryapp.com',
    privacy: 'https://bakeryapp.com/privacy',
    terms: 'https://bakeryapp.com/terms',
    description: 'A comprehensive business management app designed specifically for bakeries and food service businesses with enterprise-grade features.',
  };

  const features = [
    {
      icon: 'clipboard-list',
      title: 'Order Management',
      description: 'Create, track, and manage customer orders with status updates',
      color: '#4CAF50'
    },
    {
      icon: 'food-croissant',
      title: 'Product Catalog',
      description: 'Manage your bakery products, inventory, and pricing',
      color: '#FF9800'
    },
    {
      icon: 'account-group',
      title: 'Customer Management',
      description: 'Keep track of customer information and order history',
      color: '#2196F3'
    },
    {
      icon: 'chart-line',
      title: 'Financial Analytics',
      description: 'Monitor sales, expenses, and profitability in real-time',
      color: '#9C27B0'
    },
    {
      icon: 'file-chart',
      title: 'Advanced Reports',
      description: 'Generate detailed business reports and export as PDF/Excel',
      color: '#F44336'
    },
    {
      icon: 'cloud-upload',
      title: 'Data Backup',
      description: 'Secure backup and restore of your business data',
      color: '#607D8B'
    },
    {
      icon: 'camera',
      title: 'Image Management',
      description: 'Add photos to products and orders for better organization',
      color: '#795548'
    },
    {
      icon: 'search',
      title: 'Smart Search',
      description: 'Quickly find products, orders, and customers with intelligent search',
      color: '#009688'
    },
  ];



  const achievements = [
    { icon: 'star', title: '4.8/5 Rating', description: 'App Store & Google Play' },
    { icon: 'download', title: '50K+ Downloads', description: 'Trusted by bakeries worldwide' },
    { icon: 'shield-check', title: 'Enterprise Security', description: 'Bank-level data protection' },
    { icon: 'update', title: 'Regular Updates', description: 'Monthly feature releases' },
  ];

  const links = [
    {
      title: 'Privacy Policy',
      icon: 'shield-check',
      action: () => Linking.openURL('https://sweetdelights.com/privacy'),
    },
    {
      title: 'Terms of Service',
      icon: 'file-document',
      action: () => Linking.openURL('https://sweetdelights.com/terms'),
    },
    {
      title: 'Website',
      icon: 'web',
      action: () => Linking.openURL('https://sweetdelights.com'),
    },
    {
      title: 'Support Center',
      icon: 'help-circle',
      action: () => navigation.navigate('ContactSupport'),
    },
  ];

  const socialLinks = [
    {
      name: 'Facebook',
      icon: 'facebook',
      url: 'https://facebook.com/sweetdelights',
      color: '#1877F2',
    },
    {
      name: 'Twitter',
      icon: 'twitter',
      url: 'https://twitter.com/sweetdelights',
      color: '#1DA1F2',
    },
    {
      name: 'Instagram',
      icon: 'instagram',
      url: 'https://instagram.com/sweetdelights',
      color: '#E4405F',
    },
    {
      name: 'LinkedIn',
      icon: 'linkedin',
      url: 'https://linkedin.com/company/sweetdelights',
      color: '#0A66C2',
    },
  ];

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="About"
        subtitle="App information and details"
        showSearch={false}
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* App Info */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <View style={styles.appHeader}>
            <View style={[styles.appIcon, { backgroundColor: theme.colors.primary }]}>
              <Icon name="food-croissant" size={48} color={theme.colors.onPrimary} />
            </View>
            <View style={styles.appInfo}>
              <Text variant="headlineSmall" style={{ color: theme.colors.onSurface, fontWeight: '700' }}>
                {appInfo.name}
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, marginTop: 4 }}>
                Version {appInfo.version} (Build {appInfo.buildNumber})
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 2 }}>
                Released {appInfo.releaseDate}
              </Text>
            </View>
          </View>

          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, marginTop: SPACING.md, lineHeight: 22 }}>
            {appInfo.description}
          </Text>
        </Surface>

        {/* Features */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Key Features
          </Text>

          {features.map((feature, index) => (
            <View key={index}>
              <View style={styles.featureItem}>
                <View style={[styles.featureIcon, { backgroundColor: theme.colors.primary + '15' }]}>
                  <Icon name={feature.icon} size={24} color={theme.colors.primary} />
                </View>
                <View style={styles.featureText}>
                  <Text variant="bodyLarge" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                    {feature.title}
                  </Text>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, marginTop: 2 }}>
                    {feature.description}
                  </Text>
                </View>
              </View>
              {index < features.length - 1 && <Divider style={styles.divider} />}
            </View>
          ))}
        </Surface>

        {/* Developer Info */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Developer
          </Text>

          <View style={styles.developerInfo}>
            <View style={[styles.developerIcon, { backgroundColor: theme.colors.secondary + '15' }]}>
              <Icon name="code-tags" size={32} color={theme.colors.secondary} />
            </View>
            <View style={styles.developerText}>
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, fontWeight: '700' }}>
                {appInfo.developer}
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, marginTop: 4 }}>
                Specialized in business management solutions
              </Text>
            </View>
          </View>
        </Surface>

        {/* Links */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Links & Resources
          </Text>

          {links.map((link, index) => (
            <List.Item
              key={index}
              title={link.title}
              left={props => (
                <View style={[styles.linkIcon, { backgroundColor: theme.colors.primary + '15' }]}>
                  <Icon name={link.icon} size={20} color={theme.colors.primary} />
                </View>
              )}
              right={props => <Icon name="chevron-right" size={20} color={theme.colors.onSurfaceVariant} />}
              onPress={link.action}
              style={styles.linkItem}
            />
          ))}
        </Surface>

        {/* Social Media */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Follow Us
          </Text>

          <View style={styles.socialContainer}>
            {socialLinks.map((social, index) => (
              <Button
                key={index}
                mode="outlined"
                icon={social.icon}
                onPress={() => Linking.openURL(social.url)}
                style={[styles.socialButton, { borderColor: social.color }]}
                textColor={social.color}
              >
                {social.name}
              </Button>
            ))}
          </View>
        </Surface>

        {/* Copyright */}
        <View style={styles.copyright}>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center' }}>
            © 2024 {appInfo.developer}. All rights reserved.
          </Text>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center', marginTop: 4 }}>
            Made with ❤️ for bakery businesses
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  section: {
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  sectionTitle: {
    fontWeight: '700',
    marginBottom: SPACING.md,
  },
  appHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  appIcon: {
    width: 80,
    height: 80,
    borderRadius: BORDER_RADIUS.xl,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  appInfo: {
    flex: 1,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  featureText: {
    flex: 1,
  },
  divider: {
    marginVertical: SPACING.xs,
  },
  developerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  developerIcon: {
    width: 56,
    height: 56,
    borderRadius: BORDER_RADIUS.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  developerText: {
    flex: 1,
  },
  linkItem: {
    paddingVertical: SPACING.xs,
  },
  linkIcon: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.sm,
  },
  socialContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  socialButton: {
    flex: 1,
    minWidth: '45%',
  },
  copyright: {
    paddingVertical: SPACING.xl,
    alignItems: 'center',
  },
});

export default AboutScreen;
