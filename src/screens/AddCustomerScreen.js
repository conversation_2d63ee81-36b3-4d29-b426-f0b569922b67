import React, { useState } from 'react';
import { ScrollView, View, StyleSheet, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  Text,
  TextInput,
  Button,
  Surface,
  Switch,
  Chip,
  IconButton,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';

const AddCustomerScreen = ({ route }) => {
  const { theme } = useTheme();
  const { actions } = useData();
  const navigation = useNavigation();

  // Get customer from route params for editing
  const editingCustomer = route?.params?.customer;
  const isEditing = !!editingCustomer;

  // Customer form state
  const [customerData, setCustomerData] = useState({
    name: editingCustomer?.name || '',
    email: editingCustomer?.email || '',
    phone: editingCustomer?.phone || '',
    address: editingCustomer?.address || '',
    notes: editingCustomer?.notes || '',
    isVIP: editingCustomer?.isVIP || false,
    tags: editingCustomer?.tags || [],
  });

  const [newTag, setNewTag] = useState('');
  const [errors, setErrors] = useState({});

  // Predefined customer tags
  const predefinedTags = [
    'Regular', 'VIP', 'Corporate', 'Birthday', 'Wedding', 'Wholesale'
  ];

  const handleInputChange = (field, value) => {
    setCustomerData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const addTag = (tag) => {
    if (tag && !customerData.tags.includes(tag)) {
      setCustomerData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove) => {
    setCustomerData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!customerData.name.trim()) {
      newErrors.name = 'Customer name is required';
    }

    if (!customerData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^\+?[\d\s\-\(\)]+$/.test(customerData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    if (customerData.email && !/\S+@\S+\.\S+/.test(customerData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors and try again.');
      return;
    }

    try {
      const customerToSave = {
        ...customerData,
        updatedAt: new Date().toISOString(),
      };

      if (isEditing) {
        // Update existing customer
        const updatedCustomer = {
          ...editingCustomer,
          ...customerToSave,
        };
        actions.updateCustomer(updatedCustomer);

        Alert.alert(
          'Success',
          'Customer updated successfully!',
          [
            {
              text: 'Done',
              onPress: () => navigation.goBack(),
              style: 'default'
            }
          ]
        );
      } else {
        // Add new customer
        const newCustomer = {
          id: Date.now().toString(),
          ...customerToSave,
          totalOrders: 0,
          totalSpent: 0,
          createdAt: new Date().toISOString(),
        };

        actions.addCustomer(newCustomer);

        Alert.alert(
          'Success',
          'Customer added successfully!',
          [
            {
              text: 'Add Another',
              onPress: () => {
                setCustomerData({
                  name: '',
                  email: '',
                  phone: '',
                  address: '',
                  notes: '',
                  isVIP: false,
                  tags: [],
                });
              }
            },
            {
              text: 'Done',
              onPress: () => navigation.goBack(),
              style: 'default'
            }
          ]
        );
      }
    } catch (error) {
      Alert.alert('Error', `Failed to ${isEditing ? 'update' : 'add'} customer. Please try again.`);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title={isEditing ? "Edit Customer" : "Add Customer"}
        subtitle={isEditing ? "Update customer details" : "Register new customer"}
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Basic Information */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Basic Information
          </Text>

          <TextInput
            label="Customer Name *"
            value={customerData.name}
            onChangeText={(text) => handleInputChange('name', text)}
            mode="outlined"
            style={styles.input}
            error={!!errors.name}
            left={<TextInput.Icon icon="account" />}
          />
          {errors.name && (
            <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
              {errors.name}
            </Text>
          )}

          <TextInput
            label="Phone Number *"
            value={customerData.phone}
            onChangeText={(text) => handleInputChange('phone', text)}
            mode="outlined"
            style={styles.input}
            error={!!errors.phone}
            keyboardType="phone-pad"
            left={<TextInput.Icon icon="phone" />}
          />
          {errors.phone && (
            <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
              {errors.phone}
            </Text>
          )}

          <TextInput
            label="Email Address"
            value={customerData.email}
            onChangeText={(text) => handleInputChange('email', text)}
            mode="outlined"
            style={styles.input}
            error={!!errors.email}
            keyboardType="email-address"
            autoCapitalize="none"
            left={<TextInput.Icon icon="email" />}
          />
          {errors.email && (
            <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
              {errors.email}
            </Text>
          )}

          <TextInput
            label="Address"
            value={customerData.address}
            onChangeText={(text) => handleInputChange('address', text)}
            mode="outlined"
            style={styles.input}
            multiline
            numberOfLines={2}
            left={<TextInput.Icon icon="map-marker" />}
          />
        </Surface>

        {/* Customer Settings */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Customer Settings
          </Text>

          <View style={styles.switchRow}>
            <View style={styles.switchLabel}>
              <Icon name="crown" size={20} color={theme.colors.primary} />
              <Text variant="bodyLarge" style={{ color: theme.colors.onSurface, marginLeft: 8 }}>
                VIP Customer
              </Text>
            </View>
            <Switch
              value={customerData.isVIP}
              onValueChange={(value) => handleInputChange('isVIP', value)}
            />
          </View>

          <TextInput
            label="Notes"
            value={customerData.notes}
            onChangeText={(text) => handleInputChange('notes', text)}
            mode="outlined"
            style={styles.input}
            multiline
            numberOfLines={3}
            placeholder="Special preferences, allergies, etc."
            left={<TextInput.Icon icon="note-text" />}
          />
        </Surface>

        {/* Customer Tags */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Customer Tags
          </Text>

          {/* Predefined Tags */}
          <Text variant="bodyMedium" style={[styles.subsectionTitle, { color: theme.colors.onSurfaceVariant }]}>
            Quick Tags
          </Text>
          <View style={styles.tagsContainer}>
            {predefinedTags.map((tag) => (
              <Chip
                key={tag}
                mode={customerData.tags.includes(tag) ? 'flat' : 'outlined'}
                selected={customerData.tags.includes(tag)}
                onPress={() => {
                  if (customerData.tags.includes(tag)) {
                    removeTag(tag);
                  } else {
                    addTag(tag);
                  }
                }}
                style={styles.tag}
              >
                {tag}
              </Chip>
            ))}
          </View>

          {/* Custom Tag Input */}
          <View style={styles.customTagRow}>
            <TextInput
              label="Add Custom Tag"
              value={newTag}
              onChangeText={setNewTag}
              mode="outlined"
              style={styles.tagInput}
              onSubmitEditing={() => addTag(newTag)}
            />
            <IconButton
              icon="plus"
              mode="contained"
              onPress={() => addTag(newTag)}
              disabled={!newTag.trim()}
            />
          </View>

          {/* Selected Tags */}
          {customerData.tags.length > 0 && (
            <View>
              <Text variant="bodyMedium" style={[styles.subsectionTitle, { color: theme.colors.onSurfaceVariant }]}>
                Selected Tags
              </Text>
              <View style={styles.tagsContainer}>
                {customerData.tags.map((tag) => (
                  <Chip
                    key={tag}
                    mode="flat"
                    onClose={() => removeTag(tag)}
                    style={styles.selectedTag}
                  >
                    {tag}
                  </Chip>
                ))}
              </View>
            </View>
          )}
        </Surface>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={() => navigation.goBack()}
            style={[styles.button, styles.cancelButton]}
          >
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={handleSave}
            style={[styles.button, styles.saveButton]}
            icon="check"
          >
            {isEditing ? 'Update Customer' : 'Save Customer'}
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    padding: 16,
    marginVertical: 8,
    borderRadius: 12,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 16,
  },
  subsectionTitle: {
    fontWeight: '500',
    marginBottom: 8,
    marginTop: 8,
  },
  input: {
    marginBottom: 8,
  },
  errorText: {
    marginBottom: 8,
    marginLeft: 4,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchLabel: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 8,
  },
  tag: {
    marginBottom: 4,
  },
  selectedTag: {
    marginBottom: 4,
  },
  customTagRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  tagInput: {
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginVertical: 24,
    paddingBottom: 32,
  },
  button: {
    flex: 1,
  },
  cancelButton: {
    marginRight: 6,
  },
  saveButton: {
    marginLeft: 6,
  },
});

export default AddCustomerScreen;
