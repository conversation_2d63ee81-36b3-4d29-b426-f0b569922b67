import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useData } from '../context/DataContext';
import UnifiedBottomSheet from '../components/UnifiedBottomSheet';

const TailoringOrderScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { addOrder, customers, employees, products } = useData();
  const { order } = route.params || {};
  const isEditing = !!order;

  const [formData, setFormData] = useState({
    customerId: '',
    customerName: '',
    customerPhone: '',
    garmentType: '',
    fabric: '',
    measurements: {},
    specialInstructions: '',
    deliveryDate: '',
    urgency: 'normal',
    assignedTo: '',
    estimatedPrice: 0,
    advancePayment: 0,
    status: 'measurement_pending',
  });

  const [showCustomerSheet, setShowCustomerSheet] = useState(false);
  const [showEmployeeSheet, setShowEmployeeSheet] = useState(false);
  const [showGarmentSheet, setShowGarmentSheet] = useState(false);

  const garmentTypes = [
    { id: 'shirt', name: 'Shirt', basePrice: 800 },
    { id: 'pant', name: 'Pant', basePrice: 600 },
    { id: 'suit', name: 'Suit', basePrice: 2500 },
    { id: 'dress', name: 'Dress', basePrice: 1200 },
    { id: 'blouse', name: 'Blouse', basePrice: 700 },
    { id: 'skirt', name: 'Skirt', basePrice: 500 },
    { id: 'jacket', name: 'Jacket', basePrice: 1500 },
    { id: 'kurta', name: 'Kurta', basePrice: 900 },
  ];

  const urgencyLevels = [
    { id: 'normal', name: 'Normal', multiplier: 1, color: '#4CAF50' },
    { id: 'urgent', name: 'Urgent', multiplier: 1.5, color: '#FF9800' },
    { id: 'express', name: 'Express', multiplier: 2, color: '#F44336' },
  ];

  const orderStatuses = [
    'measurement_pending',
    'measurement_taken',
    'cutting',
    'stitching',
    'fitting',
    'finishing',
    'ready',
    'delivered',
  ];

  useEffect(() => {
    if (isEditing && order) {
      setFormData({
        customerId: order.customerId || '',
        customerName: order.customerName || '',
        customerPhone: order.customerPhone || '',
        garmentType: order.garmentType || '',
        fabric: order.fabric || '',
        measurements: order.measurements || {},
        specialInstructions: order.specialInstructions || '',
        deliveryDate: order.deliveryDate || '',
        urgency: order.urgency || 'normal',
        assignedTo: order.assignedTo || '',
        estimatedPrice: order.estimatedPrice || 0,
        advancePayment: order.advancePayment || 0,
        status: order.status || 'measurement_pending',
      });
    }
  }, [isEditing, order]);

  const updateFormData = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Auto-calculate price when garment type or urgency changes
    if (field === 'garmentType' || field === 'urgency') {
      calculatePrice(field === 'garmentType' ? value : formData.garmentType, field === 'urgency' ? value : formData.urgency);
    }
  };

  const calculatePrice = (garmentType, urgency) => {
    const garment = garmentTypes.find(g => g.id === garmentType);
    const urgencyLevel = urgencyLevels.find(u => u.id === urgency);
    
    if (garment && urgencyLevel) {
      const basePrice = garment.basePrice;
      const finalPrice = basePrice * urgencyLevel.multiplier;
      setFormData(prev => ({
        ...prev,
        estimatedPrice: finalPrice,
      }));
    }
  };

  const selectCustomer = (customer) => {
    setFormData(prev => ({
      ...prev,
      customerId: customer.id,
      customerName: customer.name,
      customerPhone: customer.phone,
    }));
    setShowCustomerSheet(false);
  };

  const selectEmployee = (employee) => {
    setFormData(prev => ({
      ...prev,
      assignedTo: employee.id,
    }));
    setShowEmployeeSheet(false);
  };

  const selectGarment = (garment) => {
    updateFormData('garmentType', garment.id);
    setShowGarmentSheet(false);
  };

  const validateForm = () => {
    if (!formData.customerName.trim()) {
      Alert.alert('Error', 'Customer name is required');
      return false;
    }
    if (!formData.garmentType) {
      Alert.alert('Error', 'Garment type is required');
      return false;
    }
    if (!formData.deliveryDate) {
      Alert.alert('Error', 'Delivery date is required');
      return false;
    }
    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      const orderData = {
        ...formData,
        id: isEditing ? order.id : undefined,
        orderDate: isEditing ? order.orderDate : new Date().toISOString(),
        total: formData.estimatedPrice,
        items: [{
          name: garmentTypes.find(g => g.id === formData.garmentType)?.name || formData.garmentType,
          quantity: 1,
          price: formData.estimatedPrice,
        }],
      };

      await addOrder(orderData);
      Alert.alert('Success', `Tailoring order ${isEditing ? 'updated' : 'created'} successfully`, [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to save order');
      console.error('Error saving order:', error);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    backButton: {
      marginRight: 16,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      flex: 1,
    },
    saveButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
    },
    saveButtonText: {
      color: 'white',
      fontWeight: '600',
    },
    scrollContent: {
      padding: 16,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    card: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    selectButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 12,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 8,
      backgroundColor: theme.colors.surface,
    },
    selectButtonText: {
      fontSize: 16,
      color: theme.colors.text,
    },
    selectButtonPlaceholder: {
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    urgencyContainer: {
      flexDirection: 'row',
      gap: 8,
    },
    urgencyButton: {
      flex: 1,
      padding: 12,
      borderRadius: 8,
      borderWidth: 1,
      alignItems: 'center',
    },
    urgencyButtonText: {
      fontSize: 14,
      fontWeight: '600',
    },
    priceContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 16,
      backgroundColor: theme.colors.primaryContainer,
      borderRadius: 8,
      marginTop: 8,
    },
    priceLabel: {
      fontSize: 16,
      color: theme.colors.onPrimaryContainer,
    },
    priceValue: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.onPrimaryContainer,
    },
  });

  const selectedGarment = garmentTypes.find(g => g.id === formData.garmentType);
  const selectedEmployee = employees.find(e => e.id === formData.assignedTo);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {isEditing ? 'Edit Order' : 'New Tailoring Order'}
        </Text>
        <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
          <Text style={styles.saveButtonText}>Save</Text>
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.scrollContent} showsVerticalScrollIndicator={false}>
          {/* Customer Selection */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Customer Information</Text>
            <TouchableOpacity
              style={styles.selectButton}
              onPress={() => setShowCustomerSheet(true)}
            >
              <Text style={formData.customerName ? styles.selectButtonText : styles.selectButtonPlaceholder}>
                {formData.customerName || 'Select Customer'}
              </Text>
              <Ionicons name="chevron-down" size={20} color={theme.colors.text} />
            </TouchableOpacity>
            {formData.customerPhone && (
              <Text style={{ marginTop: 8, color: theme.colors.textSecondary }}>
                {formData.customerPhone}
              </Text>
            )}
          </View>

          {/* Garment Selection */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Garment Details</Text>
            <TouchableOpacity
              style={styles.selectButton}
              onPress={() => setShowGarmentSheet(true)}
            >
              <Text style={selectedGarment ? styles.selectButtonText : styles.selectButtonPlaceholder}>
                {selectedGarment ? selectedGarment.name : 'Select Garment Type'}
              </Text>
              <Ionicons name="chevron-down" size={20} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          {/* Urgency Level */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Urgency Level</Text>
            <View style={styles.urgencyContainer}>
              {urgencyLevels.map((level) => (
                <TouchableOpacity
                  key={level.id}
                  style={[
                    styles.urgencyButton,
                    {
                      borderColor: formData.urgency === level.id ? level.color : theme.colors.border,
                      backgroundColor: formData.urgency === level.id ? level.color + '20' : theme.colors.surface,
                    },
                  ]}
                  onPress={() => updateFormData('urgency', level.id)}
                >
                  <Text
                    style={[
                      styles.urgencyButtonText,
                      { color: formData.urgency === level.id ? level.color : theme.colors.text },
                    ]}
                  >
                    {level.name}
                  </Text>
                  <Text
                    style={[
                      { fontSize: 12, marginTop: 2 },
                      { color: formData.urgency === level.id ? level.color : theme.colors.textSecondary },
                    ]}
                  >
                    {level.multiplier}x
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Employee Assignment */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Assign Tailor</Text>
            <TouchableOpacity
              style={styles.selectButton}
              onPress={() => setShowEmployeeSheet(true)}
            >
              <Text style={selectedEmployee ? styles.selectButtonText : styles.selectButtonPlaceholder}>
                {selectedEmployee ? selectedEmployee.name : 'Select Tailor'}
              </Text>
              <Ionicons name="chevron-down" size={20} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          {/* Price Summary */}
          {formData.estimatedPrice > 0 && (
            <View style={styles.priceContainer}>
              <Text style={styles.priceLabel}>Estimated Price</Text>
              <Text style={styles.priceValue}>৳{formData.estimatedPrice}</Text>
            </View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Customer Selection Bottom Sheet */}
      <UnifiedBottomSheet
        isVisible={showCustomerSheet}
        onClose={() => setShowCustomerSheet(false)}
        title="Select Customer"
        data={customers}
        onSelect={selectCustomer}
        renderItem={(customer) => (
          <View>
            <Text style={{ fontSize: 16, fontWeight: '600', color: theme.colors.text }}>
              {customer.name}
            </Text>
            <Text style={{ fontSize: 14, color: theme.colors.textSecondary, marginTop: 2 }}>
              {customer.phone}
            </Text>
          </View>
        )}
      />

      {/* Employee Selection Bottom Sheet */}
      <UnifiedBottomSheet
        isVisible={showEmployeeSheet}
        onClose={() => setShowEmployeeSheet(false)}
        title="Select Tailor"
        data={employees.filter(emp => emp.isActive)}
        onSelect={selectEmployee}
        renderItem={(employee) => (
          <View>
            <Text style={{ fontSize: 16, fontWeight: '600', color: theme.colors.text }}>
              {employee.name}
            </Text>
            <Text style={{ fontSize: 14, color: theme.colors.textSecondary, marginTop: 2 }}>
              {employee.role} • {employee.skills?.join(', ')}
            </Text>
          </View>
        )}
      />

      {/* Garment Selection Bottom Sheet */}
      <UnifiedBottomSheet
        isVisible={showGarmentSheet}
        onClose={() => setShowGarmentSheet(false)}
        title="Select Garment Type"
        data={garmentTypes}
        onSelect={selectGarment}
        renderItem={(garment) => (
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text style={{ fontSize: 16, fontWeight: '600', color: theme.colors.text }}>
              {garment.name}
            </Text>
            <Text style={{ fontSize: 14, color: theme.colors.primary, fontWeight: '600' }}>
              ৳{garment.basePrice}
            </Text>
          </View>
        )}
      />
    </SafeAreaView>
  );
};

export default TailoringOrderScreen;
