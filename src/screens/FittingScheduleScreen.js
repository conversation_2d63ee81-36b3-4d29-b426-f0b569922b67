import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useData } from '../context/DataContext';

const FittingScheduleScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { fittings, orders, customers, addFitting, updateFitting } = useData();
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [viewMode, setViewMode] = useState('day'); // 'day', 'week', 'month'

  const timeSlots = [
    '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
    '12:00', '12:30', '14:00', '14:30', '15:00', '15:30',
    '16:00', '16:30', '17:00', '17:30', '18:00'
  ];

  const getDateString = (date) => {
    return date.toISOString().split('T')[0];
  };

  const getFittingsForDate = (date) => {
    const dateString = getDateString(date);
    return fittings.filter(fitting => 
      fitting.scheduledDate === dateString
    ).sort((a, b) => a.scheduledTime.localeCompare(b.scheduledTime));
  };

  const isTimeSlotBooked = (time) => {
    const dateString = getDateString(selectedDate);
    return fittings.some(fitting => 
      fitting.scheduledDate === dateString && 
      fitting.scheduledTime === time &&
      fitting.status !== 'cancelled'
    );
  };

  const getCustomerName = (customerId) => {
    const customer = customers.find(c => c.id === customerId);
    return customer ? customer.name : 'Unknown Customer';
  };

  const getOrderDetails = (orderId) => {
    const order = orders.find(o => o.id === orderId);
    return order ? `${order.garmentType} - ${order.status}` : 'Unknown Order';
  };

  const handleScheduleFitting = (time) => {
    if (isTimeSlotBooked(time)) {
      Alert.alert('Time Slot Unavailable', 'This time slot is already booked.');
      return;
    }

    navigation.navigate('AddFitting', {
      scheduledDate: getDateString(selectedDate),
      scheduledTime: time,
    });
  };

  const handleFittingPress = (fitting) => {
    Alert.alert(
      'Fitting Details',
      `Customer: ${getCustomerName(fitting.customerId)}\nOrder: ${getOrderDetails(fitting.orderId)}\nTime: ${fitting.scheduledTime}\nStatus: ${fitting.status}`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Edit', onPress: () => navigation.navigate('AddFitting', { fitting }) },
        { text: 'Complete', onPress: () => completeFitting(fitting) },
      ]
    );
  };

  const completeFitting = async (fitting) => {
    try {
      await updateFitting({
        ...fitting,
        status: 'completed',
        completedAt: new Date().toISOString(),
      });
      Alert.alert('Success', 'Fitting marked as completed');
    } catch (error) {
      Alert.alert('Error', 'Failed to update fitting');
    }
  };

  const navigateDate = (direction) => {
    const newDate = new Date(selectedDate);
    if (viewMode === 'day') {
      newDate.setDate(newDate.getDate() + direction);
    } else if (viewMode === 'week') {
      newDate.setDate(newDate.getDate() + (direction * 7));
    } else {
      newDate.setMonth(newDate.getMonth() + direction);
    }
    setSelectedDate(newDate);
  };

  const formatDate = (date) => {
    const options = { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    };
    return date.toLocaleDateString('en-US', options);
  };

  const renderTimeSlot = ({ item: time }) => {
    const isBooked = isTimeSlotBooked(time);
    const fitting = fittings.find(f => 
      f.scheduledDate === getDateString(selectedDate) && 
      f.scheduledTime === time
    );

    return (
      <TouchableOpacity
        style={[
          styles.timeSlot,
          {
            backgroundColor: isBooked 
              ? (fitting?.status === 'completed' ? theme.colors.success + '20' : theme.colors.primary + '20')
              : theme.colors.surface,
            borderColor: isBooked 
              ? (fitting?.status === 'completed' ? theme.colors.success : theme.colors.primary)
              : theme.colors.border,
          },
        ]}
        onPress={() => isBooked ? handleFittingPress(fitting) : handleScheduleFitting(time)}
      >
        <Text style={[styles.timeText, { color: theme.colors.text }]}>
          {time}
        </Text>
        {isBooked && fitting && (
          <View style={styles.fittingInfo}>
            <Text style={[styles.customerName, { color: theme.colors.text }]} numberOfLines={1}>
              {getCustomerName(fitting.customerId)}
            </Text>
            <Text style={[styles.orderInfo, { color: theme.colors.textSecondary }]} numberOfLines={1}>
              {getOrderDetails(fitting.orderId)}
            </Text>
            <View style={[
              styles.statusBadge,
              { backgroundColor: fitting.status === 'completed' ? theme.colors.success : theme.colors.warning }
            ]}>
              <Text style={styles.statusText}>
                {fitting.status === 'completed' ? 'Done' : 'Scheduled'}
              </Text>
            </View>
          </View>
        )}
        {!isBooked && (
          <Text style={[styles.availableText, { color: theme.colors.textSecondary }]}>
            Available
          </Text>
        )}
      </TouchableOpacity>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    backButton: {
      marginRight: 16,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      flex: 1,
    },
    addButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
    },
    addButtonText: {
      color: 'white',
      fontWeight: '600',
    },
    dateNavigation: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
      backgroundColor: theme.colors.surface,
    },
    navButton: {
      padding: 8,
    },
    dateText: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      textAlign: 'center',
      flex: 1,
    },
    viewModeContainer: {
      flexDirection: 'row',
      padding: 16,
      gap: 8,
    },
    viewModeButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    viewModeButtonActive: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    viewModeText: {
      fontSize: 14,
      color: theme.colors.text,
    },
    viewModeTextActive: {
      color: 'white',
    },
    content: {
      flex: 1,
      padding: 16,
    },
    timeSlot: {
      borderWidth: 1,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      minHeight: 80,
    },
    timeText: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 4,
    },
    fittingInfo: {
      flex: 1,
    },
    customerName: {
      fontSize: 14,
      fontWeight: '500',
      marginBottom: 2,
    },
    orderInfo: {
      fontSize: 12,
      marginBottom: 4,
    },
    statusBadge: {
      alignSelf: 'flex-start',
      paddingHorizontal: 8,
      paddingVertical: 2,
      borderRadius: 10,
    },
    statusText: {
      fontSize: 10,
      color: 'white',
      fontWeight: '600',
    },
    availableText: {
      fontSize: 12,
      fontStyle: 'italic',
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: 40,
    },
    emptyStateText: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginTop: 16,
    },
    summary: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      padding: 16,
      backgroundColor: theme.colors.surface,
      marginBottom: 16,
      borderRadius: 12,
    },
    summaryItem: {
      alignItems: 'center',
    },
    summaryNumber: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    summaryLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginTop: 4,
    },
  });

  const todaysFittings = getFittingsForDate(selectedDate);
  const completedToday = todaysFittings.filter(f => f.status === 'completed').length;
  const scheduledToday = todaysFittings.filter(f => f.status === 'scheduled').length;

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Fitting Schedule</Text>
        <TouchableOpacity 
          style={styles.addButton} 
          onPress={() => navigation.navigate('AddFitting')}
        >
          <Text style={styles.addButtonText}>Add</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.dateNavigation}>
        <TouchableOpacity style={styles.navButton} onPress={() => navigateDate(-1)}>
          <Ionicons name="chevron-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.dateText}>{formatDate(selectedDate)}</Text>
        <TouchableOpacity style={styles.navButton} onPress={() => navigateDate(1)}>
          <Ionicons name="chevron-forward" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <View style={styles.viewModeContainer}>
        {['day', 'week', 'month'].map((mode) => (
          <TouchableOpacity
            key={mode}
            style={[
              styles.viewModeButton,
              viewMode === mode && styles.viewModeButtonActive,
            ]}
            onPress={() => setViewMode(mode)}
          >
            <Text
              style={[
                styles.viewModeText,
                viewMode === mode && styles.viewModeTextActive,
              ]}
            >
              {mode.charAt(0).toUpperCase() + mode.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.summary}>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryNumber}>{scheduledToday}</Text>
          <Text style={styles.summaryLabel}>Scheduled</Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryNumber}>{completedToday}</Text>
          <Text style={styles.summaryLabel}>Completed</Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryNumber}>{timeSlots.length - todaysFittings.length}</Text>
          <Text style={styles.summaryLabel}>Available</Text>
        </View>
      </View>

      <FlatList
        data={timeSlots}
        renderItem={renderTimeSlot}
        keyExtractor={(item) => item}
        style={styles.content}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Ionicons name="calendar-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={styles.emptyStateText}>
              No time slots available for this date
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

export default FittingScheduleScreen;
