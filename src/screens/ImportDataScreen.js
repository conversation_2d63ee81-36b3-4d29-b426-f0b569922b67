import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import {
  Text,
  Button,
  Surface,
  Card,
  IconButton,
  Divider,
  ProgressBar,
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../context/ThemeContext';
import { useData } from '../context/DataContext';
import { SPACING, SHAPE, TYPOGRAPHY, getBorderColor } from '../theme/designTokens';
import CommonHeader from '../components/CommonHeader';

const ImportDataScreen = () => {
  const { theme } = useTheme();
  const { actions } = useData();
  const navigation = useNavigation();

  const [importing, setImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [lastImport, setLastImport] = useState(null);
  const [generatingSample, setGeneratingSample] = useState(false);

  const handleImportData = async () => {
    Alert.alert(
      'Import Data',
      'File import functionality will be available in the next update. For now, you can manually add your data using the Add Product and Add Order screens.',
      [{ text: 'OK' }]
    );
  };

  const handleExportData = async () => {
    try {
      const exportData = actions.exportData();
      const jsonString = JSON.stringify(exportData, null, 2);

      console.log('Exported data:', exportData);

      Alert.alert(
        'Export Data',
        `Your data has been prepared for export!\n\nProducts: ${exportData.products?.length || 0}\nOrders: ${exportData.orders?.length || 0}\nCustomers: ${exportData.customers?.length || 0}\n\nData has been logged to console for now. File export functionality will be available in the next update.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Export error:', error);
      Alert.alert('Export Failed', `Failed to export data: ${error.message}`);
    }
  };

  const handleGenerateSampleData = async () => {
    Alert.alert(
      'Generate Sample Data',
      'This will generate 100 sample products and 100 sample customers. Any existing data will be cleared. Are you sure you want to continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Generate',
          style: 'destructive',
          onPress: async () => {
            try {
              setGeneratingSample(true);
              await actions.generateSampleData();
              Alert.alert(
                'Success',
                '100 products and 100 customers have been generated successfully!',
                [{ text: 'OK' }]
              );
            } catch (error) {
              console.error('Sample data generation error:', error);
              Alert.alert('Generation Failed', `Failed to generate sample data: ${error.message}`);
            } finally {
              setGeneratingSample(false);
            }
          }
        }
      ]
    );
  };

  const importFormats = [
    {
      title: 'JSON Format',
      description: 'Standard JSON file with products, orders, and customers',
      icon: 'code-json',
      supported: true,
    },
    {
      title: 'CSV Format',
      description: 'Comma-separated values (coming soon)',
      icon: 'file-delimited',
      supported: false,
    },
    {
      title: 'Excel Format',
      description: 'Microsoft Excel files (coming soon)',
      icon: 'file-excel',
      supported: false,
    },
  ];

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Data Backup"
        subtitle="Import & export business data"
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Import Section */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <View style={styles.sectionHeader}>
            <Icon name="import" size={24} color={theme.colors.primary} />
            <Text style={[styles.sectionTitle, {
              color: theme.colors.onSurface,
              fontSize: TYPOGRAPHY.sectionTitle.fontSize,
              fontWeight: TYPOGRAPHY.sectionTitle.fontWeight,
              lineHeight: TYPOGRAPHY.sectionTitle.lineHeight * TYPOGRAPHY.sectionTitle.fontSize
            }]}>
              Import Data
            </Text>
          </View>

          <Text variant="bodyMedium" style={[styles.description, { color: theme.colors.onSurfaceVariant }]}>
            Import your business data from a backup file. Supported formats include JSON files exported from this app.
          </Text>

          {importing && (
            <View style={styles.progressContainer}>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginBottom: 8 }}>
                Importing data... {Math.round(importProgress * 100)}%
              </Text>
              <ProgressBar progress={importProgress} color={theme.colors.primary} />
            </View>
          )}

          <Button
            mode="contained"
            icon="file-import"
            onPress={handleImportData}
            loading={importing}
            disabled={importing}
            style={styles.actionButton}
          >
            {importing ? 'Importing...' : 'Select File to Import'}
          </Button>

          {lastImport && (
            <Card style={[styles.lastImportCard, { backgroundColor: theme.colors.surfaceVariant }]}>
              <Card.Content>
                <Text variant="titleSmall" style={{ color: theme.colors.onSurfaceVariant }}>
                  Last Import
                </Text>
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 4 }}>
                  {lastImport.filename} • {new Date(lastImport.date).toLocaleDateString()}
                </Text>
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 4 }}>
                  {lastImport.productsCount} products, {lastImport.ordersCount} orders, {lastImport.customersCount} customers
                </Text>
              </Card.Content>
            </Card>
          )}
        </Surface>

        {/* Sample Data Section */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <View style={styles.sectionHeader}>
            <Icon name="database-plus" size={24} color={theme.colors.tertiary} />
            <Text style={[styles.sectionTitle, {
              color: theme.colors.onSurface,
              fontSize: TYPOGRAPHY.sectionTitle.fontSize,
              fontWeight: TYPOGRAPHY.sectionTitle.fontWeight,
              lineHeight: TYPOGRAPHY.sectionTitle.lineHeight * TYPOGRAPHY.sectionTitle.fontSize
            }]}>
              Generate Sample Data
            </Text>
          </View>

          <Text variant="bodyMedium" style={[styles.description, { color: theme.colors.onSurfaceVariant }]}>
            Generate 100 sample products and 100 sample customers for testing and demonstration purposes. This will clear any existing data.
          </Text>

          <Button
            mode="contained"
            icon="auto-fix"
            onPress={handleGenerateSampleData}
            loading={generatingSample}
            disabled={generatingSample}
            style={[styles.actionButton, { backgroundColor: theme.colors.tertiary }]}
            labelStyle={{ color: theme.colors.onTertiary }}
          >
            {generatingSample ? 'Generating...' : 'Generate Sample Data'}
          </Button>
        </Surface>

        {/* Export Section */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <View style={styles.sectionHeader}>
            <Icon name="export" size={24} color={theme.colors.secondary} />
            <Text style={[styles.sectionTitle, {
              color: theme.colors.onSurface,
              fontSize: TYPOGRAPHY.sectionTitle.fontSize,
              fontWeight: TYPOGRAPHY.sectionTitle.fontWeight,
              lineHeight: TYPOGRAPHY.sectionTitle.lineHeight * TYPOGRAPHY.sectionTitle.fontSize
            }]}>
              Export Data
            </Text>
          </View>

          <Text variant="bodyMedium" style={[styles.description, { color: theme.colors.onSurfaceVariant }]}>
            Create a backup of your current data. This will export all products, orders, customers, and settings.
          </Text>

          <Button
            mode="outlined"
            icon="file-export"
            onPress={handleExportData}
            style={styles.actionButton}
          >
            Export Current Data
          </Button>
        </Surface>

        {/* Supported Formats */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <View style={styles.sectionHeader}>
            <Icon name="file-multiple" size={24} color={theme.colors.tertiary} />
            <Text style={[styles.sectionTitle, {
              color: theme.colors.onSurface,
              fontSize: TYPOGRAPHY.sectionTitle.fontSize,
              fontWeight: TYPOGRAPHY.sectionTitle.fontWeight,
              lineHeight: TYPOGRAPHY.sectionTitle.lineHeight * TYPOGRAPHY.sectionTitle.fontSize
            }]}>
              Supported Formats
            </Text>
          </View>

          {importFormats.map((format, index) => (
            <View key={index}>
              <View style={styles.formatItem}>
                <Icon
                  name={format.icon}
                  size={20}
                  color={format.supported ? theme.colors.onSurface : theme.colors.onSurfaceVariant}
                />
                <View style={styles.formatInfo}>
                  <Text
                    variant="titleSmall"
                    style={{
                      color: format.supported ? theme.colors.onSurface : theme.colors.onSurfaceVariant
                    }}
                  >
                    {format.title}
                  </Text>
                  <Text
                    variant="bodySmall"
                    style={{
                      color: theme.colors.onSurfaceVariant,
                      marginTop: 2
                    }}
                  >
                    {format.description}
                  </Text>
                </View>
                {format.supported ? (
                  <Icon name="check-circle" size={20} color={theme.colors.primary} />
                ) : (
                  <Icon name="clock-outline" size={20} color={theme.colors.onSurfaceVariant} />
                )}
              </View>
              {index < importFormats.length - 1 && <Divider style={styles.formatDivider} />}
            </View>
          ))}
        </Surface>

        {/* Warning */}
        <Surface style={[styles.warningSection, { backgroundColor: theme.colors.errorContainer }]} elevation={1}>
          <Icon name="alert" size={24} color={theme.colors.onErrorContainer} />
          <View style={styles.warningContent}>
            <Text variant="titleSmall" style={{ color: theme.colors.onErrorContainer }}>
              Important Notice
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onErrorContainer, marginTop: 4 }}>
              Importing data will replace your current data. Make sure to export your current data first if you want to keep it.
            </Text>
          </View>
        </Surface>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  section: {
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
    borderRadius: SHAPE.corner.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    marginLeft: SPACING.md,
    fontWeight: '600',
  },
  description: {
    marginBottom: SPACING.lg,
    lineHeight: 20,
  },
  actionButton: {
    marginTop: SPACING.sm,
  },
  progressContainer: {
    marginBottom: SPACING.lg,
  },
  lastImportCard: {
    marginTop: SPACING.md,
  },
  formatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
  },
  formatInfo: {
    flex: 1,
    marginLeft: SPACING.md,
  },
  formatDivider: {
    marginVertical: SPACING.xs,
  },
  warningSection: {
    flexDirection: 'row',
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
    borderRadius: SHAPE.corner.xl,
  },
  warningContent: {
    flex: 1,
    marginLeft: SPACING.md,
  },
});

export default ImportDataScreen;
