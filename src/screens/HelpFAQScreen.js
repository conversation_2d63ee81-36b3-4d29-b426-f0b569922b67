import React, { useState } from 'react';
import { ScrollView, View, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  Text,
  Surface,
  List,
  Searchbar,
  Chip,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import { SPACING, SHAPE, TYPOGRAPHY } from '../theme/designTokens';

const HelpFAQScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', label: 'All', icon: 'help-circle' },
    { id: 'orders', label: 'Orders', icon: 'clipboard-list' },
    { id: 'products', label: 'Products', icon: 'food-croissant' },
    { id: 'financial', label: 'Financial', icon: 'chart-line' },
    { id: 'settings', label: 'Settings', icon: 'cog' },
  ];

  const faqData = [
    {
      id: 1,
      category: 'orders',
      question: 'How do I create a new order?',
      answer: 'To create a new order, tap the plus (+) button in the bottom navigation and select "Add Order". Fill in customer details, add products, and save. You can also add images and notes to orders.',
      tags: ['create', 'new', 'order', 'add'],
      helpful: 0,
    },
    {
      id: 2,
      category: 'orders',
      question: 'How can I edit an existing order?',
      answer: 'Go to the Orders tab, find your order, and tap on it. Then tap the edit button to modify order details. You can change status, add items, or update customer information.',
      tags: ['edit', 'modify', 'update', 'order'],
      helpful: 0,
    },
    {
      id: 3,
      category: 'orders',
      question: 'How do I change order status?',
      answer: 'Open any order by tapping on it, then use the status dropdown in the order details bottomsheet. You can change between Pending, Processing, Completed, or Cancelled.',
      tags: ['status', 'change', 'update', 'order'],
      helpful: 0,
    },
    {
      id: 4,
      category: 'products',
      question: 'How do I add a new product?',
      answer: 'Tap the plus (+) button and select "Add Product". Enter product details like name, price, description, stock quantity, and upload product images.',
      tags: ['create', 'new', 'product', 'add'],
      helpful: 0,
    },
    {
      id: 4,
      category: 'products',
      question: 'How do I manage product inventory?',
      answer: 'Go to the Products tab to view all products. You can edit stock quantities, update prices, and manage product details.',
    },
    {
      id: 5,
      category: 'financial',
      question: 'How do I view financial reports?',
      answer: 'Tap on "View Reports" from the dashboard or navigate to the Financial tab to see detailed profit/loss statements and analytics.',
    },
    {
      id: 6,
      category: 'financial',
      question: 'How do I export financial data?',
      answer: 'In the Financial section, you can export data as PDF or Excel files using the export buttons at the bottom of reports.',
    },
    {
      id: 7,
      category: 'settings',
      question: 'How do I backup my data?',
      answer: 'Go to Settings > Data Backup to export all your business data. You can also import previously exported data.',
    },
    {
      id: 8,
      category: 'settings',
      question: 'How do I change my profile information?',
      answer: 'Tap your profile picture in the top-right corner, then select "Edit Profile" to update your business information.',
    },
    {
      id: 9,
      category: 'orders',
      question: 'How do I generate invoices?',
      answer: 'In the Orders tab, tap on any order and select "Generate Invoice" to create a PDF invoice for your customer.',
    },
    {
      id: 10,
      category: 'products',
      question: 'Can I add product images?',
      answer: 'Yes! When adding or editing a product, you can upload images using the camera or gallery through the image picker. Images help customers identify products easily.',
      tags: ['image', 'photo', 'upload', 'product'],
      helpful: 0,
    },
    {
      id: 11,
      category: 'financial',
      question: 'How do I track expenses?',
      answer: 'Go to the Financial tab and tap "Add Expense". Enter expense details like amount, category, and description. You can also upload receipts.',
      tags: ['expense', 'track', 'financial', 'money'],
      helpful: 0,
    },
    {
      id: 12,
      category: 'financial',
      question: 'How do I generate reports?',
      answer: 'Navigate to Reports from the Financial section. Choose your date range and report type (sales, profit/loss, tax summary). You can export reports as PDF or Excel.',
      tags: ['report', 'export', 'pdf', 'excel'],
      helpful: 0,
    },
    {
      id: 13,
      category: 'customers',
      question: 'How do I manage customer information?',
      answer: 'Go to Customers tab to view all customers. Tap on any customer to see their details, order history, and contact information. You can edit or add new customers anytime.',
      tags: ['customer', 'manage', 'contact', 'history'],
      helpful: 0,
    },
    {
      id: 14,
      category: 'settings',
      question: 'How do I backup my data?',
      answer: 'Go to Settings > Data Backup. You can export your data as PDF or Excel files. Your data is automatically saved locally and can be restored if needed.',
      tags: ['backup', 'export', 'data', 'restore'],
      helpful: 0,
    },
    {
      id: 15,
      category: 'settings',
      question: 'How do I change app settings?',
      answer: 'Access Settings from the bottom navigation. You can customize notifications, currency, language, and other preferences to suit your business needs.',
      tags: ['settings', 'customize', 'preferences', 'notifications'],
      helpful: 0,
    },
  ];

  const filteredFAQs = faqData.filter(faq => {
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Help & FAQ"
        subtitle="Find answers to common questions"
        showSearch={false}
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Search Bar */}
        <Searchbar
          placeholder="Search help topics..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={[styles.searchBar, { backgroundColor: theme.colors.surfaceVariant }]}
          iconColor={theme.colors.onSurfaceVariant}
          inputStyle={{ color: theme.colors.onSurface }}
        />

        {/* Category Filters */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesContainer}>
          {categories.map((category) => (
            <Chip
              key={category.id}
              selected={selectedCategory === category.id}
              onPress={() => setSelectedCategory(category.id)}
              icon={category.icon}
              style={[
                styles.categoryChip,
                selectedCategory === category.id && { backgroundColor: theme.colors.primary }
              ]}
              textStyle={{
                color: selectedCategory === category.id ? theme.colors.onPrimary : theme.colors.onSurface
              }}
            >
              {category.label}
            </Chip>
          ))}
        </ScrollView>

        {/* FAQ List */}
        <Surface style={[styles.faqContainer, { backgroundColor: theme.colors.surface }]} elevation={1}>
          {filteredFAQs.map((faq) => (
            <List.Accordion
              key={faq.id}
              title={faq.question}
              titleStyle={{
                color: theme.colors.onSurface,
                ...TYPOGRAPHY.title.medium,
                fontWeight: '600'
              }}
              left={props => (
                <Icon
                  name="help-circle-outline"
                  size={24}
                  color={theme.colors.primary}
                  style={{ marginLeft: 8 }}
                />
              )}
              style={styles.accordionItem}
            >
              <View style={styles.answerContainer}>
                <Text
                  variant="bodyMedium"
                  style={{
                    color: theme.colors.onSurfaceVariant,
                    lineHeight: 22,
                    paddingHorizontal: 16,
                    paddingBottom: 16
                  }}
                >
                  {faq.answer}
                </Text>
              </View>
            </List.Accordion>
          ))}
        </Surface>

        {filteredFAQs.length === 0 && (
          <View style={styles.emptyState}>
            <Icon name="help-circle-outline" size={64} color={theme.colors.onSurfaceVariant} />
            <Text variant="titleMedium" style={{ color: theme.colors.onSurfaceVariant, marginTop: 16 }}>
              No results found
            </Text>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, marginTop: 8, textAlign: 'center' }}>
              Try adjusting your search or category filter
            </Text>
          </View>
        )}

        {/* Contact Support Section */}
        <Surface style={[styles.supportSection, { backgroundColor: theme.colors.primaryContainer }]} elevation={1}>
          <View style={styles.supportContent}>
            <Icon name="headset" size={32} color={theme.colors.onPrimaryContainer} />
            <View style={styles.supportText}>
              <Text variant="titleMedium" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '700' }}>
                Still need help?
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onPrimaryContainer, marginTop: 4 }}>
                Contact our support team for personalized assistance
              </Text>
            </View>
          </View>
        </Surface>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  searchBar: {
    marginBottom: SPACING.md,
    borderRadius: SHAPE.corner.lg,
  },
  categoriesContainer: {
    marginBottom: SPACING.lg,
  },
  categoryChip: {
    marginRight: SPACING.sm,
    marginBottom: SPACING.xs,
  },
  faqContainer: {
    borderRadius: SHAPE.corner.xl,
    marginBottom: SPACING.lg,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  accordionItem: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  answerContainer: {
    backgroundColor: 'rgba(0,0,0,0.02)',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xl * 2,
  },
  supportSection: {
    borderRadius: SHAPE.corner.xl,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  supportContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  supportText: {
    flex: 1,
    marginLeft: SPACING.md,
  },
});

export default HelpFAQScreen;
