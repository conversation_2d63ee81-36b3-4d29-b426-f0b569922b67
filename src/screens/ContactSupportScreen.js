import React, { useState } from 'react';
import { ScrollView, View, StyleSheet, Alert, Linking } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  Text,
  Surface,
  Button,
  TextInput,
  List,
  Chip,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import { SPACING, SHAPE, TYPOGRAPHY } from '../theme/designTokens';

const ContactSupportScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();

  const [supportForm, setSupportForm] = useState({
    subject: '',
    message: '',
    priority: 'medium',
    category: 'general',
  });

  const priorities = [
    { id: 'low', label: 'Low', color: '#4CAF50' },
    { id: 'medium', label: 'Medium', color: '#FF9800' },
    { id: 'high', label: 'High', color: '#F44336' },
  ];

  const categories = [
    { id: 'general', label: 'General', icon: 'help-circle' },
    { id: 'technical', label: 'Technical Issue', icon: 'bug' },
    { id: 'billing', label: 'Billing', icon: 'credit-card' },
    { id: 'feature', label: 'Feature Request', icon: 'lightbulb' },
  ];

  const contactMethods = [
    {
      id: 'email',
      title: 'Email Support',
      subtitle: '<EMAIL>',
      description: 'Get help via email within 24 hours',
      icon: 'email',
      color: '#4285F4',
      availability: '24/7',
      responseTime: '< 24 hours',
      action: () => Linking.openURL('mailto:<EMAIL>?subject=Bakery App Support Request'),
    },
    {
      id: 'phone',
      title: 'Phone Support',
      subtitle: '+1 (800) BAKERY-1',
      description: 'Call us Mon-Fri, 9 AM - 6 PM EST',
      icon: 'phone',
      color: '#34A853',
      availability: 'Mon-Fri 9AM-6PM EST',
      responseTime: 'Immediate',
      action: () => Linking.openURL('tel:+18002253791'),
    },
    {
      id: 'whatsapp',
      title: 'WhatsApp Support',
      subtitle: '+1 (800) BAKERY-2',
      description: 'Message us on WhatsApp for quick help',
      icon: 'whatsapp',
      color: '#25D366',
      availability: '24/7',
      responseTime: '< 1 hour',
      action: () => Linking.openURL('https://wa.me/18002253792?text=Hello, I need help with the Bakery Management App'),
    },
    {
      id: 'chat',
      title: 'Live Chat',
      subtitle: 'Available now',
      description: 'Chat with our support team instantly',
      icon: 'chat',
      color: '#FF6B35',
      availability: 'Mon-Fri 9AM-6PM EST',
      responseTime: '< 5 minutes',
      action: () => Alert.alert('Live Chat', 'Live chat feature coming soon! For immediate help, please use email or phone support.'),
    },
    {
      id: 'community',
      title: 'Community Forum',
      subtitle: 'community.bakeryapp.com',
      description: 'Connect with other users and get help',
      icon: 'forum',
      color: '#9C27B0',
      availability: '24/7',
      responseTime: 'Community driven',
      action: () => Linking.openURL('https://community.bakeryapp.com'),
    },
    {
      id: 'video',
      title: 'Video Call Support',
      subtitle: 'Schedule a call',
      description: 'Book a video call for personalized help',
      icon: 'video',
      color: '#FF5722',
      availability: 'By appointment',
      responseTime: 'Scheduled',
      action: () => Alert.alert('Video Support', 'Video call support available for premium users. Contact us to schedule a session.'),
    },
  ];

  const handleSubmitTicket = () => {
    if (!supportForm.subject.trim() || !supportForm.message.trim()) {
      Alert.alert('Error', 'Please fill in both subject and message fields.');
      return;
    }

    // Simulate ticket submission
    Alert.alert(
      'Support Ticket Submitted',
      'Your support ticket has been submitted successfully. We\'ll get back to you within 24 hours.',
      [
        {
          text: 'OK',
          onPress: () => {
            setSupportForm({
              subject: '',
              message: '',
              priority: 'medium',
              category: 'general',
            });
          }
        }
      ]
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Contact Support"
        subtitle="Get help from our support team"
        showSearch={false}
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Quick Contact Methods */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Quick Contact
          </Text>

          {contactMethods.map((method) => (
            <List.Item
              key={method.id}
              title={method.title}
              description={method.description}
              left={props => (
                <View style={[styles.iconContainer, { backgroundColor: theme.colors.primary + '15' }]}>
                  <Icon name={method.icon} size={24} color={theme.colors.primary} />
                </View>
              )}
              right={props => (
                <View style={styles.contactInfo}>
                  <Text variant="bodySmall" style={{ color: theme.colors.primary, fontWeight: '600' }}>
                    {method.subtitle}
                  </Text>
                </View>
              )}
              onPress={method.action}
              style={styles.contactMethod}
            />
          ))}
        </Surface>

        {/* Support Ticket Form */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Submit Support Ticket
          </Text>

          {/* Category Selection */}
          <Text variant="bodyMedium" style={[styles.fieldLabel, { color: theme.colors.onSurface }]}>
            Category
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.chipsContainer}>
            {categories.map((category) => (
              <Chip
                key={category.id}
                selected={supportForm.category === category.id}
                onPress={() => setSupportForm(prev => ({ ...prev, category: category.id }))}
                icon={category.icon}
                style={[
                  styles.chip,
                  supportForm.category === category.id && { backgroundColor: theme.colors.primary }
                ]}
                textStyle={{
                  color: supportForm.category === category.id ? theme.colors.onPrimary : theme.colors.onSurface
                }}
              >
                {category.label}
              </Chip>
            ))}
          </ScrollView>

          {/* Priority Selection */}
          <Text variant="bodyMedium" style={[styles.fieldLabel, { color: theme.colors.onSurface }]}>
            Priority
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.chipsContainer}>
            {priorities.map((priority) => (
              <Chip
                key={priority.id}
                selected={supportForm.priority === priority.id}
                onPress={() => setSupportForm(prev => ({ ...prev, priority: priority.id }))}
                style={[
                  styles.chip,
                  supportForm.priority === priority.id && { backgroundColor: priority.color }
                ]}
                textStyle={{
                  color: supportForm.priority === priority.id ? '#FFFFFF' : theme.colors.onSurface
                }}
              >
                {priority.label}
              </Chip>
            ))}
          </ScrollView>

          {/* Subject Input */}
          <TextInput
            mode="outlined"
            label="Subject *"
            value={supportForm.subject}
            onChangeText={(text) => setSupportForm(prev => ({ ...prev, subject: text }))}
            style={styles.input}
            placeholder="Brief description of your issue"
          />

          {/* Message Input */}
          <TextInput
            mode="outlined"
            label="Message *"
            value={supportForm.message}
            onChangeText={(text) => setSupportForm(prev => ({ ...prev, message: text }))}
            multiline
            numberOfLines={6}
            style={styles.textArea}
            placeholder="Please provide detailed information about your issue..."
          />

          <Button
            mode="contained"
            onPress={handleSubmitTicket}
            style={styles.submitButton}
            icon="send"
          >
            Submit Ticket
          </Button>
        </Surface>

        {/* Support Hours */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.primaryContainer }]} elevation={1}>
          <View style={styles.supportHours}>
            <Icon name="clock-outline" size={32} color={theme.colors.onPrimaryContainer} />
            <View style={styles.hoursText}>
              <Text variant="titleMedium" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '700' }}>
                Support Hours
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onPrimaryContainer, marginTop: 4 }}>
                Monday - Friday: 9:00 AM - 6:00 PM EST
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onPrimaryContainer }}>
                Saturday - Sunday: 10:00 AM - 4:00 PM EST
              </Text>
            </View>
          </View>
        </Surface>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  section: {
    borderRadius: SHAPE.corner.xl,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  sectionTitle: {
    fontWeight: '700',
    marginBottom: SPACING.md,
  },
  contactMethod: {
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: SHAPE.corner.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.sm,
  },
  contactInfo: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  fieldLabel: {
    fontWeight: '600',
    marginBottom: SPACING.xs,
    marginTop: SPACING.sm,
  },
  chipsContainer: {
    marginBottom: SPACING.md,
  },
  chip: {
    marginRight: SPACING.sm,
    marginBottom: SPACING.xs,
  },
  input: {
    marginBottom: SPACING.md,
  },
  textArea: {
    marginBottom: SPACING.lg,
  },
  submitButton: {
    marginTop: SPACING.sm,
  },
  supportHours: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  hoursText: {
    flex: 1,
    marginLeft: SPACING.md,
  },
});

export default ContactSupportScreen;
