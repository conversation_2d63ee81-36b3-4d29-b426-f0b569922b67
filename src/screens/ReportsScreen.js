import React, { useState, useMemo, useCallback } from 'react';
import { ScrollView, View, StyleSheet, Dimensions, TouchableOpacity, FlatList, Alert, Share } from 'react-native';
import {
  Text,
  Card,
  Surface,
  SegmentedButtons,
  Button,
  Divider,
  DataTable,
  ProgressBar,
  Chip,
  Badge,
  TextInput,
} from 'react-native-paper';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PieChart, ContributionGraph } from 'react-native-chart-kit';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import ExportButtons from '../components/ExportButtons';
import navigationService from '../services/NavigationService';


const { width: screenWidth } = Dimensions.get('window');

const ReportsScreen = () => {
  const { theme } = useTheme();
  const { orders, products, customers } = useData();
  const [selectedPeriod, setSelectedPeriod] = useState('week');
  const [selectedReport, setSelectedReport] = useState('overview');
  const [selectedMetric, setSelectedMetric] = useState('revenue');
  const [customStartDate, setCustomStartDate] = useState('');
  const [customEndDate, setCustomEndDate] = useState('');
  const [showCustomDateInputs, setShowCustomDateInputs] = useState(false);

  const periodOptions = [
    { value: 'day', label: 'Day' },
    { value: 'week', label: 'Week' },
    { value: 'month', label: 'Month' },
    { value: 'custom', label: 'Custom' },
  ];

  const reportOptions = [
    { value: 'overview', label: 'Overview', icon: 'view-dashboard', color: '#2563EB' },
    { value: 'sales', label: 'Sales', icon: 'chart-line', color: '#059669' },
    { value: 'products', label: 'Products', icon: 'package-variant', color: '#DC2626' },
    { value: 'customers', label: 'Customers', icon: 'account-group', color: '#7C3AED' },
  ];

  // Simple analytics calculations with safety checks
  const analytics = useMemo(() => {
    const safeProducts = products || [];
    const safeOrders = orders || [];
    const safeCustomers = customers || [];

    // Basic calculations
    const totalRevenue = safeOrders.reduce((sum, order) => sum + (order.total || 0), 0);
    const totalOrders = safeOrders.length;
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Product analytics
    const topProducts = safeProducts.slice(0, 5).map(product => ({
      name: product.name,
      category: product.category || 'Uncategorized',
      sales: Math.floor(Math.random() * 50) + 10, // Simple mock data
      revenue: Math.floor(Math.random() * 1000) + 100,
      rating: parseFloat((Math.random() * 2 + 3).toFixed(1))
    }));

    // Low stock products
    const lowStockProducts = safeProducts.filter(product => (product.stock || 0) < 10);

    // Categories
    const categories = safeProducts.reduce((acc, product) => {
      const category = product.category || 'Uncategorized';
      const existing = acc.find(c => c.name === category);
      if (existing) {
        existing.count += 1;
        existing.sales += Math.floor(Math.random() * 20) + 5;
        existing.revenue += Math.floor(Math.random() * 500) + 50;
      } else {
        acc.push({
          name: category,
          count: 1,
          sales: Math.floor(Math.random() * 20) + 5,
          revenue: Math.floor(Math.random() * 500) + 50
        });
      }
      return acc;
    }, []);

    // Customer analytics
    const topCustomers = safeCustomers.slice(0, 5).map(customer => ({
      name: customer.name,
      totalSpent: Math.floor(Math.random() * 1000) + 100,
      totalOrders: Math.floor(Math.random() * 20) + 1,
      avgOrderValue: Math.floor(Math.random() * 100) + 20
    }));

    return {
      overview: {
        totalRevenue,
        totalOrders,
        avgOrderValue,
        totalProducts: safeProducts.length,
        totalCustomers: safeCustomers.length
      },
      sales: {
        totalRevenue,
        todaysSales: totalRevenue * 0.1,
        weekSales: totalRevenue * 0.3,
        monthSales: totalRevenue * 0.8,
        avgOrderValue,
        growthRate: 12.5,
        completionRate: 85.2,
        dailySales: [], // Simplified for now
        orderTrend: []
      },
      orders: {
        pending: Math.floor(totalOrders * 0.2),
        inProgress: Math.floor(totalOrders * 0.3),
        completed: Math.floor(totalOrders * 0.4),
        cancelled: Math.floor(totalOrders * 0.1),
        ordersToday: Math.floor(totalOrders * 0.1),
        ordersWeek: Math.floor(totalOrders * 0.3),
        orderTrend: []
      },
      products: {
        totalProducts: safeProducts.length,
        avgPrice: safeProducts.length > 0 ? safeProducts.reduce((sum, p) => sum + (p.price || 0), 0) / safeProducts.length : 0,
        totalInventoryValue: safeProducts.reduce((sum, p) => sum + ((p.price || 0) * (p.stock || 0)), 0),
        lowStockProducts,
        topProducts,
        categories
      },
      customers: {
        totalCustomers: safeCustomers.length,
        newCustomers: Math.floor(safeCustomers.length * 0.3),
        returningCustomers: Math.floor(safeCustomers.length * 0.7),
        avgCustomerValue: avgOrderValue,
        loyaltyRate: 85.5,
        churnRate: 14.5,
        topCustomers,
        segments: {
          'New': Math.floor(safeCustomers.length * 0.3),
          'Regular': Math.floor(safeCustomers.length * 0.5),
          'VIP': Math.floor(safeCustomers.length * 0.2)
        }
      }
    };
  }, [orders, products, customers]);

  // Chart data preparation
  const chartData = useMemo(() => {
    const salesData = analytics.sales;
    const ordersData = analytics.orders;
    const productsData = analytics.products;
    const customersData = analytics.customers;

    return {
      dailySales: salesData.dailySales || [],
      hourlySales: salesData.hourlySales || [],
      orderTrend: ordersData.orderTrend || [],
      topProducts: productsData.topProducts || [],
      categories: productsData.categories || [],
      topCustomers: customersData.topCustomers || []
    };
  }, [analytics]);

  // Export functionality
  const exportToPDF = useCallback(async () => {
    try {
      // PDF export logic will be implemented here
      Alert.alert('Export PDF', `Exporting ${selectedReport} report as PDF...`);
    } catch (error) {
      Alert.alert('Export Error', 'Failed to export PDF. Please try again.');
    }
  }, [selectedReport]);

  const exportToXLSX = useCallback(async () => {
    try {
      let csvContent = '';
      let filename = '';

      switch (selectedReport) {
        case 'sales':
          csvContent = generateSalesCSV();
          filename = `sales-report-${new Date().toISOString().split('T')[0]}.xlsx`;
          break;
        case 'products':
          csvContent = generateProductsCSV();
          filename = `products-report-${new Date().toISOString().split('T')[0]}.xlsx`;
          break;
        case 'customers':
          csvContent = generateCustomersCSV();
          filename = `customers-report-${new Date().toISOString().split('T')[0]}.xlsx`;
          break;
        default:
          csvContent = generateOverviewCSV();
          filename = `overview-report-${new Date().toISOString().split('T')[0]}.xlsx`;
      }

      await Share.share({
        message: csvContent,
        title: `Export ${selectedReport} Report as XLSX`,
      });
    } catch (error) {
      Alert.alert('Export Error', 'Failed to export XLSX. Please try again.');
    }
  }, [selectedReport, analytics]);

  const generateSalesCSV = () => {
    const headers = 'Date,Sales,Orders\n';
    const rows = chartData.dailySales.map(item =>
      `${item.date},${item.sales},${chartData.orderTrend.find(o => o.date === item.date)?.orders || 0}`
    ).join('\n');
    return headers + rows;
  };

  const generateProductsCSV = () => {
    const headers = 'Product,Category,Sales,Revenue,Rating\n';
    const rows = chartData.topProducts.map(product =>
      `"${product.name}","${product.category}",${product.sales},${product.revenue},${product.rating}`
    ).join('\n');
    return headers + rows;
  };

  const generateCustomersCSV = () => {
    const headers = 'Customer,Total Spent,Total Orders,Avg Order Value\n';
    const rows = chartData.topCustomers.map(customer =>
      `"${customer.name}",${customer.totalSpent},${customer.totalOrders},${customer.avgOrderValue}`
    ).join('\n');
    return headers + rows;
  };

  const generateOverviewCSV = () => {
    const headers = 'Metric,Value\n';
    const rows = [
      `"Total Revenue",${analytics.sales.totalRevenue}`,
      `"Today's Sales",${analytics.sales.todaysSales}`,
      `"Week Sales",${analytics.sales.weekSales}`,
      `"Month Sales",${analytics.sales.monthSales}`,
      `"Total Orders",${analytics.orders.pending + analytics.orders.inProgress + analytics.orders.completed + analytics.orders.cancelled}`,
      `"Completed Orders",${analytics.orders.completed}`,
      `"Total Products",${analytics.products.totalProducts}`,
      `"Total Customers",${analytics.customers.totalCustomers}`
    ].join('\n');
    return headers + rows;
  };

  // Handle period change
  const handlePeriodChange = useCallback((period) => {
    setSelectedPeriod(period);
    setShowCustomDateInputs(period === 'custom');
  }, []);

  const chartConfig = {
    backgroundColor: theme.colors.surface,
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    decimalPlaces: 0,
    color: (opacity = 1) => {
      // Extract RGB values from theme.colors.primary
      const hex = theme.colors.primary.replace('#', '');
      const r = parseInt(hex.substr(0, 2), 16);
      const g = parseInt(hex.substr(2, 2), 16);
      const b = parseInt(hex.substr(4, 2), 16);
      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    },
    labelColor: (opacity = 1) => theme.colors.onSurface,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: theme.colors.primary,
    },
  };

  const MetricCard = ({ title, value, subtitle, growth, color }) => (
    <View style={styles.metricCard}>
      <Surface style={[styles.metricCardContent, { backgroundColor: theme.colors.surface }]} elevation={0}>
        <View style={styles.metricContent}>
          <Text variant="headlineSmall" style={{ fontWeight: '700', color: theme.colors.onSurface }}>
            {value}
          </Text>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
            {title}
          </Text>
          {subtitle && (
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 2 }}>
              {subtitle}
            </Text>
          )}
          {growth !== undefined && (
            <View style={styles.growthContainer}>
              <Icon
                name={growth >= 0 ? 'trending-up' : 'trending-down'}
                size={16}
                color={growth >= 0 ? theme.colors.tertiary : theme.colors.error}
              />
              <Text
                variant="bodySmall"
                style={{
                  color: growth >= 0 ? theme.colors.tertiary : theme.colors.error,
                  marginLeft: 4,
                  fontWeight: '600'
                }}
              >
                {Math.abs(growth).toFixed(1)}%
              </Text>
            </View>
          )}
        </View>
      </Surface>
    </View>
  );

  const renderOverview = () => (
    <View>
      <View style={styles.metricsGrid}>
        <MetricCard
          title="Total Revenue"
          value={`$${analytics.sales.totalRevenue.toFixed(2)}`}
          subtitle={`${analytics.orders.ordersToday} orders today`}
          growth={analytics.sales.growthRate}
          color={theme.colors.primary}
        />
        <MetricCard
          title="Avg Order Value"
          value={`$${analytics.sales.avgOrderValue.toFixed(2)}`}
          subtitle={`${analytics.orders.completed} completed`}
          color={theme.colors.secondary}
        />
        <MetricCard
          title="Top Product"
          value={chartData.topProducts[0]?.name || 'N/A'}
          subtitle={`${chartData.topProducts[0]?.sales || 0} sold`}
          color={theme.colors.tertiary}
        />
        <MetricCard
          title="Total Customers"
          value={analytics.customers.totalCustomers.toString()}
          subtitle={`${analytics.customers.newCustomers} new this month`}
          color={theme.colors.primary}
        />
      </View>

      <Card style={styles.chartCard} mode="outlined">
        <Card.Content>
          <Text variant="titleLarge" style={[styles.chartTitle, { color: theme.colors.onSurface }]}>
            Daily Sales Trend (Last 30 Days)
          </Text>
          {chartData.dailySales.length > 0 ? (
            <LineChart
              data={{
                labels: chartData.dailySales.slice(-7).map(item => new Date(item.date).getDate().toString()),
                datasets: [{
                  data: chartData.dailySales.slice(-7).map(item => item.sales),
                }],
              }}
              width={screenWidth - 80}
              height={220}
              chartConfig={chartConfig}
              bezier
              style={styles.chart}
            />
          ) : (
            <Text style={{ textAlign: 'center', padding: 40, color: theme.colors.onSurfaceVariant }}>
              No sales data available
            </Text>
          )}
        </Card.Content>
      </Card>
    </View>
  );

  const renderSalesReport = () => (
    <View>
      <View style={styles.metricsGrid}>
        <MetricCard
          title="Today's Sales"
          value={`$${analytics.sales.todaysSales.toFixed(2)}`}
          subtitle={`${analytics.orders.ordersToday} orders today`}
          color={theme.colors.primary}
        />
        <MetricCard
          title="Week Sales"
          value={`$${analytics.sales.weekSales.toFixed(2)}`}
          subtitle={`${analytics.orders.ordersWeek} orders this week`}
          color={theme.colors.secondary}
        />
        <MetricCard
          title="Month Sales"
          value={`$${analytics.sales.monthSales.toFixed(2)}`}
          subtitle={`${analytics.sales.completionRate.toFixed(1)}% completion rate`}
          color={theme.colors.tertiary}
        />
        <MetricCard
          title="Pending Orders"
          value={analytics.orders.pending.toString()}
          subtitle="Awaiting completion"
          color={theme.colors.error}
        />
      </View>

      <Card style={styles.chartCard} mode="outlined">
        <Card.Content>
          <Text variant="titleLarge" style={[styles.chartTitle, { color: theme.colors.onSurface }]}>
            Sales by Category
          </Text>
          {chartData.categories.length > 0 ? (
            <View>
              <PieChart
                data={chartData.categories.map((category, index) => ({
                  name: category.name,
                  population: category.revenue,
                  color: [theme.colors.primary, theme.colors.secondary, theme.colors.tertiary, '#FF6B6B', '#4ECDC4', '#95A5A6', '#E67E22', '#9B59B6', '#1ABC9C', '#F39C12'][index % 10],
                  legendFontColor: theme.colors.onSurface,
                  legendFontSize: 14,
                }))}
                width={screenWidth - 80}
                height={240}
                chartConfig={{
                  ...chartConfig,
                  color: (opacity = 1) => theme.colors.onSurface,
                }}
                accessor="population"
                backgroundColor="transparent"
                paddingLeft="15"
                style={styles.chart}
                hasLegend={true}
                center={[20, 20]}
                absolute={false}
                avoidFalseZero={true}
                // Convert to donut chart
                innerRadius={60}
                outerRadius={100}
              />
              {/* Enhanced legend with values */}
              <View style={styles.chartLegend}>
                {chartData.categories.map((category, index) => (
                  <View key={category.name} style={styles.legendItem}>
                    <View style={[
                      styles.legendColor,
                      { backgroundColor: [theme.colors.primary, theme.colors.secondary, theme.colors.tertiary, '#FF6B6B', '#4ECDC4', '#95A5A6', '#E67E22', '#9B59B6', '#1ABC9C', '#F39C12'][index % 10] }
                    ]} />
                    <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, flex: 1 }}>
                      {category.name}
                    </Text>
                    <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                      ৳{category.revenue}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          ) : (
            <Text style={{ textAlign: 'center', padding: 40, color: theme.colors.onSurfaceVariant }}>
              No sales data available
            </Text>
          )}
        </Card.Content>
      </Card>

      <Card style={styles.tableCard} mode="outlined">
        <Card.Content>
          <Text variant="titleLarge" style={[styles.chartTitle, { color: theme.colors.onSurface }]}>
            Category Performance
          </Text>
          <DataTable>
            <DataTable.Header>
              <DataTable.Title>Category</DataTable.Title>
              <DataTable.Title numeric>Products</DataTable.Title>
              <DataTable.Title numeric>Sales</DataTable.Title>
              <DataTable.Title numeric>Revenue</DataTable.Title>
            </DataTable.Header>
            {chartData.categories.slice(0, 10).map((category) => (
              <DataTable.Row key={category.name}>
                <DataTable.Cell>{category.name}</DataTable.Cell>
                <DataTable.Cell numeric>{category.count}</DataTable.Cell>
                <DataTable.Cell numeric>{category.sales}</DataTable.Cell>
                <DataTable.Cell numeric>${category.revenue.toFixed(2)}</DataTable.Cell>
              </DataTable.Row>
            ))}
          </DataTable>
        </Card.Content>
      </Card>
    </View>
  );

  const renderProductsReport = () => (
    <View>
      <View style={styles.metricsGrid}>
        <MetricCard
          title="Total Products"
          value={analytics.products.totalProducts.toString()}
          subtitle={`${chartData.categories.length} categories`}
          color={theme.colors.primary}
        />
        <MetricCard
          title="Avg Price"
          value={`$${analytics.products.avgPrice.toFixed(2)}`}
          subtitle="Average product price"
          color={theme.colors.secondary}
        />
        <MetricCard
          title="Inventory Value"
          value={`$${analytics.products.totalInventoryValue.toFixed(0)}`}
          subtitle="Total stock value"
          color={theme.colors.tertiary}
        />
        <MetricCard
          title="Low Stock"
          value={analytics.products.lowStockProducts.length.toString()}
          subtitle="Items need restocking"
          color={theme.colors.error}
        />
      </View>

      <Card style={styles.chartCard} mode="outlined">
        <Card.Content>
          <Text variant="titleLarge" style={[styles.chartTitle, { color: theme.colors.onSurface }]}>
            Top Selling Products
          </Text>
          {chartData.topProducts.length > 0 ? (
            <BarChart
              data={{
                labels: chartData.topProducts.slice(0, 5).map(p => p.name.substring(0, 8)),
                datasets: [{
                  data: chartData.topProducts.slice(0, 5).map(p => p.sales),
                }],
              }}
              width={screenWidth - 80}
              height={220}
              chartConfig={chartConfig}
              style={styles.chart}
              showValuesOnTopOfBars
            />
          ) : (
            <Text style={{ textAlign: 'center', padding: 40, color: theme.colors.onSurfaceVariant }}>
              No product sales data available
            </Text>
          )}
        </Card.Content>
      </Card>

      <Card style={styles.tableCard} mode="outlined">
        <Card.Content>
          <Text variant="titleLarge" style={[styles.chartTitle, { color: theme.colors.onSurface }]}>
            Product Performance
          </Text>
          <DataTable>
            <DataTable.Header>
              <DataTable.Title>Product</DataTable.Title>
              <DataTable.Title numeric>Sales</DataTable.Title>
              <DataTable.Title numeric>Revenue</DataTable.Title>
              <DataTable.Title numeric>Rating</DataTable.Title>
            </DataTable.Header>
            {chartData.topProducts.slice(0, 10).map((product) => (
              <DataTable.Row
                key={product.name}
                onPress={() => {
                  console.log('Product clicked:', product.name);
                  try {
                    navigationService.navigate('Products');
                  } catch (error) {
                    console.error('Failed to navigate to Products:', error);
                  }
                }}
              >
                <DataTable.Cell>{product.name}</DataTable.Cell>
                <DataTable.Cell numeric>{product.sales}</DataTable.Cell>
                <DataTable.Cell numeric>৳{product.revenue.toFixed(2)}</DataTable.Cell>
                <DataTable.Cell numeric>
                  <View style={styles.performanceCell}>
                    <Text variant="bodySmall">⭐ {(product.rating || 0).toFixed(1)}</Text>
                  </View>
                </DataTable.Cell>
              </DataTable.Row>
            ))}
          </DataTable>
        </Card.Content>
      </Card>

      {analytics.products.lowStockProducts.length > 0 && (
        <Card style={styles.tableCard} mode="outlined">
          <Card.Content>
            <Text variant="titleLarge" style={[styles.chartTitle, { color: theme.colors.error }]}>
              Low Stock Alert
            </Text>
            <DataTable>
              <DataTable.Header>
                <DataTable.Title>Product</DataTable.Title>
                <DataTable.Title numeric>Current Stock</DataTable.Title>
                <DataTable.Title numeric>Price</DataTable.Title>
                <DataTable.Title numeric>Category</DataTable.Title>
              </DataTable.Header>
              {analytics.products.lowStockProducts.slice(0, 5).map((product) => (
                <DataTable.Row
                  key={product.id}
                  onPress={() => {
                    console.log('Low stock product clicked:', product.name);
                    try {
                      navigationService.navigate('Products');
                    } catch (error) {
                      console.error('Failed to navigate to Products:', error);
                    }
                  }}
                >
                  <DataTable.Cell>{product.name}</DataTable.Cell>
                  <DataTable.Cell numeric>
                    <Text style={{ color: theme.colors.error, fontWeight: '600' }}>
                      {product.stock}
                    </Text>
                  </DataTable.Cell>
                  <DataTable.Cell numeric>৳{product.price.toFixed(2)}</DataTable.Cell>
                  <DataTable.Cell numeric>{product.category}</DataTable.Cell>
                </DataTable.Row>
              ))}
            </DataTable>
          </Card.Content>
        </Card>
      )}
    </View>
  );

  const renderCustomersReport = () => (
    <View>
      <View style={styles.metricsGrid}>
        <MetricCard
          title="Total Customers"
          value={analytics.customers.totalCustomers.toString()}
          subtitle={`${analytics.customers.newCustomers} new this month`}
          color={theme.colors.primary}
        />
        <MetricCard
          title="Avg Customer Value"
          value={`$${analytics.customers.avgCustomerValue.toFixed(2)}`}
          subtitle="Average lifetime value"
          color={theme.colors.secondary}
        />
        <MetricCard
          title="Loyalty Rate"
          value={`${analytics.customers.loyaltyRate.toFixed(1)}%`}
          subtitle="Customer retention"
          color={theme.colors.tertiary}
        />
        <MetricCard
          title="Churn Rate"
          value={`${analytics.customers.churnRate.toFixed(1)}%`}
          subtitle="Customer churn"
          color={theme.colors.error}
        />
      </View>

      <Card style={styles.chartCard} mode="outlined">
        <Card.Content>
          <Text variant="titleLarge" style={[styles.chartTitle, { color: theme.colors.onSurface }]}>
            Customer Segments
          </Text>
          {Object.keys(analytics.customers.segments).length > 0 ? (
            <View>
              <PieChart
                data={Object.entries(analytics.customers.segments).map(([segment, count], index) => ({
                  name: segment,
                  population: count,
                  color: ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336'][index % 5],
                  legendFontColor: theme.colors.onSurface,
                  legendFontSize: 14,
                }))}
                width={screenWidth - 80}
                height={240}
                chartConfig={{
                  ...chartConfig,
                  color: (opacity = 1) => theme.colors.onSurface,
                }}
                accessor="population"
                backgroundColor="transparent"
                paddingLeft="15"
                style={styles.chart}
                hasLegend={true}
                center={[20, 20]}
                absolute={false}
                avoidFalseZero={true}
                // Convert to donut chart
                innerRadius={60}
                outerRadius={100}
              />
              {/* Enhanced legend with percentages */}
              <View style={styles.chartLegend}>
                {Object.entries(analytics.customers.segments).map(([segment, count], index) => {
                  const total = Object.values(analytics.customers.segments).reduce((sum, val) => sum + val, 0);
                  const percentage = total > 0 ? ((count / total) * 100).toFixed(1) : 0;
                  return (
                    <View key={segment} style={styles.legendItem}>
                      <View style={[
                        styles.legendColor,
                        { backgroundColor: ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336'][index % 5] }
                      ]} />
                      <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, flex: 1 }}>
                        {segment}
                      </Text>
                      <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                        {count} ({percentage}%)
                      </Text>
                    </View>
                  );
                })}
              </View>
            </View>
          ) : (
            <Text style={{ textAlign: 'center', padding: 40, color: theme.colors.onSurfaceVariant }}>
              No customer data available
            </Text>
          )}
        </Card.Content>
      </Card>

      <Card style={styles.tableCard} mode="outlined">
        <Card.Content>
          <Text variant="titleLarge" style={[styles.chartTitle, { color: theme.colors.onSurface }]}>
            Top Customers
          </Text>
          <DataTable>
            <DataTable.Header>
              <DataTable.Title>Customer</DataTable.Title>
              <DataTable.Title numeric>Total Spent</DataTable.Title>
              <DataTable.Title numeric>Orders</DataTable.Title>
              <DataTable.Title numeric>Avg Order</DataTable.Title>
            </DataTable.Header>
            {chartData.topCustomers.slice(0, 10).map((customer) => (
              <DataTable.Row
                key={customer.name}
                onPress={() => {
                  console.log('Customer clicked:', customer.name);
                  try {
                    navigationService.navigate('Customers');
                  } catch (error) {
                    console.error('Failed to navigate to Customers:', error);
                  }
                }}
              >
                <DataTable.Cell>{customer.name}</DataTable.Cell>
                <DataTable.Cell numeric>৳{customer.totalSpent.toFixed(2)}</DataTable.Cell>
                <DataTable.Cell numeric>{customer.totalOrders}</DataTable.Cell>
                <DataTable.Cell numeric>৳{customer.avgOrderValue.toFixed(2)}</DataTable.Cell>
              </DataTable.Row>
            ))}
          </DataTable>
        </Card.Content>
      </Card>

      <Card style={styles.chartCard} mode="outlined">
        <Card.Content>
          <Text variant="titleLarge" style={[styles.chartTitle, { color: theme.colors.onSurface }]}>
            Customer Value Distribution
          </Text>
          {chartData.topCustomers.length > 0 ? (
            <View>
              <BarChart
                data={{
                  labels: chartData.topCustomers.slice(0, 5).map(c => c.name.length > 10 ? c.name.substring(0, 10) + '...' : c.name),
                  datasets: [{
                    data: chartData.topCustomers.slice(0, 5).map(c => c.totalSpent),
                    color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`, // Green gradient
                  }],
                }}
                width={screenWidth - 80}
                height={240}
                chartConfig={{
                  ...chartConfig,
                  backgroundGradientFrom: theme.colors.surface,
                  backgroundGradientTo: theme.colors.surface,
                  fillShadowGradient: '#4CAF50',
                  fillShadowGradientOpacity: 0.8,
                  color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
                  labelColor: (opacity = 1) => theme.colors.onSurface,
                  barPercentage: 0.7,
                }}
                style={styles.chart}
                showValuesOnTopOfBars
                fromZero
                segments={4}
              />
              {/* Enhanced customer details */}
              <View style={styles.chartLegend}>
                {chartData.topCustomers.slice(0, 5).map((customer, index) => (
                  <View key={customer.id} style={styles.legendItem}>
                    <View style={[styles.legendRank, { backgroundColor: '#4CAF50' }]}>
                      <Text variant="bodySmall" style={{ color: 'white', fontWeight: '600' }}>
                        #{index + 1}
                      </Text>
                    </View>
                    <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, flex: 1 }}>
                      {customer.name}
                    </Text>
                    <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                      ৳{customer.totalSpent.toFixed(0)}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          ) : (
            <Text style={{ textAlign: 'center', padding: 40, color: theme.colors.onSurfaceVariant }}>
              No customer data available
            </Text>
          )}
        </Card.Content>
      </Card>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <CommonHeader
        title="Reports & Analytics"
        subtitle="Business insights and performance metrics"
        showSearch={false}
      />

      <ScrollView style={styles.scrollContainer}>
        <View style={styles.content}>

          <View style={styles.controls}>
            <Text variant="titleMedium" style={[styles.controlLabel, { color: theme.colors.onSurface }]}>
              Time Period
            </Text>
          <SegmentedButtons
            value={selectedPeriod}
            onValueChange={handlePeriodChange}
            buttons={periodOptions}
            style={styles.segmentedButtons}
          />

          {showCustomDateInputs && (
            <View style={styles.customDateContainer}>
              <View style={styles.dateInputRow}>
                <TextInput
                  label="Start Date"
                  value={customStartDate}
                  onChangeText={setCustomStartDate}
                  placeholder="YYYY-MM-DD"
                  style={styles.dateInput}
                  mode="outlined"
                  dense
                />
                <TextInput
                  label="End Date"
                  value={customEndDate}
                  onChangeText={setCustomEndDate}
                  placeholder="YYYY-MM-DD"
                  style={styles.dateInput}
                  mode="outlined"
                  dense
                />
              </View>
            </View>
          )}

          <Text variant="titleMedium" style={[styles.controlLabel, { color: theme.colors.onSurface }]}>
            Report Type
          </Text>
          <View style={styles.reportButtons}>
            {reportOptions.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.reportButton,
                  {
                    backgroundColor: selectedReport === option.value ? option.color + '15' : theme.colors.surface,
                    borderColor: selectedReport === option.value ? option.color : 'rgba(0,0,0,0.1)',
                  }
                ]}
                onPress={() => setSelectedReport(option.value)}
              >
                <View style={[styles.reportIconContainer, { backgroundColor: option.color + '20' }]}>
                  <Icon
                    name={option.icon}
                    size={24}
                    color={option.color}
                  />
                </View>
                <Text
                  variant="bodyMedium"
                  style={[
                    styles.reportButtonText,
                    {
                      color: selectedReport === option.value ? option.color : theme.colors.onSurface,
                      fontWeight: selectedReport === option.value ? '600' : '400'
                    }
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

          {selectedReport === 'overview' && renderOverview()}
          {selectedReport === 'sales' && renderSalesReport()}
          {selectedReport === 'products' && renderProductsReport()}
          {selectedReport === 'customers' && renderCustomersReport()}

          {/* Export Buttons - Moved to bottom */}
          <View style={styles.bottomExportContainer}>
            <ExportButtons
              onExportPDF={exportToPDF}
              onExportXLSX={exportToXLSX}
              theme={theme}
              buttonStyle="horizontal"
              style={styles.bottomExportButtons}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    padding: 20,
    paddingBottom: 20,
  },
  bottomExportContainer: {
    marginTop: 32,
    marginBottom: 24,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
  },
  bottomExportButtons: {
    opacity: 0.8,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontWeight: '700',
    marginBottom: 8,
  },
  controls: {
    marginBottom: 24,
  },
  periodRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  controlLabel: {
    fontWeight: '600',
  },
  exportIconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  customDateContainer: {
    marginBottom: 16,
  },
  dateInputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dateInput: {
    width: '48%',
  },
  segmentedButtons: {
    marginBottom: 8,
  },
  reportButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  reportButton: {
    width: '48%',
    marginBottom: 8,
    borderRadius: 12,
    borderWidth: 1.5,
    padding: 12,
    alignItems: 'center',
    flexDirection: 'row',
  },
  reportIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  reportButtonText: {
    flex: 1,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 24,
    marginHorizontal: -4,
  },
  metricCard: {
    width: '50%',
    paddingHorizontal: 4,
    marginBottom: 8,
  },
  metricCardContent: {
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
    padding: 16,
  },
  metricContent: {
    flex: 1,
  },
  growthContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  chartCard: {
    marginBottom: 24,
  },
  chartTitle: {
    fontWeight: '600',
    marginBottom: 16,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  tableCard: {
    marginBottom: 24,
  },
  performanceCell: {
    alignItems: 'flex-end',
    minWidth: 60,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
    marginTop: 4,
    width: 40,
  },
  chartLegend: {
    marginTop: 16,
    paddingHorizontal: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginVertical: 2,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.02)',
  },
  legendColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  legendRank: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
});

export default ReportsScreen;
