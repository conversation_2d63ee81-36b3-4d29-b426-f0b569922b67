/**
 * AddEmployeeScreen - Add/Edit Employee for Tailor Shop
 * Comprehensive employee management with skills and salary
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {
  Text,
  Card,
  TextInput,
  Button,
  SegmentedButtons,
  useTheme,
  Chip,
  Switch,
  Divider,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import CommonHeader from '../components/CommonHeader';
import { useData } from '../context/DataContext';
import { SPACING, SHAPE, TYPOGRAPHY } from '../theme/designTokens';
import { BUSINESS_RULES, TAILOR_SHOP_CONFIG } from '../config/constants';

const AddEmployeeScreen = ({ navigation, route }) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const { addEmployee, updateEmployee } = useData();

  // Route params
  const { employee } = route.params || {};
  const isEditing = !!employee;

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    role: 'Junior Tailor',
    phone: '',
    email: '',
    address: '',
    salaryType: 'monthly',
    salaryAmount: '',
    commissionRate: '',
    workingDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
    startTime: '09:00',
    endTime: '18:00',
    skills: [],
    isActive: true,
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Initialize form data for editing
  useEffect(() => {
    if (isEditing && employee) {
      setFormData({
        name: employee.name || '',
        role: employee.role || 'Junior Tailor',
        phone: employee.contactInfo?.phone || '',
        email: employee.contactInfo?.email || '',
        address: employee.contactInfo?.address || '',
        salaryType: employee.salary?.type || 'monthly',
        salaryAmount: employee.salary?.amount?.toString() || '',
        commissionRate: employee.salary?.commissionRate?.toString() || '',
        workingDays: employee.schedule?.workingDays || ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
        startTime: employee.schedule?.startTime || '09:00',
        endTime: employee.schedule?.endTime || '18:00',
        skills: employee.skills || [],
        isActive: employee.isActive !== false,
      });
    }
  }, [isEditing, employee]);

  // Update form field
  const updateField = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  // Toggle skill selection
  const toggleSkill = (skill) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.includes(skill)
        ? prev.skills.filter(s => s !== skill)
        : [...prev.skills, skill]
    }));
  };

  // Toggle working day
  const toggleWorkingDay = (day) => {
    setFormData(prev => ({
      ...prev,
      workingDays: prev.workingDays.includes(day)
        ? prev.workingDays.filter(d => d !== day)
        : [...prev.workingDays, day]
    }));
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^\+?[\d\s-()]+$/.test(formData.phone)) {
      newErrors.phone = 'Invalid phone number format';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Address is required';
    }

    if (!formData.salaryAmount.trim()) {
      newErrors.salaryAmount = 'Salary amount is required';
    } else if (isNaN(parseFloat(formData.salaryAmount))) {
      newErrors.salaryAmount = 'Invalid salary amount';
    }

    if (formData.salaryType === 'commission' && !formData.commissionRate.trim()) {
      newErrors.commissionRate = 'Commission rate is required';
    }

    if (formData.skills.length === 0) {
      newErrors.skills = 'At least one skill is required';
    }

    if (formData.workingDays.length === 0) {
      newErrors.workingDays = 'At least one working day is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Save employee
  const handleSave = async () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors and try again.');
      return;
    }

    setLoading(true);
    try {
      const employeeData = {
        name: formData.name.trim(),
        role: formData.role,
        contactInfo: {
          phone: formData.phone.trim(),
          email: formData.email.trim() || undefined,
          address: formData.address.trim(),
        },
        salary: {
          type: formData.salaryType,
          amount: parseFloat(formData.salaryAmount),
          commissionRate: formData.salaryType === 'commission' ? parseFloat(formData.commissionRate) : undefined,
        },
        schedule: {
          workingDays: formData.workingDays,
          startTime: formData.startTime,
          endTime: formData.endTime,
        },
        skills: formData.skills,
        isActive: formData.isActive,
      };

      if (isEditing) {
        await updateEmployee({ ...employee, ...employeeData });
        Alert.alert('Success', 'Employee updated successfully!');
      } else {
        await addEmployee(employeeData);
        Alert.alert('Success', 'Employee added successfully!');
      }

      navigation.goBack();
    } catch (error) {
      console.error('Error saving employee:', error);
      Alert.alert('Error', 'Failed to save employee. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <CommonHeader
        title={isEditing ? 'Edit Employee' : 'Add Employee'}
        subtitle={isEditing ? 'Update employee details' : 'Add new team member'}
        showBack
        onBackPress={() => navigation.goBack()}
        showSearch={false}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => {
          console.log('Profile pressed - navigating to MyProfile');
          try {
            navigation.navigate('MyProfile');
          } catch (error) {
            console.error('Failed to navigate to MyProfile:', error);
          }
        }}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Basic Information */}
        <Card style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Basic Information
            </Text>

            <TextInput
              label="Full Name *"
              value={formData.name}
              onChangeText={(value) => updateField('name', value)}
              mode="outlined"
              style={styles.input}
              error={!!errors.name}
            />
            {errors.name && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.name}</Text>}

            <Text style={[styles.fieldLabel, { color: theme.colors.onSurface }]}>Role *</Text>
            <View style={styles.roleChips}>
              {BUSINESS_RULES.EMPLOYEE_ROLES.map(role => (
                <Chip
                  key={role}
                  selected={formData.role === role}
                  onPress={() => updateField('role', role)}
                  style={styles.roleChip}
                  mode={formData.role === role ? 'flat' : 'outlined'}
                >
                  {role}
                </Chip>
              ))}
            </View>

            <TextInput
              label="Phone Number *"
              value={formData.phone}
              onChangeText={(value) => updateField('phone', value)}
              mode="outlined"
              style={styles.input}
              keyboardType="phone-pad"
              error={!!errors.phone}
            />
            {errors.phone && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.phone}</Text>}

            <TextInput
              label="Email (Optional)"
              value={formData.email}
              onChangeText={(value) => updateField('email', value)}
              mode="outlined"
              style={styles.input}
              keyboardType="email-address"
              autoCapitalize="none"
            />

            <TextInput
              label="Address *"
              value={formData.address}
              onChangeText={(value) => updateField('address', value)}
              mode="outlined"
              style={styles.input}
              multiline
              numberOfLines={2}
              error={!!errors.address}
            />
            {errors.address && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.address}</Text>}
          </Card.Content>
        </Card>

        {/* Salary Information */}
        <Card style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Salary Information
            </Text>

            <SegmentedButtons
              value={formData.salaryType}
              onValueChange={(value) => updateField('salaryType', value)}
              buttons={[
                { value: 'hourly', label: 'Hourly' },
                { value: 'monthly', label: 'Monthly' },
                { value: 'commission', label: 'Commission' },
              ]}
              style={styles.segmentedButtons}
            />

            <TextInput
              label={`${formData.salaryType === 'hourly' ? 'Hourly Rate' : formData.salaryType === 'monthly' ? 'Monthly Salary' : 'Base Salary'} (৳) *`}
              value={formData.salaryAmount}
              onChangeText={(value) => updateField('salaryAmount', value)}
              mode="outlined"
              style={styles.input}
              keyboardType="numeric"
              error={!!errors.salaryAmount}
            />
            {errors.salaryAmount && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.salaryAmount}</Text>}

            {formData.salaryType === 'commission' && (
              <>
                <TextInput
                  label="Commission Rate (%) *"
                  value={formData.commissionRate}
                  onChangeText={(value) => updateField('commissionRate', value)}
                  mode="outlined"
                  style={styles.input}
                  keyboardType="numeric"
                  error={!!errors.commissionRate}
                />
                {errors.commissionRate && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.commissionRate}</Text>}
              </>
            )}
          </Card.Content>
        </Card>

        {/* Skills */}
        <Card style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Skills *
            </Text>
            <View style={styles.skillsGrid}>
              {TAILOR_SHOP_CONFIG.EMPLOYEE_SKILLS.map(skill => (
                <Chip
                  key={skill}
                  selected={formData.skills.includes(skill)}
                  onPress={() => toggleSkill(skill)}
                  style={styles.skillChip}
                  mode={formData.skills.includes(skill) ? 'flat' : 'outlined'}
                >
                  {skill}
                </Chip>
              ))}
            </View>
            {errors.skills && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.skills}</Text>}
          </Card.Content>
        </Card>

        {/* Save Button */}
        <View style={styles.saveButtonContainer}>
          <Button
            mode="contained"
            onPress={handleSave}
            loading={loading}
            disabled={loading}
            style={styles.saveButton}
            contentStyle={styles.saveButtonContent}
          >
            {isEditing ? 'Update Employee' : 'Add Employee'}
          </Button>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.md,
  },
  sectionCard: {
    marginVertical: SPACING.md,
    borderRadius: SHAPE.corner.lg,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.title.medium.fontSize,
    fontWeight: TYPOGRAPHY.title.medium.fontWeight,
    marginBottom: SPACING.md,
  },
  input: {
    marginBottom: SPACING.md,
  },
  errorText: {
    fontSize: TYPOGRAPHY.body.small.fontSize,
    marginTop: -SPACING.sm,
    marginBottom: SPACING.md,
  },
  fieldLabel: {
    fontSize: TYPOGRAPHY.body.medium.fontSize,
    fontWeight: '500',
    marginBottom: SPACING.sm,
    marginTop: SPACING.sm,
  },
  roleChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
    marginBottom: SPACING.md,
  },
  roleChip: {
    marginBottom: SPACING.xs,
  },
  segmentedButtons: {
    marginBottom: SPACING.md,
  },
  skillsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  skillChip: {
    marginBottom: SPACING.xs,
  },
  saveButtonContainer: {
    paddingVertical: SPACING.xl,
    paddingBottom: SPACING.xxl,
  },
  saveButton: {
    borderRadius: SHAPE.corner.lg,
  },
  saveButtonContent: {
    paddingVertical: SPACING.sm,
  },
});

export default AddEmployeeScreen;
