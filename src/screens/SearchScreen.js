import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, BackHandler } from 'react-native';
import {
  Searchbar,
  Text,
  Surface,
  Chip,
  IconButton,
  useTheme,
  Button,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useData } from '../context/DataContext';
import { StorageService } from '../services/storageService';
import navigationService from '../services/NavigationService';
import { SPACING, SHAPE, TYPOGRAPHY, getBorderColor } from '../theme/designTokens';

const SearchScreen = ({ navigation, route }) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const { orders, products, customers } = useData();

  // Get search parameters from route
  const {
    type = 'global',
    data = [],
    searchFields = ['name'],
    placeholder = 'Search...',
    showFilters = false,
    filters = [],
  } = route.params || {};

  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [recentSearches, setRecentSearches] = useState([]);
  const [selectedFilter, setSelectedFilter] = useState('All');
  const searchTimeoutRef = useRef(null);

  // Load recent searches on mount
  useEffect(() => {
    loadRecentSearches();
  }, [type]);

  // Handle back button
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      navigation.goBack();
      return true;
    });

    return () => backHandler.remove();
  }, [navigation]);

  const loadRecentSearches = async () => {
    try {
      const storageKey = `recentSearches_${type}`;
      const recent = await StorageService.get(storageKey, true) || [];
      setRecentSearches(recent.slice(0, 5));
    } catch (error) {
      console.warn('Failed to load recent searches:', error);
    }
  };

  const saveRecentSearch = async (searchQuery) => {
    if (!searchQuery.trim()) return;

    try {
      const storageKey = `recentSearches_${type}`;
      const recent = await StorageService.get(storageKey, true) || [];
      const updated = [
        searchQuery,
        ...recent.filter(item => item !== searchQuery)
      ].slice(0, 5);

      await StorageService.set(storageKey, updated);
      setRecentSearches(updated);
    } catch (error) {
      console.warn('Failed to save recent search:', error);
    }
  };

  const clearRecentSearches = async () => {
    try {
      const storageKey = `recentSearches_${type}`;
      await StorageService.remove(storageKey);
      setRecentSearches([]);
    } catch (error) {
      console.warn('Failed to clear recent searches:', error);
    }
  };

  // Enhanced search algorithm
  const generateSuggestions = useCallback((searchQuery) => {
    if (!searchQuery.trim()) {
      setSuggestions([]);
      return;
    }

    const searchData = data.length > 0 ? data : [...(orders || []), ...(products || []), ...(customers || [])];
    const query = searchQuery.toLowerCase();

    const scored = searchData.map(item => {
      let score = 0;
      let matchedFields = [];
      let bestMatch = '';

      searchFields.forEach(field => {
        const value = item[field];
        if (value) {
          const fieldValue = value.toString().toLowerCase();

          if (fieldValue === query) {
            score += 100;
            matchedFields.push(field);
            bestMatch = fieldValue;
          } else if (fieldValue.startsWith(query)) {
            score += 50;
            matchedFields.push(field);
            if (!bestMatch) bestMatch = fieldValue;
          } else if (fieldValue.includes(query)) {
            score += 25;
            matchedFields.push(field);
            if (!bestMatch) bestMatch = fieldValue;
          }
        }
      });

      return {
        ...item,
        _searchScore: score,
        _matchedFields: matchedFields,
        _bestMatch: bestMatch,
        matchedField: matchedFields[0]
      };
    })
    .filter(item => item._searchScore > 0)
    .sort((a, b) => b._searchScore - a._searchScore)
    .slice(0, 10);

    setSuggestions(scored);
  }, [data, searchFields, orders, products, customers]);

  const handleQueryChange = (searchQuery) => {
    setQuery(searchQuery);

    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      generateSuggestions(searchQuery);
    }, 300);
  };

  const handleSearch = async (searchQuery = query) => {
    if (!searchQuery.trim()) return;

    try {
      await saveRecentSearch(searchQuery);
      // Perform search action here
      console.log('Searching for:', searchQuery);
    } catch (error) {
      console.warn('Search failed:', error);
    }
  };

  const handleNavigateToResult = (item) => {
    try {
      if (item.customerName || item.customer) {
        console.log('Navigating to order:', item.id);
        navigationService.navigate('Orders');
      } else if (item.price) {
        console.log('Navigating to product:', item.name);
        navigationService.navigate('Products');
      } else if (item.email) {
        console.log('Navigating to customer:', item.name);
        navigationService.navigate('Customers');
      }
    } catch (error) {
      console.error('Failed to navigate to search result:', error);
    }
  };

  const handleSuggestionSelect = (item) => {
    const searchTerm = item.name || item[searchFields[0]] || '';
    setQuery(searchTerm);
    saveRecentSearch(searchTerm);
    handleNavigateToResult(item);
    navigation.goBack();
  };

  const handleFilterChange = (filter) => {
    setSelectedFilter(filter);
  };

  const getSearchIcon = (item) => {
    if (item.customerName || item.customer) return 'clipboard-text';
    if (item.price) return 'package-variant';
    if (item.email) return 'account';
    return 'magnify';
  };

  const getPlaceholder = () => {
    switch (type) {
      case 'orders': return 'Search orders...';
      case 'products': return 'Search products...';
      case 'customers': return 'Search customers...';
      case 'activities': return 'Search activities...';
      default: return placeholder;
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background, paddingTop: insets.top }]}>
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: theme.colors.outline + '20' }]}>
        <IconButton
          icon="arrow-left"
          size={24}
          onPress={() => navigation.goBack()}
          iconColor={theme.colors.onSurface}
        />
        <Text style={[styles.headerTitle, {
          color: theme.colors.onSurface,
          fontSize: TYPOGRAPHY.title.medium.fontSize,
          fontWeight: TYPOGRAPHY.title.medium.fontWeight
        }]}>
          Search {type.charAt(0).toUpperCase() + type.slice(1)}
        </Text>
        <View style={{ width: 48 }} />
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder={getPlaceholder()}
          onChangeText={handleQueryChange}
          value={query}
          onSubmitEditing={() => handleSearch()}
          style={[
            styles.searchBar,
            {
              backgroundColor: theme.colors.surfaceVariant,
              borderColor: theme.colors.outline,
            }
          ]}
          inputStyle={{ color: theme.colors.onSurface }}
          placeholderTextColor={theme.colors.onSurfaceVariant}
          iconColor={theme.colors.onSurfaceVariant}
          autoFocus
        />
      </View>

      {/* Filters */}
      {showFilters && filters.length > 0 && (
        <View style={styles.filtersContainer}>
          {filters.map((filter) => (
            <Chip
              key={filter}
              selected={selectedFilter === filter}
              onPress={() => handleFilterChange(filter)}
              style={[
                styles.filterChip,
                {
                  backgroundColor: selectedFilter === filter
                    ? theme.colors.primaryContainer
                    : theme.colors.surface,
                  borderColor: selectedFilter === filter
                    ? theme.colors.primary
                    : theme.colors.outline,
                }
              ]}
              textStyle={{
                color: selectedFilter === filter
                  ? theme.colors.onPrimaryContainer
                  : theme.colors.onSurface,
              }}
              mode={selectedFilter === filter ? 'flat' : 'outlined'}
            >
              {filter}
            </Chip>
          ))}
        </View>
      )}

      {/* Results */}
      <FlatList
        data={suggestions}
        keyExtractor={(item) => item.id?.toString() || item.name || Math.random().toString()}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[styles.resultItem, { borderBottomColor: theme.colors.outline + '10' }]}
            onPress={() => handleSuggestionSelect(item)}
          >
            <Icon
              name={getSearchIcon(item)}
              size={20}
              color={theme.colors.onSurfaceVariant}
            />
            <View style={styles.resultText}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                {item.name || item[searchFields[0]] || 'Unknown'}
              </Text>
              {item._bestMatch && item._bestMatch !== (item.name || item[searchFields[0]]) && (
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 2 }}>
                  Match: {item._bestMatch}
                </Text>
              )}
            </View>
            <Icon name="chevron-right" size={16} color={theme.colors.onSurfaceVariant} />
          </TouchableOpacity>
        )}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={() => (
          recentSearches.length > 0 && !query ? (
            <View>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, {
                  color: theme.colors.onSurfaceVariant,
                  fontSize: TYPOGRAPHY.body.medium.fontSize,
                  fontWeight: TYPOGRAPHY.body.medium.fontWeight
                }]}>
                  Recent Searches
                </Text>
                <TouchableOpacity onPress={clearRecentSearches}>
                  <Text style={{ color: theme.colors.primary, fontSize: 12 }}>
                    Clear
                  </Text>
                </TouchableOpacity>
              </View>
              {recentSearches.map((search, index) => (
                <TouchableOpacity
                  key={index}
                  style={[styles.resultItem, { borderBottomColor: theme.colors.outline + '10' }]}
                  onPress={() => {
                    setQuery(search);
                    handleSearch(search);
                  }}
                >
                  <Icon name="history" size={16} color={theme.colors.onSurfaceVariant} />
                  <Text style={[styles.resultText, { color: theme.colors.onSurface }]}>
                    {search}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          ) : null
        )}
        ListEmptyComponent={() => (
          query ? (
            <View style={styles.emptyState}>
              <Icon name="magnify" size={48} color={theme.colors.onSurfaceVariant} />
              <Text style={{ color: theme.colors.onSurfaceVariant, marginTop: 8 }}>
                No results found for "{query}"
              </Text>
            </View>
          ) : null
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
    paddingVertical: 8,
    borderBottomWidth: 1,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    fontWeight: '600',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchBar: {
    borderRadius: 12,
    elevation: 1,
  },
  filtersContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  filterChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  sectionTitle: {
    fontWeight: '500',
  },
  resultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  resultText: {
    marginLeft: 12,
    flex: 1,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
  },
});

export default SearchScreen;
