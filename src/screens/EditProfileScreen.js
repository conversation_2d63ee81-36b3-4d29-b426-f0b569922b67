import React, { useState, useEffect } from 'react';
import { ScrollView, View, StyleSheet, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  Text,
  TextInput,
  Button,
  Surface,
  Avatar,
  IconButton,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import ImagePicker from '../components/ImagePicker';

const EditProfileScreen = () => {
  const { theme } = useTheme();
  const { state, actions } = useData();
  const navigation = useNavigation();

  const [formData, setFormData] = useState({
    storeName: '',
    ownerName: '',
    email: '',
    phone: '',
    address: '',
    taxRate: '',
    profileImage: null,
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (state.settings) {
      setFormData({
        storeName: state.settings.storeName || '',
        ownerName: state.settings.ownerName || '',
        email: state.settings.email || '',
        phone: state.settings.phone || '',
        address: state.settings.address || '',
        taxRate: (state.settings.taxRate * 100).toString() || '8',
        profileImage: state.settings.profileImage || null,
      });
    }
  }, [state.settings]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.storeName.trim()) {
      newErrors.storeName = 'Store name is required';
    }

    if (!formData.ownerName.trim()) {
      newErrors.ownerName = 'Owner name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    const taxRate = parseFloat(formData.taxRate);
    if (isNaN(taxRate) || taxRate < 0 || taxRate > 50) {
      newErrors.taxRate = 'Tax rate must be between 0% and 50%';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors and try again.');
      return;
    }

    try {
      const updatedProfile = {
        ...formData,
        taxRate: parseFloat(formData.taxRate) / 100 || 0.08,
      };

      actions.updateSettings(updatedProfile);

      Alert.alert('Success', 'Profile updated successfully!', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Edit Profile"
        subtitle="Update your business information"
        showSearch={false}
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Image Section */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Profile Image
          </Text>

          <View style={styles.avatarSection}>
            <View style={styles.avatarContainer}>
              {formData.profileImage ? (
                <Avatar.Image
                  size={100}
                  source={{ uri: formData.profileImage }}
                />
              ) : (
                <Avatar.Text
                  size={100}
                  label={formData.storeName.split(' ').map(word => word[0]).join('').substring(0, 2)}
                  style={{ backgroundColor: theme.colors.primary }}
                />
              )}
              <IconButton
                icon="camera"
                mode="contained"
                size={20}
                style={styles.cameraButton}
                onPress={() => {
                  // Image picker is handled by the ImagePicker component below
                  console.log('Use the image picker below to upload profile image');
                }}
              />
            </View>

            <View style={styles.compactImageContainer}>
              <ImagePicker
                onImageSelected={(imageUri) => handleInputChange('profileImage', imageUri)}
                currentImage={formData.profileImage}
                placeholder="Upload profile image"
              />
            </View>
          </View>
        </Surface>

        {/* Store Information */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Store Information
          </Text>

          <TextInput
            label="Store Name *"
            value={formData.storeName}
            onChangeText={(value) => handleInputChange('storeName', value)}
            mode="outlined"
            style={styles.input}
            error={!!errors.storeName}
            left={<TextInput.Icon icon="store" />}
          />
          {errors.storeName && (
            <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
              {errors.storeName}
            </Text>
          )}

          <TextInput
            label="Owner Name *"
            value={formData.ownerName}
            onChangeText={(value) => handleInputChange('ownerName', value)}
            mode="outlined"
            style={styles.input}
            error={!!errors.ownerName}
            left={<TextInput.Icon icon="account" />}
          />
          {errors.ownerName && (
            <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
              {errors.ownerName}
            </Text>
          )}
        </Surface>

        {/* Contact Information */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Contact Information
          </Text>

          <TextInput
            label="Email Address *"
            value={formData.email}
            onChangeText={(value) => handleInputChange('email', value)}
            mode="outlined"
            style={styles.input}
            error={!!errors.email}
            keyboardType="email-address"
            autoCapitalize="none"
            left={<TextInput.Icon icon="email" />}
          />
          {errors.email && (
            <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
              {errors.email}
            </Text>
          )}

          <TextInput
            label="Phone Number *"
            value={formData.phone}
            onChangeText={(value) => handleInputChange('phone', value)}
            mode="outlined"
            style={styles.input}
            error={!!errors.phone}
            keyboardType="phone-pad"
            left={<TextInput.Icon icon="phone" />}
          />
          {errors.phone && (
            <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
              {errors.phone}
            </Text>
          )}

          <TextInput
            label="Business Address"
            value={formData.address}
            onChangeText={(value) => handleInputChange('address', value)}
            mode="outlined"
            style={styles.input}
            multiline
            numberOfLines={2}
            left={<TextInput.Icon icon="map-marker" />}
          />
        </Surface>

        {/* Business Settings */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Business Settings
          </Text>

          <TextInput
            label="Tax Rate (%)"
            value={formData.taxRate}
            onChangeText={(value) => handleInputChange('taxRate', value)}
            mode="outlined"
            style={styles.input}
            error={!!errors.taxRate}
            keyboardType="numeric"
            left={<TextInput.Icon icon="percent" />}
            right={<TextInput.Affix text="%" />}
          />
          {errors.taxRate && (
            <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
              {errors.taxRate}
            </Text>
          )}
        </Surface>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={() => navigation.goBack()}
            style={[styles.button, styles.cancelButton]}
          >
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={handleSave}
            style={[styles.button, styles.saveButton]}
            icon="check"
          >
            Save Changes
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    marginVertical: 8,
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 16,
  },
  avatarSection: {
    alignItems: 'center',
    marginBottom: 16,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  cameraButton: {
    position: 'absolute',
    bottom: -5,
    right: -5,
  },
  compactImageContainer: {
    alignItems: 'center',
    width: '100%',
  },
  input: {
    marginBottom: 8,
  },
  errorText: {
    marginBottom: 8,
    marginLeft: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginVertical: 24,
    paddingBottom: 32,
  },
  button: {
    flex: 1,
  },
  cancelButton: {
    marginRight: 6,
  },
  saveButton: {
    marginLeft: 6,
  },
});

export default EditProfileScreen;
