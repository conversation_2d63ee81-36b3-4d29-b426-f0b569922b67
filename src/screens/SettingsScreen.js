import React, { useState } from 'react';
import { ScrollView, View, StyleSheet, Alert, Linking } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  Text,
  Switch,
  List,
  Button,
  Surface,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import { SPACING, SHAPE, TYPOGRAPHY, getBorderColor } from '../theme/designTokens';
import CommonHeader from '../components/CommonHeader';
import navigationService from '../services/NavigationService';

const SettingsScreen = ({ navigation }) => {
  const { theme, isDarkMode, toggleTheme } = useTheme();
  const { state, actions } = useData();





  const handleSettingChange = (setting, value) => {
    switch (setting) {
      case 'darkMode':
        toggleTheme();
        Alert.alert('Theme Changed', `Switched to ${value ? 'dark' : 'light'} mode!`);
        break;
    }
  };



  const handleExportData = () => {
    const dataToExport = actions.exportData();

    Alert.alert(
      'Export Data',
      `Export ${state.products.length} products, ${state.orders.length} orders, and ${state.customers.length} customers?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Export',
          onPress: () => {
            // In a real app, this would save to file or send via email
            console.log('Exported data:', dataToExport);
            Alert.alert('Success', 'Data exported successfully!');
          },
        },
      ]
    );
  };



  const handleContactSupport = () => {
    console.log('Navigating to Contact Support...');
    try {
      navigationService.navigate('ContactSupport');
    } catch (error) {
      console.error('Failed to navigate to ContactSupport:', error);
    }
  };

  const handleAbout = () => {
    console.log('Navigating to About...');
    try {
      navigationService.navigate('About');
    } catch (error) {
      console.error('Failed to navigate to About:', error);
    }
  };

  const handleHelpFAQ = () => {
    console.log('Navigating to Help & FAQ...');
    try {
      navigationService.navigate('HelpFAQ');
    } catch (error) {
      console.error('Failed to navigate to HelpFAQ:', error);
    }
  };

  const handleImportData = (importData) => {
    try {
      // Use the new importData function from DataContext
      actions.importData(importData);
      Alert.alert('Success', 'Data imported successfully!');
    } catch (error) {
      console.error('Error importing data:', error);
      Alert.alert('Error', 'Failed to import data. Please check the format and try again.');
    }
  };



  const SettingsSection = ({ title, children }) => (
    <View style={styles.sectionContainer}>
      <Text style={[styles.sectionTitle, {
        color: theme.colors.onBackground,
        fontSize: TYPOGRAPHY.sectionTitle.fontSize,
        fontWeight: TYPOGRAPHY.sectionTitle.fontWeight,
        lineHeight: TYPOGRAPHY.sectionTitle.lineHeight * TYPOGRAPHY.sectionTitle.fontSize
      }]}>
        {title}
      </Text>
      <Surface style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]} elevation={0}>
        {children}
      </Surface>
    </View>
  );

  const SettingItem = ({ icon, title, subtitle, rightComponent, onPress, iconColor }) => (
    <Surface style={styles.settingItem} elevation={0}>
      <List.Item
        title={title}
        description={subtitle}
        titleStyle={{ fontWeight: '600', color: theme.colors.onSurface }}
        descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
        left={(props) => (
          <View style={[styles.settingIcon, { backgroundColor: (iconColor || theme.colors.primary) + '15' }]}>
            <Icon name={icon} size={20} color={iconColor || theme.colors.primary} />
          </View>
        )}
        right={() => rightComponent || <Icon name="chevron-right" size={20} color={theme.colors.onSurfaceVariant} />}
        onPress={onPress}
        style={styles.listItem}
      />
    </Surface>
  );



  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Settings"
        subtitle="App preferences"
        showSearch={false}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => {
          console.log('Profile pressed - navigating to MyProfile');
          try {
            navigationService.navigate('MyProfile');
          } catch (error) {
            console.error('Failed to navigate to MyProfile:', error);
          }
        }}
      />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <SettingsSection title="App Preferences">
          <SettingItem
            icon="theme-light-dark"
            title="Dark Mode"
            subtitle="Switch between light and dark themes"
            rightComponent={
              <Switch
                value={isDarkMode}
                onValueChange={(value) => handleSettingChange('darkMode', value)}
                color={theme.colors.primary}
              />
            }
          />
        </SettingsSection>



        <SettingsSection title="Data Management">
          <SettingItem
            icon="database"
            title="Data Backup"
            subtitle="Import & export business data"
            onPress={() => {
              console.log('Navigating to Data Backup...');
              try {
                navigationService.navigate('ImportData');
              } catch (error) {
                console.error('Failed to navigate to ImportData:', error);
              }
            }}
          />
          <SettingItem
            icon="history"
            title="Activity Log"
            subtitle="View recent system activities"
            onPress={() => {
              console.log('Navigating to Activity Log...');
              try {
                navigationService.navigate('ActivityLog');
              } catch (error) {
                console.error('Failed to navigate to ActivityLog:', error);
              }
            }}
          />
        </SettingsSection>

        <SettingsSection title="Support">
          <SettingItem
            icon="help-circle"
            title="Help & FAQ"
            subtitle="Get help and find answers"
            onPress={handleHelpFAQ}
          />
          <SettingItem
            icon="email"
            title="Contact Support"
            subtitle="Get in touch with our team"
            onPress={handleContactSupport}
          />
          <SettingItem
            icon="information"
            title="About"
            subtitle="App version and information"
            onPress={handleAbout}
          />
        </SettingsSection>

        <View style={styles.footer}>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center' }}>
            Business Management App v1.0.0
          </Text>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center', marginTop: 4 }}>
            Made with ❤️ for business owners
          </Text>
        </View>

      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: SPACING.lg,
  },
  sectionContainer: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontWeight: '700',
    marginBottom: SPACING.md,
  },
  sectionCard: {
    borderRadius: SHAPE.corner.xl,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
    overflow: 'hidden',
  },
  settingItem: {
    borderRadius: 0,
    marginBottom: 1,
  },
  listItem: {
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
  },
  settingIcon: {
    width: 32,
    height: 32,
    borderRadius: SHAPE.corner.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.sm,
  },
  footer: {
    marginTop: SPACING.xxxl,
    marginBottom: SPACING.lg,
  },
});

export default SettingsScreen;
