import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useData } from '../context/DataContext';

const AddGarmentScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { addProduct, updateProduct } = useData();
  const { product } = route.params || {};
  const isEditing = !!product;

  const [formData, setFormData] = useState({
    name: '',
    category: 'readymade',
    type: 'shirt',
    size: 'M',
    color: '',
    fabric: '',
    brand: '',
    price: '',
    cost: '',
    stock: '',
    minStock: '5',
    description: '',
    sku: '',
    barcode: '',
    image: null,
  });

  const categories = [
    { id: 'readymade', name: 'Ready Made', icon: 'shirt-outline' },
    { id: 'fabric', name: 'Fabric', icon: 'color-palette-outline' },
    { id: 'accessories', name: 'Accessories', icon: 'diamond-outline' },
    { id: 'supplies', name: 'Supplies', icon: 'construct-outline' },
  ];

  const garmentTypes = {
    readymade: ['Shirt', 'Pant', 'Suit', 'Dress', 'Blouse', 'Skirt', 'Jacket', 'Kurta', 'Saree', 'Lehenga'],
    fabric: ['Cotton', 'Silk', 'Linen', 'Wool', 'Polyester', 'Chiffon', 'Georgette', 'Denim'],
    accessories: ['Buttons', 'Zippers', 'Thread', 'Lace', 'Ribbons', 'Beads', 'Sequins'],
    supplies: ['Needles', 'Scissors', 'Measuring Tape', 'Pins', 'Thimble', 'Seam Ripper'],
  };

  const sizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL', 'Free Size'];
  const colors = ['Black', 'White', 'Red', 'Blue', 'Green', 'Yellow', 'Pink', 'Purple', 'Orange', 'Brown', 'Gray', 'Navy', 'Maroon'];

  useEffect(() => {
    if (isEditing && product) {
      setFormData({
        name: product.name || '',
        category: product.category || 'readymade',
        type: product.type || 'shirt',
        size: product.size || 'M',
        color: product.color || '',
        fabric: product.fabric || '',
        brand: product.brand || '',
        price: product.price?.toString() || '',
        cost: product.cost?.toString() || '',
        stock: product.stock?.toString() || '',
        minStock: product.minStock?.toString() || '5',
        description: product.description || '',
        sku: product.sku || '',
        barcode: product.barcode || '',
        image: product.image || null,
      });
    } else {
      // Generate SKU for new products
      generateSKU();
    }
  }, [isEditing, product]);

  const generateSKU = () => {
    const timestamp = Date.now().toString().slice(-6);
    const randomNum = Math.floor(Math.random() * 100).toString().padStart(2, '0');
    setFormData(prev => ({
      ...prev,
      sku: `GRM${timestamp}${randomNum}`,
    }));
  };

  const updateFormData = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Auto-generate name based on selections
    if (field === 'type' || field === 'color' || field === 'size') {
      const newFormData = { ...formData, [field]: value };
      if (newFormData.type && newFormData.color && newFormData.size) {
        const autoName = `${newFormData.color} ${newFormData.type} - ${newFormData.size}`;
        setFormData(prev => ({
          ...prev,
          [field]: value,
          name: autoName,
        }));
      }
    }
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      Alert.alert('Error', 'Product name is required');
      return false;
    }
    if (!formData.price.trim()) {
      Alert.alert('Error', 'Price is required');
      return false;
    }
    if (!formData.stock.trim()) {
      Alert.alert('Error', 'Stock quantity is required');
      return false;
    }
    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      const productData = {
        ...formData,
        price: parseFloat(formData.price) || 0,
        cost: parseFloat(formData.cost) || 0,
        stock: parseInt(formData.stock) || 0,
        minStock: parseInt(formData.minStock) || 5,
        id: isEditing ? product.id : undefined,
      };

      if (isEditing) {
        await updateProduct(productData);
      } else {
        await addProduct(productData);
      }

      Alert.alert('Success', `Product ${isEditing ? 'updated' : 'added'} successfully`, [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to save product');
      console.error('Error saving product:', error);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    backButton: {
      marginRight: 16,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      flex: 1,
    },
    saveButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
    },
    saveButtonText: {
      color: 'white',
      fontWeight: '600',
    },
    scrollContent: {
      padding: 16,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    inputGroup: {
      marginBottom: 16,
    },
    label: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
      marginBottom: 8,
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 8,
      padding: 12,
      fontSize: 16,
      color: theme.colors.text,
      backgroundColor: theme.colors.surface,
    },
    categoryContainer: {
      flexDirection: 'row',
      gap: 8,
      marginBottom: 16,
    },
    categoryButton: {
      flex: 1,
      padding: 12,
      borderRadius: 8,
      borderWidth: 1,
      alignItems: 'center',
    },
    categoryButtonText: {
      fontSize: 12,
      fontWeight: '600',
      marginTop: 4,
    },
    chipContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
    },
    chip: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
      backgroundColor: theme.colors.surface,
    },
    chipSelected: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    chipText: {
      fontSize: 12,
      color: theme.colors.text,
    },
    chipTextSelected: {
      color: 'white',
    },
    row: {
      flexDirection: 'row',
      gap: 12,
    },
    flex1: {
      flex: 1,
    },
    imageContainer: {
      height: 120,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
      backgroundColor: theme.colors.surface,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 16,
    },
    imagePreview: {
      width: '100%',
      height: '100%',
      borderRadius: 8,
    },
    imagePlaceholder: {
      alignItems: 'center',
    },
    imagePlaceholderText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginTop: 8,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {isEditing ? 'Edit Product' : 'Add Product'}
        </Text>
        <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
          <Text style={styles.saveButtonText}>Save</Text>
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.scrollContent} showsVerticalScrollIndicator={false}>
          {/* Product Image */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Product Image</Text>
            <TouchableOpacity style={styles.imageContainer}>
              {formData.image ? (
                <Image source={{ uri: formData.image }} style={styles.imagePreview} />
              ) : (
                <View style={styles.imagePlaceholder}>
                  <Ionicons name="camera-outline" size={32} color={theme.colors.textSecondary} />
                  <Text style={styles.imagePlaceholderText}>Tap to add image</Text>
                </View>
              )}
            </TouchableOpacity>
          </View>

          {/* Category Selection */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Category</Text>
            <View style={styles.categoryContainer}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.categoryButton,
                    {
                      borderColor: formData.category === category.id ? theme.colors.primary : theme.colors.border,
                      backgroundColor: formData.category === category.id ? theme.colors.primary + '20' : theme.colors.surface,
                    },
                  ]}
                  onPress={() => updateFormData('category', category.id)}
                >
                  <Ionicons
                    name={category.icon}
                    size={24}
                    color={formData.category === category.id ? theme.colors.primary : theme.colors.text}
                  />
                  <Text
                    style={[
                      styles.categoryButtonText,
                      { color: formData.category === category.id ? theme.colors.primary : theme.colors.text },
                    ]}
                  >
                    {category.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Product Type */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Type</Text>
            <View style={styles.chipContainer}>
              {garmentTypes[formData.category]?.map((type) => (
                <TouchableOpacity
                  key={type}
                  style={[
                    styles.chip,
                    formData.type === type.toLowerCase() && styles.chipSelected,
                  ]}
                  onPress={() => updateFormData('type', type.toLowerCase())}
                >
                  <Text
                    style={[
                      styles.chipText,
                      formData.type === type.toLowerCase() && styles.chipTextSelected,
                    ]}
                  >
                    {type}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Basic Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Basic Information</Text>
            
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Product Name *</Text>
              <TextInput
                style={styles.input}
                value={formData.name}
                onChangeText={(value) => updateFormData('name', value)}
                placeholder="Enter product name"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>

            <View style={styles.row}>
              <View style={[styles.inputGroup, styles.flex1]}>
                <Text style={styles.label}>Size</Text>
                <View style={styles.chipContainer}>
                  {sizes.map((size) => (
                    <TouchableOpacity
                      key={size}
                      style={[
                        styles.chip,
                        formData.size === size && styles.chipSelected,
                      ]}
                      onPress={() => updateFormData('size', size)}
                    >
                      <Text
                        style={[
                          styles.chipText,
                          formData.size === size && styles.chipTextSelected,
                        ]}
                      >
                        {size}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Color</Text>
              <View style={styles.chipContainer}>
                {colors.map((color) => (
                  <TouchableOpacity
                    key={color}
                    style={[
                      styles.chip,
                      formData.color === color && styles.chipSelected,
                    ]}
                    onPress={() => updateFormData('color', color)}
                  >
                    <Text
                      style={[
                        styles.chipText,
                        formData.color === color && styles.chipTextSelected,
                      ]}
                    >
                      {color}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.row}>
              <View style={[styles.inputGroup, styles.flex1]}>
                <Text style={styles.label}>Price (৳) *</Text>
                <TextInput
                  style={styles.input}
                  value={formData.price}
                  onChangeText={(value) => updateFormData('price', value)}
                  placeholder="0"
                  placeholderTextColor={theme.colors.textSecondary}
                  keyboardType="numeric"
                />
              </View>
              <View style={[styles.inputGroup, styles.flex1]}>
                <Text style={styles.label}>Stock *</Text>
                <TextInput
                  style={styles.input}
                  value={formData.stock}
                  onChangeText={(value) => updateFormData('stock', value)}
                  placeholder="0"
                  placeholderTextColor={theme.colors.textSecondary}
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>SKU</Text>
              <TextInput
                style={styles.input}
                value={formData.sku}
                onChangeText={(value) => updateFormData('sku', value)}
                placeholder="Auto-generated"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default AddGarmentScreen;
