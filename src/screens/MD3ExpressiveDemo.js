/**
 * MD3 Expressive Demo Screen
 * Showcases the new Material Design 3 Expressive design system
 */

import React, { useState } from 'react';
import { ScrollView, View, StyleSheet } from 'react-native';
import { Text, useTheme, Switch, Divider } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import CommonHeader from '../components/CommonHeader';
import MD3ExpressiveButton from '../components/MD3ExpressiveButton';
import MD3ExpressiveCard from '../components/MD3ExpressiveCard';
import UnifiedCard from '../components/UnifiedCard';
import { 
  SPACING, 
  TYPOGRAPHY, 
  getTypographyStyle 
} from '../theme/designTokens';

const MD3ExpressiveDemo = ({ navigation }) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const [expressive, setExpressive] = useState(false);

  const titleStyle = getTypographyStyle('headline', 'small', expressive);
  const sectionTitleStyle = getTypographyStyle('title', 'medium', expressive);
  const bodyStyle = getTypographyStyle('body', 'medium');

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="MD3 Expressive Demo"
        showBack
        onBackPress={() => navigation.goBack()}
        onSearchPress={() => console.log('Search pressed')}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => console.log('Profile pressed')}
      />

      <ScrollView 
        style={styles.scrollContent}
        contentContainerStyle={[
          styles.content,
          { paddingBottom: insets.bottom + SPACING.xl }
        ]}
      >
        {/* Header */}
        <View style={styles.section}>
          <Text style={[titleStyle, { color: theme.colors.onBackground }]}>
            Material Design 3 Expressive
          </Text>
          <Text style={[bodyStyle, { color: theme.colors.onSurfaceVariant, marginTop: SPACING.sm }]}>
            Experience Google's latest design system with vibrant colors, expressive typography, and adaptive components.
          </Text>
        </View>

        {/* Expressive Toggle */}
        <View style={styles.section}>
          <View style={styles.toggleRow}>
            <Text style={[sectionTitleStyle, { color: theme.colors.onBackground }]}>
              Expressive Mode
            </Text>
            <Switch
              value={expressive}
              onValueChange={setExpressive}
              color={theme.colors.primary}
            />
          </View>
          <Text style={[bodyStyle, { color: theme.colors.onSurfaceVariant, marginTop: SPACING.xs }]}>
            Toggle to see the difference between standard and expressive styling
          </Text>
        </View>

        <Divider style={{ marginVertical: SPACING.lg }} />

        {/* Typography Showcase */}
        <View style={styles.section}>
          <Text style={[sectionTitleStyle, { color: theme.colors.onBackground }]}>
            Typography Scale
          </Text>
          
          <View style={styles.typographyShowcase}>
            <Text style={[
              getTypographyStyle('display', 'small', expressive),
              { color: theme.colors.onBackground }
            ]}>
              Display Small
            </Text>
            <Text style={[
              getTypographyStyle('headline', 'large', expressive),
              { color: theme.colors.onBackground }
            ]}>
              Headline Large
            </Text>
            <Text style={[
              getTypographyStyle('title', 'large', expressive),
              { color: theme.colors.onBackground }
            ]}>
              Title Large
            </Text>
            <Text style={[
              getTypographyStyle('body', 'large', expressive),
              { color: theme.colors.onSurfaceVariant }
            ]}>
              Body Large - This is how regular text appears in the new design system
            </Text>
            <Text style={[
              getTypographyStyle('label', 'large', expressive),
              { color: theme.colors.primary }
            ]}>
              Label Large
            </Text>
          </View>
        </View>

        <Divider style={{ marginVertical: SPACING.lg }} />

        {/* Button Showcase */}
        <View style={styles.section}>
          <Text style={[sectionTitleStyle, { color: theme.colors.onBackground }]}>
            Button Variants
          </Text>
          
          <View style={styles.buttonShowcase}>
            <MD3ExpressiveButton
              variant="filled"
              expressive={expressive}
              icon="heart"
              onPress={() => console.log('Filled button pressed')}
            >
              Filled Button
            </MD3ExpressiveButton>
            
            <MD3ExpressiveButton
              variant="outlined"
              expressive={expressive}
              icon="star"
              onPress={() => console.log('Outlined button pressed')}
            >
              Outlined Button
            </MD3ExpressiveButton>
            
            <MD3ExpressiveButton
              variant="text"
              expressive={expressive}
              icon="share"
              onPress={() => console.log('Text button pressed')}
            >
              Text Button
            </MD3ExpressiveButton>
            
            <MD3ExpressiveButton
              variant="tonal"
              expressive={expressive}
              icon="download"
              size="large"
              onPress={() => console.log('Tonal button pressed')}
            >
              Tonal Button
            </MD3ExpressiveButton>
          </View>
        </View>

        <Divider style={{ marginVertical: SPACING.lg }} />

        {/* Card Showcase */}
        <View style={styles.section}>
          <Text style={[sectionTitleStyle, { color: theme.colors.onBackground }]}>
            Card Variants
          </Text>
          
          <MD3ExpressiveCard
            variant="elevated"
            expressive={expressive}
            title="Elevated Card"
            subtitle="Surface container low"
            description="This card uses elevation to create depth and hierarchy in the interface."
            onPress={() => console.log('Elevated card pressed')}
          />
          
          <MD3ExpressiveCard
            variant="filled"
            expressive={expressive}
            title="Filled Card"
            subtitle="Surface container highest"
            description="This card uses a filled background to distinguish content areas."
            onPress={() => console.log('Filled card pressed')}
          />
          
          <MD3ExpressiveCard
            variant="outlined"
            expressive={expressive}
            title="Outlined Card"
            subtitle="Surface with outline"
            description="This card uses an outline to define boundaries without elevation."
            onPress={() => console.log('Outlined card pressed')}
          />
        </View>

        {/* Enhanced UnifiedCard */}
        <View style={styles.section}>
          <Text style={[sectionTitleStyle, { color: theme.colors.onBackground }]}>
            Enhanced Components
          </Text>
          
          <UnifiedCard
            title="Enhanced Product Card"
            subtitle="৳299.99"
            description="Updated with MD3 Expressive design tokens"
            icon="cake"
            expressive={expressive}
            elevation={2}
            status="In Stock"
            statusColor={theme.colors.success}
            onPress={() => console.log('Enhanced card pressed')}
            primaryAction={{
              title: "Add to Cart",
              onPress: () => console.log('Add to cart'),
              icon: "cart-plus"
            }}
            theme={theme}
          />
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
  },
  content: {
    padding: SPACING.lg,
  },
  section: {
    marginBottom: SPACING.xl,
  },
  toggleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  typographyShowcase: {
    marginTop: SPACING.lg,
    gap: SPACING.md,
  },
  buttonShowcase: {
    marginTop: SPACING.lg,
    gap: SPACING.md,
  },
});

export default MD3ExpressiveDemo;
