import React, { useState, useRef } from 'react';
import { ScrollView, View, StyleSheet, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  Card,
  Text,
  Button,
  Surface,
  Avatar,
  List,
  Divider,
  Switch,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import { SPACING, SHAPE, TYPOGRAPHY, getBorderColor } from '../theme/designTokens';
import EditProfileBottomSheet from '../components/EditProfileBottomSheet';

import PaymentMethodsModal from '../components/PaymentMethodsModal';
import CommonHeader from '../components/CommonHeader';

const MyProfileScreen = () => {
  const { theme } = useTheme();
  const { state, actions } = useData();
  const navigation = useNavigation();

  // Bottom sheet refs
  const editProfileBottomSheetRef = useRef(null);

  // Modal states
  const [paymentMethodsModalVisible, setPaymentMethodsModalVisible] = useState(false);

  // Profile-related settings state
  const [notifications, setNotifications] = useState(state.settings.notifications || true);
  const [autoBackup, setAutoBackup] = useState(state.settings.autoBackup || true);

  const handleProfileSave = (profileData) => {
    actions.updateSettings(profileData);
    Alert.alert('Success', 'Profile updated successfully!');
  };



  const handlePaymentMethodsSave = (paymentMethods) => {
    actions.updateSettings({ paymentMethods });
    Alert.alert('Success', 'Payment methods updated successfully!');
  };

  const handleSettingChange = (key, value) => {
    const newSettings = { [key]: value };
    actions.updateSettings(newSettings);

    // Update local state
    if (key === 'notifications') setNotifications(value);
    if (key === 'autoBackup') setAutoBackup(value);
  };

  const ProfileHeader = () => (
    <Surface style={[styles.profileCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
      <View style={styles.profileContent}>
        <View style={styles.profileHeader}>
          <View style={styles.profileMain}>
            <View style={[styles.avatarContainer, { backgroundColor: theme.colors.primary + '10' }]}>
              {state.settings?.profileImage ? (
                <Avatar.Image
                  size={56}
                  source={{ uri: state.settings.profileImage }}
                />
              ) : (
                <Avatar.Text
                  size={56}
                  label={state.settings.storeName.split(' ').map(word => word[0]).join('').substring(0, 2)}
                  style={{ backgroundColor: theme.colors.primary }}
                />
              )}
              <View style={[styles.statusIndicator, { backgroundColor: '#4CAF50' }]} />
            </View>
            <View style={styles.profileInfo}>
              <Text style={{
                fontWeight: '700',
                color: theme.colors.onSurface,
                fontSize: TYPOGRAPHY.cardTitle.fontSize,
                lineHeight: TYPOGRAPHY.cardTitle.lineHeight * TYPOGRAPHY.cardTitle.fontSize
              }}>
                {state.settings.storeName}
              </Text>
              <Text style={{
                color: theme.colors.onSurfaceVariant,
                marginTop: 2,
                fontSize: TYPOGRAPHY.subtitle.fontSize,
                fontWeight: TYPOGRAPHY.subtitle.fontWeight
              }}>
                {state.settings.ownerName}
              </Text>
            </View>
          </View>
          <Button
            mode="outlined"
            style={styles.editButton}
            onPress={() => navigation.navigate('EditProfile')}
            icon="pencil"
            compact
          >
            Edit
          </Button>
        </View>

        <View style={styles.contactInfo}>
          <View style={styles.contactRow}>
            <View style={styles.contactItem}>
              <Icon name="email" size={14} color={theme.colors.onSurfaceVariant} />
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginLeft: 6 }} numberOfLines={1}>
                {state.settings.email}
              </Text>
            </View>
            <View style={styles.contactItem}>
              <Icon name="phone" size={14} color={theme.colors.onSurfaceVariant} />
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginLeft: 6 }}>
                {state.settings.phone}
              </Text>
            </View>
          </View>
          <View style={styles.contactItem}>
            <Icon name="map-marker" size={14} color={theme.colors.onSurfaceVariant} />
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginLeft: 6 }} numberOfLines={2}>
              {state.settings.address}
            </Text>
          </View>
        </View>
      </View>
    </Surface>
  );



  const BusinessSection = () => (
    <Surface style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
      <Text style={[styles.sectionTitle, {
        color: theme.colors.onSurface,
        fontSize: TYPOGRAPHY.sectionTitle.fontSize,
        fontWeight: TYPOGRAPHY.sectionTitle.fontWeight,
        lineHeight: TYPOGRAPHY.sectionTitle.lineHeight * TYPOGRAPHY.sectionTitle.fontSize
      }]}>
        Business Settings
      </Text>



      <List.Item
        title="Payment Methods"
        description="Configure accepted payment options"
        titleStyle={{ fontWeight: '600', color: theme.colors.onSurface }}
        descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
        left={(props) => (
          <View style={[styles.settingIcon, { backgroundColor: theme.colors.secondary + '15' }]}>
            <Icon name="cash-register" size={24} color={theme.colors.secondary} />
          </View>
        )}
        right={() => <Icon name="chevron-right" size={20} color={theme.colors.onSurfaceVariant} />}
        onPress={() => navigation.navigate('PaymentMethods')}
        style={styles.listItem}
      />

      <Divider style={{ marginVertical: 4 }} />

      <List.Item
        title="Tax Settings"
        description={`Current rate: ${(state.settings.taxRate * 100).toFixed(1)}%`}
        titleStyle={{ fontWeight: '600', color: theme.colors.onSurface }}
        descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
        left={(props) => (
          <View style={[styles.settingIcon, { backgroundColor: theme.colors.tertiary + '15' }]}>
            <Icon name="percent" size={24} color={theme.colors.tertiary} />
          </View>
        )}
        right={() => <Icon name="chevron-right" size={20} color={theme.colors.onSurfaceVariant} />}
        onPress={() => navigation.navigate('EditProfile')}
        style={styles.listItem}
      />
    </Surface>
  );

  const ProfilePreferencesSection = () => (
    <Surface style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
      <Text style={[styles.sectionTitle, {
        color: theme.colors.onSurface,
        fontSize: TYPOGRAPHY.sectionTitle.fontSize,
        fontWeight: TYPOGRAPHY.sectionTitle.fontWeight,
        lineHeight: TYPOGRAPHY.sectionTitle.lineHeight * TYPOGRAPHY.sectionTitle.fontSize
      }]}>
        Profile Preferences
      </Text>

      <List.Item
        title="Notifications"
        description="Receive order and system notifications"
        titleStyle={{ fontWeight: '600', color: theme.colors.onSurface }}
        descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
        left={(props) => (
          <View style={[styles.settingIcon, { backgroundColor: theme.colors.primary + '15' }]}>
            <Icon name="bell" size={24} color={theme.colors.primary} />
          </View>
        )}
        right={() => (
          <Switch
            value={notifications}
            onValueChange={(value) => handleSettingChange('notifications', value)}
            color={theme.colors.primary}
          />
        )}
        style={styles.listItem}
      />

      <Divider style={{ marginVertical: 4 }} />

      <List.Item
        title="Auto Backup"
        description="Automatically backup profile data daily"
        titleStyle={{ fontWeight: '600', color: theme.colors.onSurface }}
        descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
        left={(props) => (
          <View style={[styles.settingIcon, { backgroundColor: theme.colors.secondary + '15' }]}>
            <Icon name="backup-restore" size={24} color={theme.colors.secondary} />
          </View>
        )}
        right={() => (
          <Switch
            value={autoBackup}
            onValueChange={(value) => handleSettingChange('autoBackup', value)}
            color={theme.colors.primary}
          />
        )}
        style={styles.listItem}
      />

      <Divider style={{ marginVertical: 4 }} />

      <List.Item
        title="Security"
        description="Manage passwords and security settings"
        titleStyle={{ fontWeight: '600', color: theme.colors.onSurface }}
        descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
        left={(props) => (
          <View style={[styles.settingIcon, { backgroundColor: theme.colors.tertiary + '15' }]}>
            <Icon name="shield-check" size={24} color={theme.colors.tertiary} />
          </View>
        )}
        right={() => <Icon name="chevron-right" size={20} color={theme.colors.onSurfaceVariant} />}
        onPress={() => navigation.navigate('SecuritySettings')}
        style={styles.listItem}
      />
    </Surface>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="My Profile"
        subtitle="Personal & business settings"
        showSearch={false}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => navigation.goBack()}
      />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <ProfileHeader />
        <BusinessSection />
        <ProfilePreferencesSection />
      </ScrollView>

      {/* Bottom Sheets and Modals */}
      <EditProfileBottomSheet
        ref={editProfileBottomSheetRef}
        profile={state.settings}
        onSave={handleProfileSave}
        onClose={() => {}}
      />



      <PaymentMethodsModal
        visible={paymentMethodsModalVisible}
        onDismiss={() => setPaymentMethodsModalVisible(false)}
        onSave={handlePaymentMethodsSave}
        paymentMethods={state.settings.paymentMethods}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  profileCard: {
    marginBottom: SPACING.lg,
    borderRadius: SHAPE.corner.xl,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  profileContent: {
    padding: SPACING.lg,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: SPACING.md,
  },
  profileMain: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    padding: SPACING.xs,
    borderRadius: SHAPE.corner.lg,
    marginRight: SPACING.md,
    position: 'relative',
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  profileInfo: {
    flex: 1,
  },
  contactInfo: {
    gap: SPACING.xs,
  },
  contactRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: SPACING.md,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  editButton: {
    minWidth: 80,
  },
  sectionTitle: {
    fontWeight: '700',
    marginBottom: SPACING.md,
  },
  sectionCard: {
    marginBottom: SPACING.lg,
    borderRadius: SHAPE.corner.xl,
    padding: SPACING.md,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  settingIcon: {
    width: 32,
    height: 32,
    borderRadius: SHAPE.corner.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.sm,
  },
  listItem: {
    paddingVertical: SPACING.xs,
  },

});

export default MyProfileScreen;
