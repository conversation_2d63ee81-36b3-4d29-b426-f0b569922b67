import React, { useState, useMemo, useCallback } from 'react';
import { ScrollView, View, StyleSheet, TouchableOpacity, RefreshControl } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  Surface,
  Text,
  Button,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import { useFinancial } from '../context/FinancialContext';

import CommonHeader from '../components/CommonHeader';
import navigationService from '../services/NavigationService';
import UnifiedInfoCard from '../components/UnifiedInfoCard';
import StatCardGroup from '../components/StatCardGroup';
import MD3ExpressiveButton from '../components/MD3ExpressiveButton';
import {
  SPACING,
  SHAPE,
  COMPONENT_SIZES,
  TYPOGRAPHY,
  ELEVATION,
  getTypographyStyle,
  getElevationStyle,
  getBorderColor
} from '../theme/designTokens';

const DashboardScreen = ({ navigation, navigateToTab }) => {
  const { theme } = useTheme();
  const { orders, products, customers } = useData();
  const { profitLossData, derivedData } = useFinancial();
  const insets = useSafeAreaInsets();

  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Memoize today's date to avoid recalculating
  const today = useMemo(() => new Date().toISOString().split('T')[0], []);

  // Simplified refresh functionality
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // Simple refresh simulation
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log('Dashboard refreshed');
    } catch (error) {
      console.log('Failed to refresh dashboard:', error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  // Simplified stats calculation with safety checks
  const dashboardStats = useMemo(() => {
    const safeOrders = orders || [];
    const safeProducts = products || [];
    const safeCustomers = customers || [];

    const todaysSales = safeOrders
      .filter(order => order.date === today && order.status === 'Completed')
      .reduce((sum, order) => sum + (order.total || 0), 0);

    return {
      todaysSales,
      totalOrders: safeOrders.length,
      totalProducts: safeProducts.length,
      totalCustomers: safeCustomers.length,
    };
  }, [orders, products, customers, today]);

  const dashboardCards = useMemo(() => [
    {
      key: 'sales',
      title: 'Today\'s Sales',
      value: `৳${dashboardStats.todaysSales.toFixed(0)}`,
      icon: 'cash-multiple',
      iconColor: theme.colors.primary,
      onPress: () => handleStatCardPress('sales')
    },
    {
      key: 'orders',
      title: 'Total Orders',
      value: dashboardStats.totalOrders.toString(),
      icon: 'clipboard-list-outline',
      iconColor: theme.colors.secondary,
      onPress: () => handleStatCardPress('orders')
    },
    {
      key: 'products',
      title: 'Total Products',
      value: dashboardStats.totalProducts.toString(),
      icon: 'food-variant',
      iconColor: theme.colors.tertiary,
      onPress: () => handleStatCardPress('products')
    },
    {
      key: 'customers',
      title: 'Total Customers',
      value: dashboardStats.totalCustomers.toString(),
      icon: 'account-group-outline',
      iconColor: theme.colors.primary,
      onPress: () => handleStatCardPress('customers')
    },
    {
      key: 'profit',
      title: 'Net Profit',
      value: profitLossData ? `৳${profitLossData.profit.net.toFixed(0)}` : '৳0',
      icon: 'chart-line',
      iconColor: profitLossData?.profit.net >= 0 ? "#10B981" : "#EF4444",
      onPress: () => handleStatCardPress('financial')
    },
    {
      key: 'expenses',
      title: 'Total Expenses',
      value: `৳${derivedData?.totalExpenses?.toFixed(0) || '0'}`,
      icon: 'receipt',
      iconColor: "#F59E0B",
      onPress: () => handleStatCardPress('financial')
    },
    {
      key: 'reports',
      title: 'View Reports',
      value: 'Advanced Analytics',
      icon: 'file-chart',
      iconColor: "#8B5CF6",
      onPress: () => handleStatCardPress('reports')
    },
  ], [dashboardStats, theme.colors, profitLossData, derivedData, handleStatCardPress]);

  // Simplified recent orders with safety checks
  const recentOrders = useMemo(() => {
    const safeOrders = orders || [];
    return safeOrders
      .sort((a, b) => new Date(b.createdAt || 0) - new Date(a.createdAt || 0))
      .slice(0, 2);
  }, [orders]);

  // Enhanced navigation handlers with proper data views
  const handleStatCardPress = useCallback((type) => {
    switch (type) {
      case 'sales':
        console.log('Fast navigation to Sales Reports');
        try {
          // Navigate to Reports screen
          navigationService.navigate('Reports');
        } catch (error) {
          console.error('Failed to navigate to Reports:', error);
        }
        break;
      case 'orders':
        console.log('Fast navigation to Orders');
        navigateToTab('Orders');
        break;
      case 'products':
        console.log('Fast navigation to Products');
        navigateToTab('Products');
        break;
      case 'customers':
        console.log('Fast navigation to Customers');
        try {
          // Navigate to dedicated Customers screen
          navigationService.navigate('Customers');
        } catch (error) {
          console.error('Failed to navigate to Customers:', error);
        }
        break;
      case 'financial':
        console.log('Fast navigation to Financial');
        navigateToTab('Financial');
        break;
      case 'reports':
        console.log('Navigating to Advanced Reports...');
        try {
          navigationService.navigate('Reports');
        } catch (error) {
          console.error('Failed to navigate to Reports:', error);
        }
        break;
    }
  }, [navigateToTab]);

  const handleOrderPress = useCallback((order) => {
    console.log('Order pressed:', order.id);
    navigateToTab('Orders');
  }, [navigateToTab]);







  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Dashboard"
        subtitle="Welcome back!"
        searchPlaceholder="Search orders, products, customers..."
        searchType="global"
        searchData={[...orders, ...products, ...customers]}
        searchFields={["name", "customerName", "customer", "title", "description"]}
        onSearchChange={setSearchQuery}
        onSearchResult={(item) => {
          console.log('Search result selected:', item);
          // Handle navigation based on item type
          if (item.customerName || item.customer) {
            // It's an order
            console.log('Navigate to order details');
          } else if (item.price) {
            // It's a product
            console.log('Navigate to product details');
          } else {
            // It's a customer
            console.log('Navigate to customer details');
          }
        }}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => {
          console.log('Profile pressed - navigating to MyProfile');
          try {
            navigationService.navigate('MyProfile');
          } catch (error) {
            console.error('Failed to navigate to MyProfile:', error);
          }
        }}
      />

      <ScrollView
        style={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        <View style={styles.content}>
          <StatCardGroup
            cards={dashboardCards}
            columns={2}
            showTitle={false}
          />

          {/* MD3 Expressive Demo Button */}
          <View style={styles.demoSection}>
            <MD3ExpressiveButton
              variant="tonal"
              icon="palette"
              expressive={true}
              size="large"
              fullWidth
              onPress={() => navigation.navigate('MD3ExpressiveDemo')}
            >
              Explore MD3 Expressive Design
            </MD3ExpressiveButton>
          </View>

        <View style={styles.ordersSection}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, {
              color: theme.colors.onBackground,
              fontSize: TYPOGRAPHY.title.medium.fontSize,
              fontWeight: TYPOGRAPHY.title.medium.fontWeight,
              lineHeight: TYPOGRAPHY.title.medium.lineHeight
            }]}>
              Recent Orders
            </Text>
            <Button
              mode="text"
              onPress={() => navigateToTab('Orders')}
              textColor={theme.colors.primary}
              compact
            >
              View All
            </Button>
          </View>
          <View style={styles.ordersList}>
            {recentOrders.map((order, index) => (
              <UnifiedInfoCard
                key={order.id}
                type="order"
                data={order}
                status={order.status}
                statusColor={
                  order.status === 'Completed' ? theme.colors.tertiary :
                  order.status === 'In Progress' ? theme.colors.secondary :
                  order.status === 'Pending' ? theme.colors.primary :
                  theme.colors.error
                }
                description={order.items?.map(item => `${item.name} x${item.quantity}`).join(', ') || ''}
                onPress={() => handleOrderPress(order)}
              />
            ))}
          </View>
        </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
  },
  content: {
    padding: SPACING.lg,
  },
  demoSection: {
    marginVertical: SPACING.lg,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: SPACING.md,
    marginHorizontal: -SPACING.xs / 2,
  },
  statCardWrapper: {
    width: '50%',
    paddingHorizontal: SPACING.xs / 2,
    marginBottom: SPACING.xs,
  },

  sectionTitle: {
    fontWeight: '700',
    marginBottom: SPACING.sm,
  },
  financialSection: {
    marginBottom: SPACING.lg,
  },
  financialCardsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.md,
  },
  financialCardWrapper: {
    width: '48%',
  },
  financialIconContainer: {
    width: COMPONENT_SIZES.icon.xxxl,
    height: COMPONENT_SIZES.icon.xxxl,
    borderRadius: SHAPE.corner.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.sm + 2,
  },
  reportsCardWrapper: {
    width: '100%',
  },
  reportsCard: {
    borderRadius: SHAPE.corner.xl,
    padding: SPACING.lg,
    borderWidth: 1,
  },
  reportsCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reportsCardText: {
    flex: 1,
    marginLeft: 4,
  },
  ordersSection: {
    marginBottom: SPACING.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  ordersList: {
    gap: SPACING.sm,
  },

});

export default DashboardScreen;
