/**
 * EmployeesScreen - Employee Management for Tailor Shop
 * Manages staff, assignments, performance, and scheduling
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Alert,
  TouchableOpacity,
} from 'react-native';
import {
  Text,
  Card,
  Avatar,
  Chip,
  Button,
  useTheme,
  Searchbar,
  FAB,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import CommonHeader from '../components/CommonHeader';
import UnifiedFilterChips from '../components/UnifiedFilterChips';
import StatCardGroup from '../components/StatCardGroup';
import UnifiedBottomSheet from '../components/UnifiedBottomSheet';
import { useData } from '../context/DataContext';
import { SPACING, SHAPE, TYPOGRAPHY, getBorderColor } from '../theme/designTokens';
import { BUSINESS_RULES } from '../config/constants';

const EmployeesScreen = ({ navigation }) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const { employees, loading, refreshData } = useData();

  // State management
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [refreshing, setRefreshing] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [bottomSheetVisible, setBottomSheetVisible] = useState(false);

  // Filter options
  const filterOptions = ['All', ...BUSINESS_RULES.EMPLOYEE_ROLES];

  // Filtered employees
  const filteredEmployees = employees?.filter(employee => {
    const matchesSearch = employee.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         employee.role.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = selectedFilter === 'All' || employee.role === selectedFilter;
    return matchesSearch && matchesFilter && employee.isActive;
  }) || [];

  // Statistics
  const employeeStats = [
    {
      id: 'total',
      title: 'Total Staff',
      value: employees?.filter(emp => emp.isActive).length || 0,
      icon: 'account-group',
      color: theme.colors.primary,
    },
    {
      id: 'active',
      title: 'Active Today',
      value: employees?.filter(emp => emp.isActive && emp.activeOrders?.length > 0).length || 0,
      icon: 'account-check',
      color: theme.colors.secondary,
    },
    {
      id: 'performance',
      title: 'Avg Performance',
      value: `${Math.round(employees?.reduce((acc, emp) => acc + (emp.performance?.qualityScore || 0), 0) / (employees?.length || 1))}%`,
      icon: 'chart-line',
      color: theme.colors.tertiary,
    },
  ];

  // Refresh handler
  const handleRefresh = async () => {
    setRefreshing(true);
    await refreshData();
    setRefreshing(false);
  };

  // Employee card press handler
  const handleEmployeePress = (employee) => {
    setSelectedEmployee(employee);
    setBottomSheetVisible(true);
  };

  // Get role color
  const getRoleColor = (role) => {
    const roleColors = {
      'Shop Manager': theme.colors.primary,
      'Senior Tailor': theme.colors.secondary,
      'Junior Tailor': theme.colors.tertiary,
      'Cutter': theme.colors.error,
      'Helper': theme.colors.outline,
      'Fitting Specialist': theme.colors.primary,
      'Quality Controller': theme.colors.secondary,
      'Sales Associate': theme.colors.tertiary,
      'Delivery Person': theme.colors.error,
    };
    return roleColors[role] || theme.colors.outline;
  };

  // Render employee card
  const renderEmployeeCard = (employee) => (
    <Card
      key={employee.id}
      style={[styles.employeeCard, { borderColor: getBorderColor(theme) }]}
      onPress={() => handleEmployeePress(employee)}
    >
      <Card.Content style={styles.cardContent}>
        <View style={styles.employeeHeader}>
          <Avatar.Text
            size={48}
            label={employee.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            style={{ backgroundColor: getRoleColor(employee.role) }}
          />
          <View style={styles.employeeInfo}>
            <Text style={[styles.employeeName, { color: theme.colors.onSurface }]}>
              {employee.name}
            </Text>
            <Text style={[styles.employeeRole, { color: theme.colors.onSurfaceVariant }]}>
              {employee.role}
            </Text>
            <View style={styles.skillsContainer}>
              {employee.skills?.slice(0, 2).map((skill, index) => (
                <Chip
                  key={index}
                  mode="outlined"
                  compact
                  style={styles.skillChip}
                  textStyle={styles.skillChipText}
                >
                  {skill}
                </Chip>
              ))}
              {employee.skills?.length > 2 && (
                <Text style={[styles.moreSkills, { color: theme.colors.onSurfaceVariant }]}>
                  +{employee.skills.length - 2} more
                </Text>
              )}
            </View>
          </View>
        </View>

        <View style={styles.employeeStats}>
          <View style={styles.statItem}>
            <Icon name="clipboard-check" size={16} color={theme.colors.primary} />
            <Text style={[styles.statValue, { color: theme.colors.onSurface }]}>
              {employee.activeOrders?.length || 0}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.onSurfaceVariant }]}>
              Active
            </Text>
          </View>
          <View style={styles.statItem}>
            <Icon name="star" size={16} color={theme.colors.secondary} />
            <Text style={[styles.statValue, { color: theme.colors.onSurface }]}>
              {employee.performance?.averageRating?.toFixed(1) || '0.0'}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.onSurfaceVariant }]}>
              Rating
            </Text>
          </View>
          <View style={styles.statItem}>
            <Icon name="clock-check" size={16} color={theme.colors.tertiary} />
            <Text style={[styles.statValue, { color: theme.colors.onSurface }]}>
              {employee.performance?.onTimeDelivery || 0}%
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.onSurfaceVariant }]}>
              On Time
            </Text>
          </View>
        </View>
      </Card.Content>
    </Card>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Staff Management"
        subtitle="Manage your team"
        showSearch={false}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => {
          console.log('Profile pressed - navigating to MyProfile');
          try {
            navigation.navigate('MyProfile');
          } catch (error) {
            console.error('Failed to navigate to MyProfile:', error);
          }
        }}
      />

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Statistics */}
        <StatCardGroup cards={employeeStats} />

        {/* Search */}
        <View style={styles.searchContainer}>
          <Searchbar
            placeholder="Search employees..."
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={[styles.searchBar, { backgroundColor: theme.colors.surface }]}
          />
        </View>

        {/* Filters */}
        <UnifiedFilterChips
          filters={filterOptions}
          selectedFilter={selectedFilter}
          onFilterChange={setSelectedFilter}
        />

        {/* Employee List */}
        <View style={styles.employeeList}>
          {filteredEmployees.map(renderEmployeeCard)}
        </View>

        {/* Empty State */}
        {filteredEmployees.length === 0 && (
          <View style={styles.emptyState}>
            <Icon name="account-off" size={64} color={theme.colors.onSurfaceVariant} />
            <Text style={[styles.emptyText, { color: theme.colors.onSurfaceVariant }]}>
              No employees found
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Add Employee FAB */}
      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={() => navigation.navigate('AddEmployee')}
      />

      {/* Employee Details Bottom Sheet */}
      <UnifiedBottomSheet
        ref={null}
        title={selectedEmployee?.name}
        subtitle={selectedEmployee?.role}
        icon="account"
        iconColor={theme.colors.primary}
        snapPoints={['50%', '90%']}
        onClose={() => setBottomSheetVisible(false)}
      >
        {selectedEmployee && (
          <View style={styles.bottomSheetContent}>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Contact Information
            </Text>
            <Text style={[styles.contactInfo, { color: theme.colors.onSurfaceVariant }]}>
              Phone: {selectedEmployee.contactInfo?.phone}
            </Text>
            {selectedEmployee.contactInfo?.email && (
              <Text style={[styles.contactInfo, { color: theme.colors.onSurfaceVariant }]}>
                Email: {selectedEmployee.contactInfo.email}
              </Text>
            )}

            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Skills
            </Text>
            <View style={styles.skillsGrid}>
              {selectedEmployee.skills?.map((skill, index) => (
                <Chip key={index} mode="outlined" style={styles.skillChip}>
                  {skill}
                </Chip>
              ))}
            </View>

            <View style={styles.actionButtons}>
              <Button
                mode="outlined"
                onPress={() => {
                  setBottomSheetVisible(false);
                  navigation.navigate('EditEmployee', { employee: selectedEmployee });
                }}
                style={styles.actionButton}
              >
                Edit Details
              </Button>
              <Button
                mode="contained"
                onPress={() => {
                  setBottomSheetVisible(false);
                  navigation.navigate('EmployeeOrders', { employeeId: selectedEmployee.id });
                }}
                style={styles.actionButton}
              >
                View Orders
              </Button>
            </View>
          </View>
        )}
      </UnifiedBottomSheet>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.md,
  },
  searchContainer: {
    marginVertical: SPACING.md,
  },
  searchBar: {
    borderRadius: SHAPE.corner.lg,
  },
  employeeList: {
    paddingBottom: SPACING.xl,
  },
  employeeCard: {
    marginBottom: SPACING.md,
    borderRadius: SHAPE.corner.xl,
    borderWidth: 1,
    overflow: 'hidden',
  },
  cardContent: {
    padding: SPACING.lg,
  },
  employeeHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: SPACING.md,
  },
  employeeInfo: {
    flex: 1,
    marginLeft: SPACING.md,
  },
  employeeName: {
    fontSize: TYPOGRAPHY.title.medium.fontSize,
    fontWeight: TYPOGRAPHY.title.medium.fontWeight,
    lineHeight: TYPOGRAPHY.title.medium.lineHeight,
  },
  employeeRole: {
    fontSize: TYPOGRAPHY.body.medium.fontSize,
    marginBottom: SPACING.xs,
  },
  skillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  skillChip: {
    marginRight: SPACING.xs,
    marginBottom: SPACING.xs,
  },
  skillChipText: {
    fontSize: 10,
  },
  moreSkills: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  employeeStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: TYPOGRAPHY.title.small.fontSize,
    fontWeight: 'bold',
    marginTop: SPACING.xs,
  },
  statLabel: {
    fontSize: 10,
    marginTop: 2,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xxl,
  },
  emptyText: {
    fontSize: TYPOGRAPHY.body.large.fontSize,
    marginTop: SPACING.md,
  },
  fab: {
    position: 'absolute',
    margin: SPACING.lg,
    right: 0,
    bottom: 0,
  },
  bottomSheetContent: {
    padding: SPACING.lg,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.title.medium.fontSize,
    fontWeight: TYPOGRAPHY.title.medium.fontWeight,
    marginTop: SPACING.lg,
    marginBottom: SPACING.md,
  },
  contactInfo: {
    fontSize: TYPOGRAPHY.body.medium.fontSize,
    marginBottom: SPACING.xs,
  },
  skillsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: SPACING.lg,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.lg,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: SPACING.xs,
  },
});

export default EmployeesScreen;
