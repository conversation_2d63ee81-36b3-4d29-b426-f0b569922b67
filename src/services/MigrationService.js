/**
 * Migration Service - Seamless transition from AsyncStorage to SQLite
 * Handles data migration and fallback strategies
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import SQLiteService from './SQLiteService';

class MigrationService {
  constructor() {
    this.migrationCompleted = false;
    this.useSQLite = false;
  }

  async initialize() {
    try {
      // Check if migration has been completed before
      const migrationStatus = await AsyncStorage.getItem('sqlite_migration_completed');

      if (migrationStatus === 'true') {
        // Migration already completed, use SQLite
        await SQLiteService.initialize();
        this.useSQLite = true;
        this.migrationCompleted = true;
        console.log('Using SQLite database (migration previously completed)');

        // Generate sample data if needed
        await this.generateSampleDataIfNeeded();
        return;
      }

      // Try to initialize SQLite
      await SQLiteService.initialize();

      // Migrate existing data from AsyncStorage
      await this.migrateData();

      // Mark migration as completed
      await AsyncStorage.setItem('sqlite_migration_completed', 'true');

      this.useSQLite = true;
      this.migrationCompleted = true;
      console.log('SQLite migration completed successfully');

      // Generate sample data if needed
      await this.generateSampleDataIfNeeded();

    } catch (error) {
      console.warn('SQLite initialization failed, falling back to AsyncStorage:', error);
      // Fallback to AsyncStorage if SQLite fails
      this.useSQLite = false;
      this.migrationCompleted = false;
    }
  }

  async migrateData() {
    console.log('Starting data migration from AsyncStorage to SQLite...');

    try {
      // Get all existing data from AsyncStorage
      const [products, orders, customers, expenses, reconciliations] = await Promise.all([
        this.getAsyncStorageData('bakeryData'),
        this.getAsyncStorageData('orders'),
        this.getAsyncStorageData('customers'),
        this.getAsyncStorageData('financial_expenses'),
        this.getAsyncStorageData('cash_reconciliations')
      ]);

      const migrationData = {
        products: products?.products || [],
        orders: orders || [],
        customers: customers || [],
        expenses: expenses || [],
        reconciliations: reconciliations || []
      };

      // Only migrate if there's actual data
      const hasData = migrationData.products.length > 0 ||
                     migrationData.orders.length > 0 ||
                     migrationData.customers.length > 0;

      if (hasData) {
        await SQLiteService.migrateFromAsyncStorage(migrationData);
        console.log('Data migration completed:', {
          products: migrationData.products.length,
          orders: migrationData.orders.length,
          customers: migrationData.customers.length
        });
      } else {
        console.log('No existing data found to migrate');
      }

    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  }

  async getAsyncStorageData(key) {
    try {
      const data = await AsyncStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.warn(`Failed to get AsyncStorage data for key ${key}:`, error);
      return null;
    }
  }

  // Unified data access methods that automatically choose the right storage
  async getProducts(filters = {}) {
    if (this.useSQLite) {
      return await SQLiteService.getProducts(filters);
    } else {
      // Fallback to AsyncStorage
      const data = await this.getAsyncStorageData('bakeryData');
      let products = data?.products || [];

      // Apply filters
      if (filters.isActive !== undefined) {
        products = products.filter(p => p.isActive === filters.isActive);
      }
      if (filters.category) {
        products = products.filter(p => p.category === filters.category);
      }
      if (filters.search) {
        const search = filters.search.toLowerCase();
        products = products.filter(p =>
          p.name.toLowerCase().includes(search) ||
          p.description?.toLowerCase().includes(search) ||
          p.sku?.toLowerCase().includes(search)
        );
      }

      return products;
    }
  }

  async saveProduct(product) {
    if (this.useSQLite) {
      return await SQLiteService.saveProduct(product);
    } else {
      // Fallback to AsyncStorage
      const data = await this.getAsyncStorageData('bakeryData') || { products: [] };
      const products = data.products || [];

      if (product.id) {
        const index = products.findIndex(p => p.id === product.id);
        if (index >= 0) {
          products[index] = { ...product, updatedAt: new Date().toISOString() };
        }
      } else {
        const newProduct = {
          ...product,
          id: Date.now(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        products.push(newProduct);
        product = newProduct;
      }

      await AsyncStorage.setItem('bakeryData', JSON.stringify({ ...data, products }));
      return product;
    }
  }

  async getOrders(filters = {}) {
    if (this.useSQLite) {
      return await SQLiteService.getOrders(filters);
    } else {
      // Fallback to AsyncStorage
      let orders = await this.getAsyncStorageData('orders') || [];

      // Apply filters
      if (filters.status) {
        orders = orders.filter(o => o.status === filters.status);
      }
      if (filters.dateFrom) {
        orders = orders.filter(o => o.date >= filters.dateFrom);
      }
      if (filters.dateTo) {
        orders = orders.filter(o => o.date <= filters.dateTo);
      }
      if (filters.search) {
        const search = filters.search.toLowerCase();
        orders = orders.filter(o =>
          o.customerName?.toLowerCase().includes(search) ||
          o.id?.toLowerCase().includes(search)
        );
      }

      return orders;
    }
  }

  async saveOrder(order) {
    if (this.useSQLite) {
      return await SQLiteService.saveOrder(order);
    } else {
      // Fallback to AsyncStorage
      const orders = await this.getAsyncStorageData('orders') || [];

      if (order.id) {
        const index = orders.findIndex(o => o.id === order.id);
        if (index >= 0) {
          orders[index] = { ...order, updatedAt: new Date().toISOString() };
        } else {
          orders.push({ ...order, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() });
        }
      } else {
        const newOrder = {
          ...order,
          id: `ORD-${Date.now()}`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        orders.push(newOrder);
        order = newOrder;
      }

      await AsyncStorage.setItem('orders', JSON.stringify(orders));
      return order;
    }
  }

  async getCustomers() {
    if (this.useSQLite) {
      return await SQLiteService.getCustomers();
    } else {
      return await this.getAsyncStorageData('customers') || [];
    }
  }

  async deleteProduct(id) {
    if (this.useSQLite) {
      return await SQLiteService.deleteProduct(id);
    } else {
      const data = await this.getAsyncStorageData('bakeryData') || { products: [] };
      const products = data.products.filter(p => p.id !== id);
      await AsyncStorage.setItem('bakeryData', JSON.stringify({ ...data, products }));
    }
  }

  async deleteOrder(id) {
    if (this.useSQLite) {
      return await SQLiteService.deleteOrder(id);
    } else {
      const orders = await this.getAsyncStorageData('orders') || [];
      const filteredOrders = orders.filter(o => o.id !== id);
      await AsyncStorage.setItem('orders', JSON.stringify(filteredOrders));
    }
  }

  async deleteCustomer(id) {
    if (this.useSQLite) {
      return await SQLiteService.deleteCustomer(id);
    } else {
      const customers = await this.getAsyncStorageData('customers') || [];
      const filteredCustomers = customers.filter(c => c.id !== id);
      await AsyncStorage.setItem('customers', JSON.stringify(filteredCustomers));
    }
  }

  async saveCustomer(customer) {
    if (this.useSQLite) {
      return await SQLiteService.saveCustomer(customer);
    } else {
      const customers = await this.getAsyncStorageData('customers') || [];

      if (customer.id) {
        const index = customers.findIndex(c => c.id === customer.id);
        if (index >= 0) {
          customers[index] = { ...customer, updatedAt: new Date().toISOString() };
        }
      } else {
        const newCustomer = {
          ...customer,
          id: Date.now(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        customers.push(newCustomer);
        customer = newCustomer;
      }

      await AsyncStorage.setItem('customers', JSON.stringify(customers));
      return customer;
    }
  }

  // Performance analytics
  getPerformanceInfo() {
    return {
      usingDatabase: this.useSQLite ? 'SQLite' : 'AsyncStorage',
      migrationCompleted: this.migrationCompleted,
      optimizedForScale: this.useSQLite,
      recommendedMaxRecords: this.useSQLite ? 100000 : 5000,
      currentPerformanceLevel: this.useSQLite ? 'Excellent' : 'Good'
    };
  }

  // Health check
  async healthCheck() {
    try {
      if (this.useSQLite) {
        // Test SQLite connection
        await SQLiteService.db.getFirstAsync('SELECT 1 as test');
        return {
          status: 'healthy',
          database: 'SQLite',
          performance: 'Excellent',
          scalability: 'High'
        };
      } else {
        // Test AsyncStorage
        await AsyncStorage.getItem('test');
        return {
          status: 'healthy',
          database: 'AsyncStorage',
          performance: 'Good',
          scalability: 'Medium'
        };
      }
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        database: this.useSQLite ? 'SQLite' : 'AsyncStorage'
      };
    }
  }

  // Generate sample data if database is empty
  async generateSampleDataIfNeeded() {
    if (!this.useSQLite) return;

    try {
      // Check if we already have data
      const [products, customers] = await Promise.all([
        this.getProducts(),
        this.getCustomers()
      ]);

      // Only generate if we have less than 10 products and customers
      if (products.length < 10 && customers.length < 10) {
        console.log('Generating sample data...');
        await this.generateSampleData();
        console.log('Sample data generated successfully');
      }
    } catch (error) {
      console.warn('Failed to generate sample data:', error);
    }
  }

  // Generate 100 products and 100 customers
  async generateSampleData() {
    const startTime = Date.now();

    // Generate 100 products
    const products = this.generateSampleProducts(100);
    const customers = this.generateSampleCustomers(100);

    // Save products in batches for better performance
    console.log('Saving 100 products...');
    for (const product of products) {
      await this.saveProduct(product);
    }

    // Save customers in batches for better performance
    console.log('Saving 100 customers...');
    for (const customer of customers) {
      await this.saveCustomer(customer);
    }

    const endTime = Date.now();
    console.log(`Generated 100 products and 100 customers in ${endTime - startTime}ms`);
  }

  // Generate sample products
  generateSampleProducts(count) {
    const categories = ['Bread', 'Pastries', 'Cakes', 'Cookies', 'Beverages', 'Sandwiches', 'Desserts'];
    const productNames = {
      'Bread': ['Sourdough Bread', 'Whole Wheat Bread', 'French Baguette', 'Rye Bread', 'Ciabatta', 'Focaccia', 'Pita Bread'],
      'Pastries': ['Croissant', 'Danish Pastry', 'Éclair', 'Profiterole', 'Palmier', 'Pain au Chocolat', 'Turnover'],
      'Cakes': ['Chocolate Cake', 'Vanilla Cake', 'Red Velvet Cake', 'Carrot Cake', 'Cheesecake', 'Tiramisu', 'Black Forest'],
      'Cookies': ['Chocolate Chip', 'Oatmeal Raisin', 'Sugar Cookie', 'Snickerdoodle', 'Macaron', 'Biscotti', 'Shortbread'],
      'Beverages': ['Coffee', 'Tea', 'Hot Chocolate', 'Latte', 'Cappuccino', 'Espresso', 'Smoothie'],
      'Sandwiches': ['Club Sandwich', 'BLT', 'Grilled Cheese', 'Turkey Club', 'Ham & Swiss', 'Veggie Wrap', 'Panini'],
      'Desserts': ['Apple Pie', 'Brownie', 'Muffin', 'Donut', 'Cupcake', 'Tart', 'Pudding']
    };

    const products = [];
    const now = new Date().toISOString();

    for (let i = 1; i <= count; i++) {
      const category = categories[Math.floor(Math.random() * categories.length)];
      const names = productNames[category];
      const baseName = names[Math.floor(Math.random() * names.length)];
      const variation = Math.random() > 0.7 ? ` ${['Premium', 'Deluxe', 'Special', 'Classic'][Math.floor(Math.random() * 4)]}` : '';

      // Ensure unique names by adding index
      const uniqueName = `${baseName}${variation} #${i}`;

      products.push({
        name: uniqueName,
        description: `Delicious ${baseName.toLowerCase()} made fresh daily with premium ingredients`,
        price: Math.round((Math.random() * 50 + 5) * 100) / 100, // $5-$55
        cost: Math.round((Math.random() * 25 + 2) * 100) / 100, // $2-$27
        stock: Math.floor(Math.random() * 100) + 10, // 10-110
        category: category,
        sku: `SKU${String(i).padStart(3, '0')}`,
        barcode: `${Math.floor(Math.random() * 9000000000000) + 1000000000000}`,
        isActive: Math.random() > 0.1, // 90% active
        createdAt: now,
        updatedAt: now
      });
    }

    return products;
  }

  // Generate sample customers
  generateSampleCustomers(count) {
    const firstNames = ['John', 'Jane', 'Michael', 'Sarah', 'David', 'Emily', 'Robert', 'Lisa', 'William', 'Jennifer', 'James', 'Mary', 'Christopher', 'Patricia', 'Daniel', 'Linda', 'Matthew', 'Elizabeth', 'Anthony', 'Barbara', 'Mark', 'Susan', 'Donald', 'Jessica', 'Steven', 'Karen', 'Paul', 'Nancy', 'Andrew', 'Betty', 'Joshua', 'Helen', 'Kenneth', 'Sandra', 'Kevin', 'Donna', 'Brian', 'Carol', 'George', 'Ruth', 'Timothy', 'Sharon', 'Ronald', 'Michelle', 'Jason', 'Laura', 'Edward', 'Sarah', 'Jeffrey', 'Kimberly'];
    const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin', 'Lee', 'Perez', 'Thompson', 'White', 'Harris', 'Sanchez', 'Clark', 'Ramirez', 'Lewis', 'Robinson', 'Walker', 'Young', 'Allen', 'King', 'Wright', 'Scott', 'Torres', 'Nguyen', 'Hill', 'Flores', 'Green', 'Adams', 'Nelson', 'Baker', 'Hall', 'Rivera', 'Campbell', 'Mitchell', 'Carter', 'Roberts'];

    const customers = [];
    const now = new Date().toISOString();

    for (let i = 1; i <= count; i++) {
      const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
      const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];

      customers.push({
        name: `${firstName} ${lastName}`,
        email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@email.com`,
        phone: `+1${Math.floor(Math.random() * 9000000000) + 1000000000}`,
        address: `${Math.floor(Math.random() * 9999) + 1} ${['Main St', 'Oak Ave', 'Pine Rd', 'Elm Dr', 'Maple Ln'][Math.floor(Math.random() * 5)]}`,
        city: ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 'San Diego', 'Dallas', 'San Jose'][Math.floor(Math.random() * 10)],
        totalOrders: Math.floor(Math.random() * 50),
        totalSpent: Math.round((Math.random() * 2000 + 100) * 100) / 100, // $100-$2100
        loyaltyPoints: Math.floor(Math.random() * 1000),
        createdAt: now,
        updatedAt: now
      });
    }

    return customers;
  }

  // Force regenerate sample data (for development/testing)
  async forceRegenerateSampleData() {
    if (!this.useSQLite) {
      console.log('SQLite not available, cannot generate sample data');
      return;
    }

    try {
      console.log('Force regenerating sample data...');

      // Clear existing data
      await SQLiteService.clearAllData();

      // Generate new sample data
      await this.generateSampleData();

      console.log('Sample data regenerated successfully');
    } catch (error) {
      console.error('Failed to regenerate sample data:', error);
    }
  }
}

export default new MigrationService();
