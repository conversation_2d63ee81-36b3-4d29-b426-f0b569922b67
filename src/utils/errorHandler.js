import React, { useCallback } from 'react';
import { Alert } from 'react-native';
import { ERROR_MESSAGES } from '../config/constants';

/**
 * Error types for better categorization
 */
export const ErrorTypes = {
  NETWORK: 'NETWORK',
  VALIDATION: 'VALIDATION',
  STORAGE: 'STORAGE',
  PERMISSION: 'PERMISSION',
  BUSINESS_LOGIC: 'BUSINESS_LOGIC',
  UNKNOWN: 'UNKNOWN',
};

/**
 * Logger utility for development and production
 */
export class Logger {
  static isDevelopment = __DEV__;

  static log(level, message, data = null) {
    if (this.isDevelopment) {
      const timestamp = new Date().toISOString();
      const logMessage = `[${timestamp}] [${level}] ${message}`;

      switch (level) {
        case 'ERROR':
          console.error(logMessage, data);
          break;
        case 'WARN':
          console.warn(logMessage, data);
          break;
        case 'INFO':
          console.info(logMessage, data);
          break;
        default:
          console.log(logMessage, data);
      }
    }

    // In production, you might want to send logs to a service
    // this.sendToLoggingService(level, message, data);
  }

  static error(message, data = null) {
    this.log('ERROR', message, data);
  }

  static warn(message, data = null) {
    this.log('WARN', message, data);
  }

  static info(message, data = null) {
    this.log('INFO', message, data);
  }

  static debug(message, data = null) {
    this.log('DEBUG', message, data);
  }
}

/**
 * Custom Error classes for better error handling
 */
export class AppError extends Error {
  constructor(message, type = ErrorTypes.UNKNOWN, originalError = null) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.originalError = originalError;
    this.timestamp = new Date().toISOString();
  }
}

export class NetworkError extends AppError {
  constructor(message, originalError = null) {
    super(message, ErrorTypes.NETWORK, originalError);
    this.name = 'NetworkError';
  }
}

export class ValidationError extends AppError {
  constructor(message, field = null, originalError = null) {
    super(message, ErrorTypes.VALIDATION, originalError);
    this.name = 'ValidationError';
    this.field = field;
  }
}

export class StorageError extends AppError {
  constructor(message, originalError = null) {
    super(message, ErrorTypes.STORAGE, originalError);
    this.name = 'StorageError';
  }
}

/**
 * Global error handler
 */
export class ErrorHandler {
  static handle(error, context = 'Unknown') {
    Logger.error(`Error in ${context}:`, {
      message: error.message,
      stack: error.stack,
      type: error.type || ErrorTypes.UNKNOWN,
    });

    // Determine user-friendly message
    let userMessage = ERROR_MESSAGES.GENERIC_ERROR;

    if (error instanceof AppError) {
      switch (error.type) {
        case ErrorTypes.NETWORK:
          userMessage = ERROR_MESSAGES.NETWORK_ERROR;
          break;
        case ErrorTypes.VALIDATION:
          userMessage = ERROR_MESSAGES.VALIDATION_ERROR;
          break;
        case ErrorTypes.STORAGE:
          userMessage = ERROR_MESSAGES.SAVE_ERROR;
          break;
        case ErrorTypes.PERMISSION:
          userMessage = ERROR_MESSAGES.PERMISSION_ERROR;
          break;
        default:
          userMessage = error.message || ERROR_MESSAGES.GENERIC_ERROR;
      }
    }

    return userMessage;
  }

  static showAlert(error, context = 'Error', onPress = null) {
    const message = this.handle(error, context);

    Alert.alert(
      'Error',
      message,
      [
        {
          text: 'OK',
          onPress: onPress || (() => {}),
        },
      ]
    );
  }

  static async handleAsync(asyncFunction, context = 'Async Operation') {
    try {
      return await asyncFunction();
    } catch (error) {
      this.handle(error, context);
      throw error;
    }
  }
}

/**
 * Error boundary hook for React components
 */
export const useErrorHandler = () => {
  const handleError = useCallback((error, context = 'Component') => {
    return ErrorHandler.handle(error, context);
  }, []);

  const showErrorAlert = useCallback((error, context = 'Error', onPress = null) => {
    ErrorHandler.showAlert(error, context, onPress);
  }, []);

  const handleAsyncError = useCallback(async (asyncFunction, context = 'Async Operation') => {
    return ErrorHandler.handleAsync(asyncFunction, context);
  }, []);

  return {
    handleError,
    showErrorAlert,
    handleAsyncError,
  };
};

/**
 * Network error handler
 */
export const handleNetworkError = (error) => {
  if (!error.response) {
    // Network error
    throw new NetworkError(ERROR_MESSAGES.NETWORK_ERROR, error);
  } else if (error.response.status >= 400 && error.response.status < 500) {
    // Client error
    throw new AppError(
      error.response.data?.message || 'Client error occurred',
      ErrorTypes.VALIDATION,
      error
    );
  } else if (error.response.status >= 500) {
    // Server error
    throw new AppError(
      'Server error occurred. Please try again later.',
      ErrorTypes.NETWORK,
      error
    );
  }

  throw new AppError(error.message || ERROR_MESSAGES.GENERIC_ERROR, ErrorTypes.UNKNOWN, error);
};
