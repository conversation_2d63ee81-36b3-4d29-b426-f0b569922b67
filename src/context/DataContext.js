import React, { createContext, useContext, useReducer, useEffect, useMemo, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import MigrationService from '../services/MigrationService';
import PerformanceOptimizer from '../services/PerformanceOptimizer';
import HealthMonitor from '../services/HealthMonitor';
import LoggingService from '../services/LoggingService';

// Initial state with completely empty arrays - no dummy data
const initialState = {
  products: [],
  orders: [],
  customers: [],
  settings: {
    storeName: 'My Business',
    ownerName: 'Business Owner',
    email: '<EMAIL>',
    phone: '+1 ************',
    address: 'Business Address',
    taxRate: 0.08,
    currency: 'BDT',
    notifications: true,
    darkMode: false,
    autoBackup: true,
    storeHours: {
      monday: { open: '09:00', close: '18:00', closed: false },
      tuesday: { open: '09:00', close: '18:00', closed: false },
      wednesday: { open: '09:00', close: '18:00', closed: false },
      thursday: { open: '09:00', close: '18:00', closed: false },
      friday: { open: '09:00', close: '18:00', closed: false },
      saturday: { open: '09:00', close: '17:00', closed: false },
      sunday: { open: '10:00', close: '16:00', closed: true },
    },
    paymentMethods: {
      cash: { enabled: true, processingFee: 0 },
      card: { enabled: true, processingFee: 2.9 },
      digitalWallet: { enabled: false, processingFee: 2.5 },
      bankTransfer: { enabled: false, processingFee: 1.0 },
      giftCard: { enabled: true, processingFee: 0 },
    },
  },
  nextProductId: 1,
  nextOrderId: 1,
  nextCustomerId: 1,
  isDataLoaded: false, // Track if data has been loaded from storage
};

// Action types
const actionTypes = {
  // Products
  ADD_PRODUCT: 'ADD_PRODUCT',
  UPDATE_PRODUCT: 'UPDATE_PRODUCT',
  DELETE_PRODUCT: 'DELETE_PRODUCT',
  UPDATE_STOCK: 'UPDATE_STOCK',

  // Orders
  ADD_ORDER: 'ADD_ORDER',
  UPDATE_ORDER: 'UPDATE_ORDER',
  DELETE_ORDER: 'DELETE_ORDER',
  UPDATE_ORDER_STATUS: 'UPDATE_ORDER_STATUS',

  // Customers
  ADD_CUSTOMER: 'ADD_CUSTOMER',
  UPDATE_CUSTOMER: 'UPDATE_CUSTOMER',
  DELETE_CUSTOMER: 'DELETE_CUSTOMER',

  // Settings
  UPDATE_SETTINGS: 'UPDATE_SETTINGS',

  // Data
  LOAD_DATA: 'LOAD_DATA',
  SET_DATA_LOADED: 'SET_DATA_LOADED',
  CLEAR_DATA: 'CLEAR_DATA',
};

// Reducer function
const dataReducer = (state, action) => {
  switch (action.type) {
    case actionTypes.ADD_PRODUCT:
      return {
        ...state,
        products: [...state.products, { ...action.payload, id: state.nextProductId }],
        nextProductId: state.nextProductId + 1,
      };

    case actionTypes.UPDATE_PRODUCT:
      return {
        ...state,
        products: state.products.map(product =>
          product.id === action.payload.id ? { ...product, ...action.payload } : product
        ),
      };

    case actionTypes.DELETE_PRODUCT:
      return {
        ...state,
        products: state.products.filter(product => product.id !== action.payload),
      };

    case actionTypes.UPDATE_STOCK:
      return {
        ...state,
        products: state.products.map(product =>
          product.id === action.payload.id
            ? { ...product, stock: Math.max(0, product.stock + action.payload.change) }
            : product
        ),
      };

    case actionTypes.ADD_ORDER:
      const newOrderId = String(state.nextOrderId).padStart(3, '0');
      return {
        ...state,
        orders: [...state.orders, { ...action.payload, id: newOrderId }],
        nextOrderId: state.nextOrderId + 1,
      };

    case actionTypes.UPDATE_ORDER:
      return {
        ...state,
        orders: state.orders.map(order =>
          order.id === action.payload.id ? { ...order, ...action.payload } : order
        ),
      };

    case actionTypes.DELETE_ORDER:
      return {
        ...state,
        orders: state.orders.filter(order => order.id !== action.payload),
      };

    case actionTypes.UPDATE_ORDER_STATUS:
      return {
        ...state,
        orders: state.orders.map(order =>
          order.id === action.payload.id ? { ...order, status: action.payload.status } : order
        ),
      };

    case actionTypes.ADD_CUSTOMER:
      return {
        ...state,
        customers: [...state.customers, { ...action.payload, id: state.nextCustomerId }],
        nextCustomerId: state.nextCustomerId + 1,
      };

    case actionTypes.UPDATE_CUSTOMER:
      return {
        ...state,
        customers: state.customers.map(customer =>
          customer.id === action.payload.id ? { ...customer, ...action.payload } : customer
        ),
      };

    case actionTypes.DELETE_CUSTOMER:
      return {
        ...state,
        customers: state.customers.filter(customer => customer.id !== action.payload),
      };

    case actionTypes.UPDATE_SETTINGS:
      return {
        ...state,
        settings: { ...state.settings, ...action.payload },
      };

    case actionTypes.LOAD_DATA:
      return {
        ...state,
        ...action.payload,
        isDataLoaded: true,
      };

    case actionTypes.SET_DATA_LOADED:
      return {
        ...state,
        isDataLoaded: action.payload,
      };

    case actionTypes.CLEAR_DATA:
      return {
        ...initialState,
        isDataLoaded: false,
      };

    default:
      return state;
  }
};

// Create context
const DataContext = createContext();

// Provider component with performance optimizations
export const DataProvider = ({ children }) => {
  const [state, dispatch] = useReducer(dataReducer, initialState);

  // Load data from AsyncStorage on app start
  useEffect(() => {
    loadData();
  }, []);

  // Debounced save to prevent excessive writes - reduced to 500ms for snappier feel
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      saveData();
    }, 500); // Save after 500ms of inactivity for better responsiveness

    return () => clearTimeout(timeoutId);
  }, [state.products, state.orders, state.customers, state.settings]); // Only watch specific state properties

  const loadData = useCallback(async () => {
    const startTime = Date.now();
    try {
      LoggingService.info('Loading data with enterprise optimization...', 'DATA_CONTEXT');
      PerformanceOptimizer.measureRenderTime('DataContext.loadData', 0);

      // Initialize MigrationService (handles SQLite setup and migration)
      await MigrationService.initialize();

      // Data will be loaded from SQLite or start empty

      // Load data using the optimized service
      const [products, orders, customers] = await Promise.all([
        MigrationService.getProducts(),
        MigrationService.getOrders(),
        MigrationService.getCustomers()
      ]);

      // Load settings from AsyncStorage (settings remain in AsyncStorage for simplicity)
      const savedSettings = await AsyncStorage.getItem('bakerySettings');
      const settings = savedSettings ?
        { ...initialState.settings, ...JSON.parse(savedSettings) } :
        initialState.settings;

      console.log(`Data loaded: ${products.length} products, ${orders.length} orders, ${customers.length} customers`);
      console.log('Performance info:', MigrationService.getPerformanceInfo());

      // Calculate next IDs
      const nextProductId = products.length > 0 ? Math.max(...products.map(p => p.id)) + 1 : 1;
      const nextOrderId = orders.length > 0 ? Math.max(...orders.map(o => parseInt(o.id) || 0)) + 1 : 1;
      const nextCustomerId = customers.length > 0 ? Math.max(...customers.map(c => c.id)) + 1 : 1;

      const loadedData = {
        products,
        orders,
        customers,
        settings,
        nextProductId,
        nextOrderId,
        nextCustomerId,
      };

      dispatch({ type: actionTypes.LOAD_DATA, payload: loadedData });

      // Track performance metrics
      const loadTime = Date.now() - startTime;
      PerformanceOptimizer.measureRenderTime('DataContext.loadData', loadTime);
      LoggingService.info('Data loading completed successfully', 'DATA_CONTEXT', {
        loadTime,
        productsCount: products.length,
        ordersCount: orders.length,
        customersCount: customers.length,
      });
    } catch (error) {
      LoggingService.error('Error loading data', 'DATA_CONTEXT', error);
      // Fallback to empty state on error
      dispatch({ type: actionTypes.SET_DATA_LOADED, payload: true });
    }
  }, []);

  const saveData = useCallback(async () => {
    // Only save if data has been loaded to prevent overwriting with empty state
    if (!state.isDataLoaded) {
      return;
    }

    try {
      // Save settings to AsyncStorage
      await AsyncStorage.setItem('bakerySettings', JSON.stringify(state.settings));

      // Note: Products, orders, and customers are automatically saved by MigrationService
      // when using the action creators below. This function now mainly handles settings.

      console.log(`Settings saved successfully`);
    } catch (error) {
      console.error('Error saving settings:', error);
      // Don't crash the app on save errors
    }
  }, [state.settings, state.isDataLoaded]);

  // Memoized action creators for performance with SQLite optimization
  const actions = useMemo(() => ({
    // Products - now with SQLite backend
    addProduct: async (product) => {
      const savedProduct = await MigrationService.saveProduct({
        ...product,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: actionTypes.ADD_PRODUCT, payload: savedProduct });
      return savedProduct;
    },

    updateProduct: async (product) => {
      const updatedProduct = await MigrationService.saveProduct({
        ...product,
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: actionTypes.UPDATE_PRODUCT, payload: updatedProduct });
      return updatedProduct;
    },

    deleteProduct: async (id) => {
      await MigrationService.deleteProduct(id);
      dispatch({ type: actionTypes.DELETE_PRODUCT, payload: id });
    },

    updateStock: async (id, change) => {
      // Get current product, update stock, then save
      const products = await MigrationService.getProducts();
      const product = products.find(p => p.id === id);
      if (product) {
        const updatedProduct = {
          ...product,
          stock: Math.max(0, product.stock + change),
          updatedAt: new Date().toISOString()
        };
        await MigrationService.saveProduct(updatedProduct);
        dispatch({ type: actionTypes.UPDATE_STOCK, payload: { id, change } });
      }
    },

    // Orders - now with SQLite backend
    addOrder: async (order) => {
      const orderWithId = {
        ...order,
        id: order.id || String(state.nextOrderId).padStart(3, '0'),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      const savedOrder = await MigrationService.saveOrder(orderWithId);
      dispatch({ type: actionTypes.ADD_ORDER, payload: savedOrder });
      return savedOrder;
    },

    updateOrder: async (order) => {
      const updatedOrder = await MigrationService.saveOrder({
        ...order,
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: actionTypes.UPDATE_ORDER, payload: updatedOrder });
      return updatedOrder;
    },

    deleteOrder: async (id) => {
      await MigrationService.deleteOrder(id);
      dispatch({ type: actionTypes.DELETE_ORDER, payload: id });
    },

    updateOrderStatus: async (id, status) => {
      const orders = await MigrationService.getOrders();
      const order = orders.find(o => o.id === id);
      if (order) {
        const updatedOrder = {
          ...order,
          status,
          updatedAt: new Date().toISOString()
        };
        await MigrationService.saveOrder(updatedOrder);
        dispatch({ type: actionTypes.UPDATE_ORDER_STATUS, payload: { id, status } });
      }
    },

    // Customers - now with SQLite backend
    addCustomer: async (customer) => {
      const savedCustomer = await MigrationService.saveCustomer({
        ...customer,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: actionTypes.ADD_CUSTOMER, payload: savedCustomer });
      return savedCustomer;
    },

    updateCustomer: async (customer) => {
      const updatedCustomer = await MigrationService.saveCustomer({
        ...customer,
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: actionTypes.UPDATE_CUSTOMER, payload: updatedCustomer });
      return updatedCustomer;
    },

    deleteCustomer: async (id) => {
      await MigrationService.deleteCustomer(id);
      dispatch({ type: actionTypes.DELETE_CUSTOMER, payload: id });
    },

    // Settings
    updateSettings: (settings) => dispatch({ type: actionTypes.UPDATE_SETTINGS, payload: settings }),

    // Data Management

    importData: (importedData) => {
      try {
        // Validate imported data
        const validatedData = {
          products: Array.isArray(importedData.products) ? importedData.products : [],
          orders: Array.isArray(importedData.orders) ? importedData.orders : [],
          customers: Array.isArray(importedData.customers) ? importedData.customers : [],
          settings: importedData.settings || state.settings,
        };

        // Update next IDs based on imported data
        const nextProductId = validatedData.products.length > 0 ? Math.max(...validatedData.products.map(p => p.id)) + 1 : 1;
        const nextOrderId = validatedData.orders.length > 0 ? Math.max(...validatedData.orders.map(o => parseInt(o.id))) + 1 : 1;
        const nextCustomerId = validatedData.customers.length > 0 ? Math.max(...validatedData.customers.map(c => c.id)) + 1 : 1;

        dispatch({
          type: actionTypes.LOAD_DATA,
          payload: {
            ...validatedData,
            nextProductId,
            nextOrderId,
            nextCustomerId,
          }
        });

        console.log(`Data imported successfully: ${validatedData.products.length} products, ${validatedData.orders.length} orders, ${validatedData.customers.length} customers`);
      } catch (error) {
        console.error('Error importing data:', error);
      }
    },

    exportData: () => {
      return {
        products: state.products,
        orders: state.orders,
        customers: state.customers,
        settings: state.settings,
        exportDate: new Date().toISOString(),
        version: '1.0.0',
      };
    },

    clearData: async () => {
      try {
        await AsyncStorage.removeItem('bakeryData');
        dispatch({ type: actionTypes.CLEAR_DATA });
        console.log('All data cleared successfully');
      } catch (error) {
        console.error('Error clearing data:', error);
      }
    },

    // Force save data immediately (for critical operations)
    forceSave: async () => {
      await saveData();
    },

    // Enterprise monitoring methods
    getPerformanceScore: () => {
      return PerformanceOptimizer.getScore();
    },



    getHealthStatus: () => {
      return HealthMonitor.getHealthStatus();
    },

    generateHealthReport: () => {
      return HealthMonitor.generateHealthReport();
    },

    getOverallAppScore: () => {
      const performanceScore = PerformanceOptimizer.getScore();
      const healthScore = HealthMonitor.getOverallScore();

      const overallScore = (performanceScore + healthScore) / 2;

      LoggingService.info('Overall app score calculated', 'ENTERPRISE', {
        performanceScore,
        healthScore,
        overallScore,
      });

      return {
        overallScore: Math.round(overallScore * 100) / 100,
        breakdown: {
          performance: performanceScore,
          health: healthScore,
        },
        grade: overallScore >= 95 ? 'A+' : overallScore >= 90 ? 'A' : overallScore >= 85 ? 'B+' : 'B',
        status: overallScore >= 95 ? 'Excellent' : overallScore >= 90 ? 'Very Good' : overallScore >= 85 ? 'Good' : 'Fair',
      };
    },

    // Generate sample data (100 products and 100 customers)
    generateSampleData: async () => {
      try {
        await MigrationService.forceRegenerateSampleData();
        // Reload data after generation
        await loadData();
        console.log('Sample data generated and reloaded successfully');
      } catch (error) {
        console.error('Failed to generate sample data:', error);
      }
    },

  }), [dispatch, state.products, state.orders, state.customers, state.settings, saveData, loadData]);

  return (
    <DataContext.Provider value={{ state, actions }}>
      {children}
    </DataContext.Provider>
  );
};

// Custom hook to use the context
export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export default DataContext;
