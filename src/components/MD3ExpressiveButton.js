/**
 * MD3 Expressive Button Component
 * Enhanced Material Design 3 button with expressive styling and animations
 */

import React from 'react';
import { TouchableOpacity, View, StyleSheet } from 'react-native';
import { Text, useTheme, ActivityIndicator } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { 
  SPACING, 
  SHAPE, 
  TYPOGRAPHY, 
  COMPONENT_SIZES, 
  MOTION, 
  STATE,
  getElevationStyle,
  getShapeStyle,
  getTypographyStyle,
  getStateStyle 
} from '../theme/designTokens';

const MD3ExpressiveButton = ({
  // Content
  children,
  icon,
  iconPosition = 'left', // 'left', 'right', 'top'
  
  // Variants
  variant = 'filled', // 'filled', 'outlined', 'text', 'elevated', 'tonal'
  size = 'medium', // 'small', 'medium', 'large', 'extraLarge'
  
  // States
  disabled = false,
  loading = false,
  
  // Styling
  style,
  contentStyle,
  labelStyle,
  
  // Interaction
  onPress,
  onLongPress,
  
  // Accessibility
  accessibilityLabel,
  accessibilityHint,
  
  // Advanced
  fullWidth = false,
  expressive = false, // Use expressive shape family
}) => {
  const theme = useTheme();

  // Get size configuration
  const sizeConfig = COMPONENT_SIZES.button[size] || COMPONENT_SIZES.button.medium;
  
  // Get shape style
  const shapeFamily = expressive ? 'expressive' : 'rounded';
  const shapeSize = size === 'small' ? 'small' : size === 'large' || size === 'extraLarge' ? 'large' : 'medium';
  const shapeStyle = getShapeStyle(shapeFamily, shapeSize);
  
  // Get typography style
  const typographyStyle = getTypographyStyle('label', 'large', expressive);

  // Variant styles
  const getVariantStyles = () => {
    const variants = {
      filled: {
        backgroundColor: theme.colors.primary,
        color: theme.colors.onPrimary,
        borderWidth: 0,
        elevation: 0,
      },
      outlined: {
        backgroundColor: 'transparent',
        color: theme.colors.primary,
        borderWidth: 1,
        borderColor: theme.colors.outline,
        elevation: 0,
      },
      text: {
        backgroundColor: 'transparent',
        color: theme.colors.primary,
        borderWidth: 0,
        elevation: 0,
      },
      elevated: {
        backgroundColor: theme.colors.surfaceContainerLow || theme.colors.surface,
        color: theme.colors.primary,
        borderWidth: 0,
        elevation: 1,
      },
      tonal: {
        backgroundColor: theme.colors.secondaryContainer,
        color: theme.colors.onSecondaryContainer,
        borderWidth: 0,
        elevation: 0,
      },
    };

    return variants[variant] || variants.filled;
  };

  const variantStyles = getVariantStyles();

  // Handle press states
  const getOpacity = () => {
    if (disabled) return STATE.opacity.disabled;
    if (loading) return STATE.opacity.inactive;
    return STATE.opacity.active;
  };

  // Render icon
  const renderIcon = () => {
    if (!icon || loading) return null;

    const iconSize = size === 'small' ? 16 : size === 'large' || size === 'extraLarge' ? 20 : 18;
    
    return (
      <Icon 
        name={icon} 
        size={iconSize} 
        color={variantStyles.color}
        style={[
          iconPosition === 'left' && { marginRight: SPACING.sm },
          iconPosition === 'right' && { marginLeft: SPACING.sm },
          iconPosition === 'top' && { marginBottom: SPACING.xs },
        ]}
      />
    );
  };

  // Render loading indicator
  const renderLoading = () => {
    if (!loading) return null;

    const indicatorSize = size === 'small' ? 16 : 20;
    
    return (
      <ActivityIndicator 
        size={indicatorSize} 
        color={variantStyles.color}
        style={{ marginRight: children ? SPACING.sm : 0 }}
      />
    );
  };

  // Render content
  const renderContent = () => {
    const textStyle = [
      typographyStyle,
      { color: variantStyles.color },
      labelStyle,
    ];

    if (iconPosition === 'top') {
      return (
        <View style={styles.verticalContent}>
          {renderIcon()}
          {children && (
            <Text style={textStyle} numberOfLines={1}>
              {children}
            </Text>
          )}
        </View>
      );
    }

    return (
      <View style={[styles.horizontalContent, contentStyle]}>
        {iconPosition === 'left' && renderIcon()}
        {renderLoading()}
        {children && (
          <Text style={textStyle} numberOfLines={1}>
            {children}
          </Text>
        )}
        {iconPosition === 'right' && renderIcon()}
      </View>
    );
  };

  // Container styles
  const containerStyles = [
    styles.container,
    shapeStyle,
    {
      backgroundColor: variantStyles.backgroundColor,
      borderColor: variantStyles.borderColor,
      borderWidth: variantStyles.borderWidth,
      height: sizeConfig.height,
      paddingHorizontal: sizeConfig.paddingHorizontal,
      minWidth: fullWidth ? '100%' : sizeConfig.minWidth,
      opacity: getOpacity(),
    },
    variantStyles.elevation > 0 && getElevationStyle(variantStyles.elevation, theme),
    style,
  ];

  return (
    <TouchableOpacity
      style={containerStyles}
      onPress={onPress}
      onLongPress={onLongPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
      accessibilityLabel={accessibilityLabel}
      accessibilityHint={accessibilityHint}
      accessibilityRole="button"
      accessibilityState={{ disabled: disabled || loading }}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  horizontalContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  verticalContent: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default MD3ExpressiveButton;
