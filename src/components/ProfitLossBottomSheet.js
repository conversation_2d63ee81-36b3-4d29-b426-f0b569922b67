/**
 * ProfitLossBottomSheet - Unified P&L Statement
 * Redesigned with unified design inspired by QuickActionsBottomSheet
 */

import React, { useState, forwardRef, useImperativeHandle, useCallback, useMemo } from 'react';
import { View, StyleSheet, ScrollView, Keyboard } from 'react-native';
import {
  Text,
  Button,
  useTheme,
  Card,
  Divider,
  DataTable,
  IconButton,
  Portal,
} from 'react-native-paper';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { FINANCIAL_CONFIG } from '../config/constants';
// import * as FileSystem from 'expo-file-system'; // Will be implemented later
// import * as Sharing from 'expo-sharing'; // Will be implemented later

const ProfitLossBottomSheet = forwardRef(({ data }, ref) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const bottomSheetRef = React.useRef(null);

  const snapPoints = useMemo(() => ['90%'], []);

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      Keyboard.dismiss();
    }
  }, []);

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  useImperativeHandle(ref, () => ({
    expand: () => {
      bottomSheetRef.current?.expand();
    },
    close: () => {
      bottomSheetRef.current?.close();
    },
  }));

  const formatCurrency = (amount) => {
    return `${FINANCIAL_CONFIG.CURRENCY.SYMBOL}${amount.toFixed(FINANCIAL_CONFIG.CURRENCY.DECIMAL_PLACES)}`;
  };

  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`;
  };

  const exportToPDF = async () => {
    // Export functionality will be implemented later
    console.log('Export P&L statement - feature coming soon');
  };

  const generateProfitLossHTML = () => {
    if (!data) return '';

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Profit & Loss Statement</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .period { color: #666; }
          table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
          th { background-color: #f5f5f5; font-weight: bold; }
          .amount { text-align: right; }
          .total-row { font-weight: bold; border-top: 2px solid #333; }
          .positive { color: #4CAF50; }
          .negative { color: #F44336; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Profit & Loss Statement</h1>
          <p class="period">Period: ${new Date(data.period.startDate).toLocaleDateString()} - ${new Date(data.period.endDate).toLocaleDateString()}</p>
          <p>Generated on: ${new Date().toLocaleDateString()}</p>
        </div>

        <table>
          <tr><th colspan="2">REVENUE</th></tr>
          <tr>
            <td>Total Sales (${data.revenue.orderCount} orders)</td>
            <td class="amount">${formatCurrency(data.revenue.totalSales)}</td>
          </tr>
          <tr>
            <td>Average Order Value</td>
            <td class="amount">${formatCurrency(data.revenue.averageOrderValue)}</td>
          </tr>
          <tr class="total-row">
            <td>TOTAL REVENUE</td>
            <td class="amount">${formatCurrency(data.revenue.totalSales)}</td>
          </tr>
        </table>

        <table>
          <tr><th colspan="2">EXPENSES</th></tr>
          ${Object.entries(data.expenses.byCategory).map(([category, amount]) => `
            <tr>
              <td>${category}</td>
              <td class="amount">${formatCurrency(amount)}</td>
            </tr>
          `).join('')}
          <tr class="total-row">
            <td>TOTAL EXPENSES</td>
            <td class="amount">${formatCurrency(data.expenses.total)}</td>
          </tr>
        </table>

        <table>
          <tr><th colspan="2">TAXES</th></tr>
          <tr>
            <td>Sales Tax (${formatPercentage(FINANCIAL_CONFIG.TAX_RATES.SALES_TAX * 100)})</td>
            <td class="amount">${formatCurrency(data.taxes.salesTax)}</td>
          </tr>
          <tr>
            <td>Income Tax (${formatPercentage(FINANCIAL_CONFIG.TAX_RATES.INCOME_TAX * 100)})</td>
            <td class="amount">${formatCurrency(data.taxes.incomeTax)}</td>
          </tr>
          <tr class="total-row">
            <td>TOTAL TAXES</td>
            <td class="amount">${formatCurrency(data.taxes.total)}</td>
          </tr>
        </table>

        <table>
          <tr><th colspan="2">PROFIT SUMMARY</th></tr>
          <tr>
            <td>Gross Profit</td>
            <td class="amount ${data.profit.gross >= 0 ? 'positive' : 'negative'}">${formatCurrency(data.profit.gross)}</td>
          </tr>
          <tr class="total-row">
            <td>NET PROFIT</td>
            <td class="amount ${data.profit.net >= 0 ? 'positive' : 'negative'}">${formatCurrency(data.profit.net)}</td>
          </tr>
          <tr>
            <td>Profit Margin</td>
            <td class="amount">${formatPercentage(data.profit.margin)}</td>
          </tr>
        </table>
      </body>
      </html>
    `;
  };

  if (!data) return null;

  const getProfitStatus = () => {
    const netProfit = data.profit.net;
    const margin = data.profit.margin;
    return `${netProfit >= 0 ? 'Profit' : 'Loss'}: ${formatCurrency(Math.abs(netProfit))} • ${formatPercentage(margin)} margin`;
  };

  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
        style={styles.bottomSheet}
      >
        <BottomSheetView style={styles.container}>
          {/* Header - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.header, { borderBottomColor: theme.colors.outline + '20' }]}>
            <View style={styles.headerLeft}>
              <View style={[styles.iconContainer, { backgroundColor: theme.colors.primary + '15' }]}>
                <Icon name="chart-line" size={24} color={theme.colors.primary} />
              </View>
              <View style={styles.titleContainer}>
                <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
                  Profit & Loss Statement
                </Text>
                <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
                  {new Date(data.period.startDate).toLocaleDateString()} - {new Date(data.period.endDate).toLocaleDateString()} • {getProfitStatus()}
                </Text>
              </View>
            </View>
            <IconButton
              icon="close"
              size={20}
              onPress={() => bottomSheetRef.current?.close()}
              iconColor={theme.colors.onSurfaceVariant}
            />
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {/* Revenue Section */}
            <Card style={[styles.section, { backgroundColor: theme.colors.surface }]}>
              <Card.Content>
            <View style={styles.sectionHeader}>
              <Icon name="trending-up" size={24} color={theme.colors.primary} />
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginLeft: 8 }}>
                Revenue
              </Text>
            </View>

            <DataTable>
              <DataTable.Row>
                <DataTable.Cell>Total Sales ({data.revenue.orderCount} orders)</DataTable.Cell>
                <DataTable.Cell numeric>{formatCurrency(data.revenue.totalSales)}</DataTable.Cell>
              </DataTable.Row>
              <DataTable.Row>
                <DataTable.Cell>Average Order Value</DataTable.Cell>
                <DataTable.Cell numeric>{formatCurrency(data.revenue.averageOrderValue)}</DataTable.Cell>
              </DataTable.Row>
            </DataTable>

            <Divider style={styles.divider} />
            <View style={styles.totalRow}>
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, fontWeight: '700' }}>
                Total Revenue
              </Text>
              <Text variant="titleMedium" style={{ color: theme.colors.primary, fontWeight: '700' }}>
                {formatCurrency(data.revenue.totalSales)}
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Expenses Section */}
        <Card style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Icon name="trending-down" size={24} color="#F44336" />
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginLeft: 8 }}>
                Expenses
              </Text>
            </View>

            <DataTable>
              {Object.entries(data.expenses.byCategory).map(([category, amount]) => (
                <DataTable.Row key={category}>
                  <DataTable.Cell>{category}</DataTable.Cell>
                  <DataTable.Cell numeric>{formatCurrency(amount)}</DataTable.Cell>
                </DataTable.Row>
              ))}
            </DataTable>

            <Divider style={styles.divider} />
            <View style={styles.totalRow}>
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, fontWeight: '700' }}>
                Total Expenses
              </Text>
              <Text variant="titleMedium" style={{ color: '#F44336', fontWeight: '700' }}>
                {formatCurrency(data.expenses.total)}
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Taxes Section */}
        <Card style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Icon name="calculator" size={24} color="#FF9800" />
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginLeft: 8 }}>
                Taxes
              </Text>
            </View>

            <DataTable>
              <DataTable.Row>
                <DataTable.Cell>Sales Tax ({formatPercentage(FINANCIAL_CONFIG.TAX_RATES.SALES_TAX * 100)})</DataTable.Cell>
                <DataTable.Cell numeric>{formatCurrency(data.taxes.salesTax)}</DataTable.Cell>
              </DataTable.Row>
              <DataTable.Row>
                <DataTable.Cell>Income Tax ({formatPercentage(FINANCIAL_CONFIG.TAX_RATES.INCOME_TAX * 100)})</DataTable.Cell>
                <DataTable.Cell numeric>{formatCurrency(data.taxes.incomeTax)}</DataTable.Cell>
              </DataTable.Row>
            </DataTable>

            <Divider style={styles.divider} />
            <View style={styles.totalRow}>
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, fontWeight: '700' }}>
                Total Taxes
              </Text>
              <Text variant="titleMedium" style={{ color: '#FF9800', fontWeight: '700' }}>
                {formatCurrency(data.taxes.total)}
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Profit Summary */}
        <Card style={[styles.section, { backgroundColor: theme.colors.primaryContainer }]}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Icon name="chart-line" size={24} color={theme.colors.primary} />
              <Text variant="titleMedium" style={{ color: theme.colors.onPrimaryContainer, marginLeft: 8 }}>
                Profit Summary
              </Text>
            </View>

            <DataTable>
              <DataTable.Row>
                <DataTable.Cell>
                  <Text style={{ color: theme.colors.onPrimaryContainer }}>Gross Profit</Text>
                </DataTable.Cell>
                <DataTable.Cell numeric>
                  <Text style={{
                    color: data.profit.gross >= 0 ? '#4CAF50' : '#F44336',
                    fontWeight: '600'
                  }}>
                    {formatCurrency(data.profit.gross)}
                  </Text>
                </DataTable.Cell>
              </DataTable.Row>
              <DataTable.Row>
                <DataTable.Cell>
                  <Text style={{ color: theme.colors.onPrimaryContainer }}>Profit Margin</Text>
                </DataTable.Cell>
                <DataTable.Cell numeric>
                  <Text style={{ color: theme.colors.onPrimaryContainer }}>
                    {formatPercentage(data.profit.margin)}
                  </Text>
                </DataTable.Cell>
              </DataTable.Row>
            </DataTable>

            <Divider style={styles.divider} />
            <View style={styles.totalRow}>
              <Text variant="titleLarge" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '700' }}>
                Net Profit
              </Text>
              <Text variant="titleLarge" style={{
                color: data.profit.net >= 0 ? '#4CAF50' : '#F44336',
                fontWeight: '700'
              }}>
                {formatCurrency(data.profit.net)}
              </Text>
              </View>
              </Card.Content>
            </Card>

            {/* Bottom padding for safe area */}
            <View style={{ height: Math.max(insets.bottom + 80, 100) }} />
          </ScrollView>

          {/* Footer - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.footer, { paddingBottom: insets.bottom, borderTopColor: theme.colors.outline + '20' }]}>
            <Button
              mode="contained"
              onPress={exportToPDF}
              icon="download"
              style={styles.exportButton}
            >
              Export Statement
            </Button>
          </View>
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
});

const styles = StyleSheet.create({
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '700',
    lineHeight: 28,
  },
  subtitle: {
    marginTop: 2,
    lineHeight: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  section: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  divider: {
    marginVertical: 8,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 8,
  },
  footer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    borderTopWidth: 1,
    alignItems: 'center',
  },
  exportButton: {
    borderRadius: 25,
    minWidth: 200,
  },
});

export default ProfitLossBottomSheet;
