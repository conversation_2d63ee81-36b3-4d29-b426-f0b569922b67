/**
 * UnifiedInfoCard - Consistent info card component for all screens
 * Supports different types: stat, financial, metric, order, product, etc.
 */

import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Surface, Text, useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const UnifiedInfoCard = ({
  // Content
  title,
  value,
  subtitle,
  description,

  // Visual
  icon,
  iconColor,
  iconBackground,

  // Type-specific styling
  type = 'default', // 'stat', 'financial', 'metric', 'order', 'product', 'activity'

  // Interaction
  onPress,
  disabled = false,

  // Layout
  style,
  elevation = 0,

  // Additional data for complex cards
  data,

  // Growth indicator for metrics
  growth,

  // Status for orders/products
  status,
  statusColor,
}) => {
  const theme = useTheme();

  // Get card styling based on type
  const getCardStyles = () => {
    const baseStyle = {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      overflow: 'hidden',
    };

    switch (type) {
      case 'stat':
        return {
          ...baseStyle,
          borderWidth: 1,
          borderColor: theme.colors.outline + '15',
        };
      case 'financial':
        return {
          ...baseStyle,
          borderWidth: 1,
          borderColor: theme.colors.outline + '15',
        };
      case 'metric':
        return {
          ...baseStyle,
          borderWidth: 1,
          borderColor: theme.colors.outline + '10',
        };
      case 'order':
      case 'product':
        return {
          ...baseStyle,
          borderWidth: 1,
          borderColor: theme.colors.outline + '25',
        };
      default:
        return baseStyle;
    }
  };

  // Get icon container styling
  const getIconContainerStyle = () => {
    const backgroundColor = iconBackground || (iconColor ? iconColor + '15' : theme.colors.primary + '15');
    const size = (type === 'stat' || type === 'financial') ? 28 : 24;

    return {
      width: size + 8,
      height: size + 8,
      borderRadius: (size + 8) / 2,
      backgroundColor,
      justifyContent: 'center',
      alignItems: 'center',
    };
  };

  // Get icon size based on type
  const getIconSize = () => {
    switch (type) {
      case 'stat': return 20;
      case 'financial': return 20;
      case 'metric': return 16;
      default: return 18;
    }
  };

  // Render growth indicator
  const renderGrowthIndicator = () => {
    if (!growth) return null;

    const isPositive = growth > 0;
    const growthColor = isPositive ? '#10B981' : '#EF4444';
    const growthIcon = isPositive ? 'trending-up' : 'trending-down';

    return (
      <View style={styles.growthContainer}>
        <Icon name={growthIcon} size={12} color={growthColor} />
        <Text variant="bodySmall" style={{ color: growthColor, marginLeft: 2 }}>
          {Math.abs(growth).toFixed(1)}%
        </Text>
      </View>
    );
  };

  // Render status indicator
  const renderStatusIndicator = () => {
    if (!status) return null;

    return (
      <View style={[styles.statusIndicator, { backgroundColor: statusColor || theme.colors.primary }]}>
        <Text variant="labelSmall" style={{ color: '#fff', fontSize: 10 }}>
          {status}
        </Text>
      </View>
    );
  };

  // Render card content based on type
  const renderContent = () => {
    switch (type) {
      case 'stat':
        return (
          <View style={styles.financialContent}>
            {icon && (
              <View style={getIconContainerStyle()}>
                <Icon name={icon} size={getIconSize()} color={iconColor || theme.colors.primary} />
              </View>
            )}
            <View style={styles.financialText}>
              <Text variant="titleSmall" style={[styles.financialTitle, { color: theme.colors.onSurface }]}>
                {title}
              </Text>
              <Text variant="bodySmall" style={[styles.financialSubtitle, { color: theme.colors.onSurfaceVariant }]}>
                {subtitle || value}
              </Text>
            </View>
          </View>
        );

      case 'financial':
        return (
          <View style={styles.financialContent}>
            {icon && (
              <View style={getIconContainerStyle()}>
                <Icon name={icon} size={getIconSize()} color={iconColor || theme.colors.primary} />
              </View>
            )}
            <View style={styles.financialText}>
              <Text variant="titleSmall" style={[styles.financialTitle, { color: theme.colors.onSurface }]}>
                {title}
              </Text>
              <Text variant="bodySmall" style={[styles.financialSubtitle, { color: theme.colors.onSurfaceVariant }]}>
                {subtitle || description}
              </Text>
            </View>
          </View>
        );

      case 'metric':
        return (
          <View style={styles.metricContent}>
            <Text variant="headlineSmall" style={[styles.metricValue, { color: theme.colors.onSurface }]}>
              {value}
            </Text>
            <Text variant="bodySmall" style={[styles.metricTitle, { color: theme.colors.onSurfaceVariant }]}>
              {title}
            </Text>
            {subtitle && (
              <Text variant="bodySmall" style={[styles.metricSubtitle, { color: theme.colors.onSurfaceVariant }]}>
                {subtitle}
              </Text>
            )}
            {renderGrowthIndicator()}
          </View>
        );

      case 'order':
        return (
          <View style={styles.orderContent}>
            {/* Status Bar */}
            <View style={[styles.orderStatusBar, { backgroundColor: statusColor || theme.colors.primary }]} />

            {/* Main Content */}
            <View style={styles.orderMainContent}>
              <View style={styles.orderLeft}>
                <View style={styles.orderInfo}>
                  <Text variant="titleMedium" style={[styles.orderTitle, { color: theme.colors.onSurface }]}>
                    Order #{data?.id || value}
                  </Text>
                  <Text variant="bodyMedium" style={[styles.orderCustomer, { color: theme.colors.onSurfaceVariant }]}>
                    {data?.customer || subtitle}
                  </Text>
                  <Text variant="bodySmall" style={[styles.orderDate, { color: theme.colors.onSurfaceVariant }]}>
                    {data?.date ? new Date(data.date).toLocaleDateString() : ''} • {data?.time || ''}
                  </Text>
                </View>
              </View>

              <View style={styles.orderRight}>
                <Text variant="titleLarge" style={[styles.orderAmount, { color: theme.colors.onSurface }]}>
                  ৳{data?.total?.toFixed(0) || value}
                </Text>
                <View style={[styles.orderStatus, { backgroundColor: `${statusColor || theme.colors.primary}20` }]}>
                  <Text variant="bodySmall" style={[styles.orderStatusText, { color: statusColor || theme.colors.primary }]}>
                    {status || data?.status || 'Pending'}
                  </Text>
                </View>
              </View>
            </View>

            {/* Items Summary */}
            {description && (
              <View style={styles.orderItems}>
                <Text variant="bodySmall" style={[styles.orderItemsText, { color: theme.colors.onSurfaceVariant }]}>
                  {description}
                </Text>
              </View>
            )}
          </View>
        );

      default:
        return (
          <View style={styles.defaultContent}>
            {icon && (
              <View style={getIconContainerStyle()}>
                <Icon name={icon} size={getIconSize()} color={iconColor || theme.colors.primary} />
              </View>
            )}
            <View style={styles.defaultText}>
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface }}>
                {title}
              </Text>
              {value && (
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, marginTop: 2 }}>
                  {value}
                </Text>
              )}
              {(subtitle || description) && (
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 4 }}>
                  {subtitle || description}
                </Text>
              )}
            </View>
          </View>
        );
    }
  };

  const CardComponent = onPress && !disabled ? TouchableOpacity : View;

  return (
    <CardComponent
      style={[styles.cardWrapper, style]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.7}
    >
      <Surface style={getCardStyles()} elevation={elevation}>
        <View style={styles.cardInner}>
          {renderContent()}
        </View>
      </Surface>
    </CardComponent>
  );
};

const styles = StyleSheet.create({
  cardWrapper: {
    marginBottom: 8,
  },
  cardInner: {
    padding: 12,
  },

  // Stat card styles
  statContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  statMain: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  statText: {
    marginLeft: 12,
    flex: 1,
  },
  statValue: {
    fontWeight: '700',
    lineHeight: 28,
  },
  statTitle: {
    marginTop: 1,
    lineHeight: 16,
  },

  // Financial card styles
  financialContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  financialText: {
    marginLeft: 12,
    flex: 1,
  },
  financialTitle: {
    fontWeight: '600',
    lineHeight: 20,
  },
  financialSubtitle: {
    marginTop: 2,
    lineHeight: 16,
  },

  // Metric card styles
  metricContent: {
    alignItems: 'flex-start',
  },
  metricValue: {
    fontWeight: '700',
    lineHeight: 28,
  },
  metricTitle: {
    marginTop: 4,
    lineHeight: 16,
  },
  metricSubtitle: {
    marginTop: 2,
    lineHeight: 16,
  },

  // Order card styles
  orderContent: {
    position: 'relative',
    overflow: 'hidden',
  },
  orderStatusBar: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
  },
  orderMainContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingTop: 8,
    marginBottom: 8,
  },
  orderLeft: {
    flex: 1,
    marginRight: 12,
  },
  orderInfo: {
    gap: 2,
  },
  orderTitle: {
    fontWeight: '700',
    lineHeight: 20,
  },
  orderCustomer: {
    fontWeight: '500',
    lineHeight: 18,
  },
  orderDate: {
    lineHeight: 16,
    opacity: 0.8,
  },
  orderRight: {
    alignItems: 'flex-end',
    gap: 6,
  },
  orderAmount: {
    fontWeight: '700',
    lineHeight: 24,
  },
  orderStatus: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
    minWidth: 60,
    alignItems: 'center',
  },
  orderStatusText: {
    fontWeight: '600',
    fontSize: 11,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  orderItems: {
    paddingTop: 4,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
  },
  orderItemsText: {
    lineHeight: 16,
    fontStyle: 'italic',
  },

  // Default card styles
  defaultContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  defaultText: {
    marginLeft: 12,
    flex: 1,
  },

  // Common elements
  growthContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  statusIndicator: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default UnifiedInfoCard;
