/**
 * CustomerDetailsBottomSheet - Unified Customer Details
 * Redesigned with unified design inspired by QuickActionsBottomSheet
 */

import React, { forwardRef, useState, useImperativeHandle, useCallback, useMemo } from 'react';
import { View, StyleSheet, ScrollView, Keyboard } from 'react-native';
import {
  Text,
  Button,
  Chip,
  Surface,
  useTheme,
  Divider,
  IconButton,
  Portal
} from 'react-native-paper';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const CustomerDetailsBottomSheet = forwardRef((props, ref) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const [customer, setCustomer] = useState(null);
  const bottomSheetRef = React.useRef(null);

  const snapPoints = useMemo(() => ['25%', '80%'], []);

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      Keyboard.dismiss();
      setCustomer(null);
    }
  }, []);

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  useImperativeHandle(ref, () => ({
    expand: (customerData) => {
      setCustomer(customerData);
      bottomSheetRef.current?.expand();
    },
    close: () => {
      bottomSheetRef.current?.close();
    }
  }));

  const handleCall = () => {
    console.log('Calling customer:', customer?.phone);
    // Implement call functionality
  };

  const handleEmail = () => {
    console.log('Emailing customer:', customer?.email);
    // Implement email functionality
  };

  const handleEdit = () => {
    console.log('Editing customer:', customer?.name);
    // Implement edit functionality
    bottomSheetRef.current?.close();
  };

  const footerActions = (
    <View style={styles.footerActions}>
      <Button
        mode="outlined"
        icon="phone"
        onPress={handleCall}
        style={styles.actionButton}
      >
        Call
      </Button>
      <Button
        mode="outlined"
        icon="email"
        onPress={handleEmail}
        style={styles.actionButton}
      >
        Email
      </Button>
      <Button
        mode="contained"
        icon="pencil"
        onPress={handleEdit}
        style={styles.actionButton}
      >
        Edit
      </Button>
    </View>
  );

  if (!customer) return null;

  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
        style={styles.bottomSheet}
      >
        <BottomSheetView style={styles.container}>
          {/* Header - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.header, { borderBottomColor: theme.colors.outline + '20' }]}>
            <View style={styles.headerLeft}>
              <View style={[styles.iconContainer, { backgroundColor: theme.colors.primary + '15' }]}>
                <Icon name="account" size={24} color={theme.colors.primary} />
              </View>
              <View style={styles.titleContainer}>
                <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
                  {customer.name}
                </Text>
                <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
                  Customer since {new Date(customer.createdAt).getFullYear()} • {customer.totalOrders || 0} orders
                </Text>
              </View>
            </View>
            <IconButton
              icon="close"
              size={20}
              onPress={() => bottomSheetRef.current?.close()}
              iconColor={theme.colors.onSurfaceVariant}
            />
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
      {/* Contact Information */}
      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
        <View style={styles.sectionHeader}>
          <Icon name="card-account-details" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>Contact Information</Text>
        </View>

        <View style={styles.infoRow}>
          <Icon name="phone" size={16} color={theme.colors.onSurfaceVariant} />
          <Text variant="bodyMedium" style={styles.infoText}>{customer.phone}</Text>
        </View>

        <View style={styles.infoRow}>
          <Icon name="email" size={16} color={theme.colors.onSurfaceVariant} />
          <Text variant="bodyMedium" style={styles.infoText}>{customer.email}</Text>
        </View>
      </Surface>

      {/* Order History */}
      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
        <View style={styles.sectionHeader}>
          <Icon name="history" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>Order History</Text>
        </View>

        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.primary, fontWeight: '700' }}>
              {customer.totalOrders || 0}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Total Orders
            </Text>
          </View>

          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.secondary, fontWeight: '700' }}>
              ${customer.totalSpent || 0}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Total Spent
            </Text>
          </View>
        </View>
      </Surface>

      {/* Customer Status */}
      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
        <View style={styles.sectionHeader}>
          <Icon name="star" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>Customer Status</Text>
        </View>

        <View style={styles.chipContainer}>
          <Chip
            icon="crown"
            mode="flat"
            style={[styles.chip, { backgroundColor: theme.colors.primaryContainer }]}
            textStyle={{ color: theme.colors.onPrimaryContainer }}
          >
            VIP Customer
          </Chip>
          <Chip
            icon="check-circle"
            mode="flat"
            style={[styles.chip, { backgroundColor: theme.colors.secondaryContainer }]}
            textStyle={{ color: theme.colors.onSecondaryContainer }}
          >
            Verified
          </Chip>
            </View>
            </Surface>

            {/* Bottom padding for safe area */}
            <View style={{ height: Math.max(insets.bottom + 80, 100) }} />
          </ScrollView>

          {/* Footer - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.footer, { paddingBottom: insets.bottom, borderTopColor: theme.colors.outline + '20' }]}>
            {footerActions}
          </View>
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
});

const styles = StyleSheet.create({
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '700',
    lineHeight: 28,
  },
  subtitle: {
    marginTop: 2,
    lineHeight: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  section: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  sectionTitle: {
    fontWeight: '600',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 12,
  },
  infoText: {
    flex: 1,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  chipContainer: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap',
  },
  chip: {
    marginBottom: 4,
  },
  footer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    borderTopWidth: 1,
  },
  footerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
  },
});

CustomerDetailsBottomSheet.displayName = 'CustomerDetailsBottomSheet';

export default CustomerDetailsBottomSheet;
