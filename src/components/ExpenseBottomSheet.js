/**
 * ExpenseBottomSheet - Unified Expense Form
 * Redesigned with unified design inspired by QuickActionsBottomSheet
 */

import React, { useState, useCallback, forwardRef, useImperativeHandle, useMemo } from 'react';
import { View, StyleSheet, Alert, ScrollView, Keyboard } from 'react-native';
import {
  Text,
  TextInput,
  Button,
  useTheme,
  SegmentedButtons,
  IconButton,
  Portal,
} from 'react-native-paper';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
// import { Picker } from '@react-native-picker/picker'; // Will be implemented later
// import DateTimePicker from '@react-native-community/datetimepicker'; // Will be implemented later
import { useFinancial } from '../context/FinancialContext';
import { FINANCIAL_CONFIG } from '../config/constants';
import { Validator } from '../utils/validation';

const ExpenseBottomSheet = forwardRef((props, ref) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const { addExpense, updateExpense } = useFinancial();
  const bottomSheetRef = React.useRef(null);

  const snapPoints = useMemo(() => ['90%'], []);

  const [mode, setMode] = useState('add'); // 'add' or 'edit'
  const [expense, setExpense] = useState({
    description: '',
    amount: '',
    category: FINANCIAL_CONFIG.EXPENSE_CATEGORIES[0],
    paymentMethod: FINANCIAL_CONFIG.PAYMENT_METHODS[0],
    date: new Date().toISOString().split('T')[0],
    notes: '',
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      Keyboard.dismiss();
      setExpense({
        description: '',
        amount: '',
        category: FINANCIAL_CONFIG.EXPENSE_CATEGORIES[0],
        paymentMethod: FINANCIAL_CONFIG.PAYMENT_METHODS[0],
        date: new Date().toISOString().split('T')[0],
        notes: '',
      });
      setErrors({});
    }
  }, []);

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  useImperativeHandle(ref, () => ({
    expand: (expenseData = null) => {
      if (expenseData) {
        setMode('edit');
        setExpense(expenseData);
      } else {
        setMode('add');
        setExpense({
          description: '',
          amount: '',
          category: FINANCIAL_CONFIG.EXPENSE_CATEGORIES[0],
          paymentMethod: FINANCIAL_CONFIG.PAYMENT_METHODS[0],
          date: new Date().toISOString().split('T')[0],
          notes: '',
        });
      }
      setErrors({});
      bottomSheetRef.current?.expand();
    },
    close: () => {
      bottomSheetRef.current?.close();
    },
  }));

  const validateExpense = useCallback(() => {
    const newErrors = {};

    // Description validation
    if (!expense.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (expense.description.length < 3) {
      newErrors.description = 'Description must be at least 3 characters';
    }

    // Amount validation
    if (!expense.amount) {
      newErrors.amount = 'Amount is required';
    } else {
      const amount = parseFloat(expense.amount);
      if (isNaN(amount) || amount <= 0) {
        newErrors.amount = 'Amount must be a positive number';
      } else if (amount > 10000) {
        newErrors.amount = 'Amount cannot exceed $10,000';
      }
    }

    // Date validation
    if (!expense.date) {
      newErrors.date = 'Date is required';
    } else {
      const expenseDate = new Date(expense.date);
      const today = new Date();
      if (expenseDate > today) {
        newErrors.date = 'Date cannot be in the future';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [expense]);

  const handleSave = useCallback(async () => {
    if (!validateExpense()) {
      return;
    }

    setLoading(true);
    try {
      const expenseData = {
        ...expense,
        amount: parseFloat(expense.amount),
      };

      if (mode === 'add') {
        await addExpense(expenseData);
        Alert.alert('Success', 'Expense added successfully!');
      } else {
        await updateExpense(expense.id, expenseData);
        Alert.alert('Success', 'Expense updated successfully!');
      }

      bottomSheetRef.current?.close();
    } catch (error) {
      Alert.alert('Error', error.message || 'Failed to save expense');
    } finally {
      setLoading(false);
    }
  }, [expense, mode, validateExpense, addExpense, updateExpense]);

  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setExpense(prev => ({
        ...prev,
        date: selectedDate.toISOString().split('T')[0],
      }));
    }
  };

  const updateField = useCallback((field, value) => {
    setExpense(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  }, [errors]);

  const getTotalAmount = () => {
    return expense.amount ? `$${parseFloat(expense.amount || 0).toFixed(2)}` : 'Enter amount';
  };

  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
        style={styles.bottomSheet}
      >
        <BottomSheetView style={styles.container}>
          {/* Header - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.header, { borderBottomColor: theme.colors.outline + '20' }]}>
            <View style={styles.headerLeft}>
              <View style={[styles.iconContainer, { backgroundColor: theme.colors.error + '15' }]}>
                <Icon name="receipt" size={24} color={theme.colors.error} />
              </View>
              <View style={styles.titleContainer}>
                <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
                  {mode === 'add' ? 'Add Expense' : 'Edit Expense'}
                </Text>
                <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
                  {getTotalAmount()} • {expense.category || 'Select category'}
                </Text>
              </View>
            </View>
            <IconButton
              icon="close"
              size={20}
              onPress={() => bottomSheetRef.current?.close()}
              iconColor={theme.colors.onSurfaceVariant}
            />
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            <View style={styles.form}>
              {/* Description */}
              <TextInput
            label="Description *"
            value={expense.description}
            onChangeText={(text) => updateField('description', text)}
            error={!!errors.description}
            style={styles.input}
            mode="outlined"
            placeholder="e.g., Flour purchase, Electricity bill"
          />
          {errors.description && (
            <Text variant="bodySmall" style={styles.errorText}>
              {errors.description}
            </Text>
          )}

          {/* Amount */}
          <TextInput
            label="Amount *"
            value={expense.amount}
            onChangeText={(text) => updateField('amount', text)}
            error={!!errors.amount}
            style={styles.input}
            mode="outlined"
            keyboardType="numeric"
            placeholder="0.00"
            left={<TextInput.Icon icon={() => (
              <Text style={{ color: theme.colors.onSurfaceVariant }}>
                {FINANCIAL_CONFIG.CURRENCY.SYMBOL}
              </Text>
            )} />}
          />
          {errors.amount && (
            <Text variant="bodySmall" style={styles.errorText}>
              {errors.amount}
            </Text>
          )}

          {/* Category */}
          <TextInput
            label="Category *"
            value={expense.category}
            onChangeText={(text) => updateField('category', text)}
            style={styles.input}
            mode="outlined"
            placeholder="e.g., Ingredients, Utilities, Staff Wages"
          />

          {/* Payment Method */}
          <TextInput
            label="Payment Method *"
            value={expense.paymentMethod}
            onChangeText={(text) => updateField('paymentMethod', text)}
            style={styles.input}
            mode="outlined"
            placeholder="e.g., Cash, Credit Card, Bank Transfer"
          />

          {/* Date */}
          <TextInput
            label="Date *"
            value={expense.date}
            onChangeText={(text) => updateField('date', text)}
            error={!!errors.date}
            style={styles.input}
            mode="outlined"
            placeholder="YYYY-MM-DD"
          />
          {errors.date && (
            <Text variant="bodySmall" style={styles.errorText}>
              {errors.date}
            </Text>
          )}

          {/* Notes */}
          <TextInput
            label="Notes (Optional)"
            value={expense.notes}
            onChangeText={(text) => updateField('notes', text)}
            style={styles.input}
            mode="outlined"
            multiline
            numberOfLines={3}
                placeholder="Additional notes about this expense..."
              />
            </View>

            {/* Bottom padding for safe area */}
            <View style={{ height: Math.max(insets.bottom + 80, 100) }} />
          </ScrollView>

          {/* Footer - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.footer, { paddingBottom: insets.bottom, borderTopColor: theme.colors.outline + '20' }]}>
            <View style={styles.buttonRow}>
              <Button
                mode="outlined"
                onPress={() => bottomSheetRef.current?.close()}
                style={styles.button}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                mode="contained"
                onPress={handleSave}
                style={styles.button}
                loading={loading}
                disabled={loading}
              >
                {mode === 'add' ? 'Add Expense' : 'Update Expense'}
              </Button>
            </View>
          </View>
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
});

const styles = StyleSheet.create({
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '700',
    lineHeight: 28,
  },
  subtitle: {
    marginTop: 2,
    lineHeight: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  form: {
    paddingBottom: 32,
  },
  input: {
    marginBottom: 16,
  },
  footer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    borderTopWidth: 1,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  button: {
    flex: 1,
  },
  errorText: {
    color: '#F44336',
    marginTop: -12,
    marginBottom: 8,
  },
});

export default ExpenseBottomSheet;
