/**
 * QuickActionsBottomSheet - Unified Quick Actions
 * Redesigned with unified design inspired by QuickActionsBottomSheet
 */

import React, { forwardRef, useCallback, useMemo } from 'react';
import { View, StyleSheet, ScrollView, Keyboard } from 'react-native';
import {
  Text,
  Surface,
  useTheme,
  TouchableRipple,
  IconButton,
  Portal
} from 'react-native-paper';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY, getBorderColor } from '../theme/designTokens';

const QuickActionsBottomSheet = forwardRef(({ onActionPress }, ref) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const bottomSheetRef = React.useRef(null);

  const actions = [
    {
      id: 'add-product',
      title: 'Add Product',
      icon: 'plus-circle',
      color: theme.colors.primary,
      backgroundColor: theme.colors.primaryContainer,
    },
    {
      id: 'add-order',
      title: 'New Order',
      icon: 'clipboard-plus',
      color: theme.colors.secondary,
      backgroundColor: theme.colors.secondaryContainer,
    },
    {
      id: 'add-customer',
      title: 'Add Customer',
      icon: 'account-plus',
      color: theme.colors.tertiary,
      backgroundColor: theme.colors.tertiaryContainer,
    },
  ];

  const snapPoints = useMemo(() => {
    // Base height for header + padding
    const baseHeight = 120;
    // Height per action item (including padding and margins)
    const itemHeight = 80;
    // Total content height
    const contentHeight = baseHeight + (actions.length * itemHeight);
    // Convert to percentage of screen height (max 80%)
    const percentage = Math.min((contentHeight / 800) * 100, 80);
    return [`${Math.max(percentage, 30)}%`];
  }, []);

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      Keyboard.dismiss();
    }
  }, []);

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  React.useImperativeHandle(ref, () => ({
    expand: () => {
      console.log('⚡ Opening Quick Actions bottomsheet');
      bottomSheetRef.current?.expand();
    },
    close: () => {
      console.log('⚡ Closing Quick Actions bottomsheet');
      bottomSheetRef.current?.close();
    }
  }));

  const handleActionPress = (actionId) => {
    console.log(`⚡ Quick Action pressed: ${actionId}`);
    bottomSheetRef.current?.close();
    onActionPress?.(actionId);
  };

  const renderActionItem = (action) => (
    <Surface
      key={action.id}
      style={[styles.actionItem, { backgroundColor: theme.colors.surface }]}
      elevation={1}
    >
      <TouchableRipple
        onPress={() => handleActionPress(action.id)}
        style={styles.actionContent}
        borderless
      >
        <View style={styles.actionInner}>
          <View style={[styles.actionIconContainer, { backgroundColor: action.backgroundColor }]}>
            <Icon name={action.icon} size={24} color={action.color} />
          </View>

          <View style={styles.actionText}>
            <Text style={[styles.actionTitle, {
              color: theme.colors.onSurface,
              fontSize: TYPOGRAPHY.cardTitle.fontSize,
              fontWeight: TYPOGRAPHY.cardTitle.fontWeight,
              lineHeight: TYPOGRAPHY.cardTitle.lineHeight * TYPOGRAPHY.cardTitle.fontSize
            }]}>
              {action.title}
            </Text>
          </View>

          <Icon
            name="chevron-right"
            size={20}
            color={theme.colors.onSurfaceVariant}
          />
        </View>
      </TouchableRipple>
    </Surface>
  );

  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
        style={styles.bottomSheet}
      >
        <BottomSheetView style={styles.container}>
          {/* Header - Compact Design */}
          <View style={[styles.header, { borderBottomColor: theme.colors.outline + '20' }]}>
            <View style={styles.headerLeft}>
              <View style={[styles.iconContainer, { backgroundColor: '#F59E0B15' }]}>
                <Icon name="lightning-bolt" size={24} color="#F59E0B" />
              </View>
              <View style={styles.titleContainer}>
                <Text style={[styles.title, {
                  color: theme.colors.onSurface,
                  fontSize: TYPOGRAPHY.sectionTitle.fontSize,
                  fontWeight: TYPOGRAPHY.sectionTitle.fontWeight,
                  lineHeight: TYPOGRAPHY.sectionTitle.lineHeight * TYPOGRAPHY.sectionTitle.fontSize
                }]}>
                  Quick Actions
                </Text>
              </View>
            </View>
            <IconButton
              icon="close"
              size={20}
              onPress={() => bottomSheetRef.current?.close()}
              iconColor={theme.colors.onSurfaceVariant}
            />
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            <View style={styles.actionsGrid}>
              {actions.map(renderActionItem)}
            </View>

            {/* Bottom padding for safe area */}
            <View style={{ height: Math.max(insets.bottom + 20, 40) }} />
          </ScrollView>
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
});

const styles = StyleSheet.create({
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.md,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '700',
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.md,
  },
  actionsGrid: {
    gap: SPACING.sm,
    marginBottom: SPACING.lg,
  },
  actionItem: {
    borderRadius: BORDER_RADIUS.xl,
    overflow: 'hidden',
  },
  actionContent: {
    borderRadius: BORDER_RADIUS.xl,
  },
  actionInner: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
  },
  actionIconContainer: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  actionText: {
    flex: 1,
  },
  actionTitle: {
    fontWeight: '600',
  },
});

QuickActionsBottomSheet.displayName = 'QuickActionsBottomSheet';

export default QuickActionsBottomSheet;
