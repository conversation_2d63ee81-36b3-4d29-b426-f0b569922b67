/**
 * MD3ComponentUtils - Material Design 3 component utilities
 * Provides reusable MD3-compliant component patterns and helpers
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Surface, IconButton } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { SPACING, SHAPE, ELEVATION, TYPOGRAPHY } from '../theme/designTokens';

/**
 * MD3 Card Component with proper elevation and shape
 */
export const MD3Card = ({ 
  children, 
  elevation = 1, 
  shape = 'medium', 
  style, 
  onPress,
  ...props 
}) => {
  const cardStyle = [
    styles.card,
    {
      borderRadius: SHAPE.corner[shape] || SHAPE.corner.medium,
      ...ELEVATION.shadow[`level${elevation}`],
    },
    style,
  ];

  return (
    <Surface style={cardStyle} elevation={elevation} {...props}>
      {children}
    </Surface>
  );
};

/**
 * MD3 Section Header with proper typography
 */
export const MD3SectionHeader = ({ 
  title, 
  subtitle, 
  action, 
  style,
  titleStyle,
  subtitleStyle 
}) => {
  return (
    <View style={[styles.sectionHeader, style]}>
      <View style={styles.sectionHeaderText}>
        <Text 
          variant="titleLarge" 
          style={[{ ...TYPOGRAPHY.title.large }, titleStyle]}
        >
          {title}
        </Text>
        {subtitle && (
          <Text 
            variant="bodyMedium" 
            style={[{ ...TYPOGRAPHY.body.medium }, subtitleStyle]}
          >
            {subtitle}
          </Text>
        )}
      </View>
      {action && (
        <View style={styles.sectionHeaderAction}>
          {action}
        </View>
      )}
    </View>
  );
};

/**
 * MD3 Icon Container with proper shape and elevation
 */
export const MD3IconContainer = ({ 
  icon, 
  size = 24, 
  containerSize = 40,
  shape = 'medium',
  elevation = 0,
  backgroundColor,
  iconColor,
  style,
  onPress 
}) => {
  const containerStyle = [
    styles.iconContainer,
    {
      width: containerSize,
      height: containerSize,
      borderRadius: SHAPE.corner[shape] || SHAPE.corner.medium,
      backgroundColor: backgroundColor || 'transparent',
      ...ELEVATION.shadow[`level${elevation}`],
    },
    style,
  ];

  const IconComponent = onPress ? IconButton : Icon;
  const iconProps = onPress 
    ? { icon, size, iconColor, onPress }
    : { name: icon, size, color: iconColor };

  return (
    <View style={containerStyle}>
      <IconComponent {...iconProps} />
    </View>
  );
};

/**
 * MD3 List Item with proper spacing and typography
 */
export const MD3ListItem = ({ 
  title, 
  subtitle, 
  leading, 
  trailing, 
  onPress,
  style,
  titleStyle,
  subtitleStyle 
}) => {
  return (
    <View style={[styles.listItem, style]}>
      {leading && (
        <View style={styles.listItemLeading}>
          {leading}
        </View>
      )}
      <View style={styles.listItemContent}>
        <Text 
          variant="bodyLarge" 
          style={[{ ...TYPOGRAPHY.body.large }, titleStyle]}
          numberOfLines={1}
        >
          {title}
        </Text>
        {subtitle && (
          <Text 
            variant="bodyMedium" 
            style={[{ ...TYPOGRAPHY.body.medium }, subtitleStyle]}
            numberOfLines={1}
          >
            {subtitle}
          </Text>
        )}
      </View>
      {trailing && (
        <View style={styles.listItemTrailing}>
          {trailing}
        </View>
      )}
    </View>
  );
};

/**
 * MD3 Divider with proper spacing
 */
export const MD3Divider = ({ 
  style, 
  thickness = 1, 
  color = 'rgba(0,0,0,0.12)',
  spacing = SPACING.md 
}) => {
  return (
    <View 
      style={[
        styles.divider,
        {
          height: thickness,
          backgroundColor: color,
          marginVertical: spacing,
        },
        style,
      ]} 
    />
  );
};

/**
 * MD3 Chip Component
 */
export const MD3Chip = ({ 
  label, 
  selected = false, 
  onPress, 
  icon, 
  style,
  labelStyle 
}) => {
  return (
    <View style={[styles.chip, selected && styles.chipSelected, style]}>
      {icon && (
        <Icon 
          name={icon} 
          size={16} 
          style={styles.chipIcon}
        />
      )}
      <Text 
        variant="labelLarge" 
        style={[{ ...TYPOGRAPHY.label.large }, labelStyle]}
      >
        {label}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    padding: SPACING.md,
    marginBottom: SPACING.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: SPACING.md,
  },
  sectionHeaderText: {
    flex: 1,
  },
  sectionHeaderAction: {
    marginLeft: SPACING.md,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
  },
  listItemLeading: {
    marginRight: SPACING.md,
  },
  listItemContent: {
    flex: 1,
  },
  listItemTrailing: {
    marginLeft: SPACING.md,
  },
  divider: {
    width: '100%',
  },
  chip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.xs,
    borderRadius: SHAPE.corner.sm,
    backgroundColor: 'rgba(0,0,0,0.05)',
    marginRight: SPACING.xs,
    marginBottom: SPACING.xs,
  },
  chipSelected: {
    backgroundColor: 'rgba(103, 80, 164, 0.12)',
  },
  chipIcon: {
    marginRight: SPACING.xs,
  },
});

export default {
  MD3Card,
  MD3SectionHeader,
  MD3IconContainer,
  MD3ListItem,
  MD3Divider,
  MD3Chip,
};
