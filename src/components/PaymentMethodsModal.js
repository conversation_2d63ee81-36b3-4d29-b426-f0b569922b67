import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Modal,
  Portal,
  Text,
  Button,
  useTheme,
  Card,
  Switch,
  List,
  Divider,
  TextInput,
  HelperText,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const PaymentMethodsModal = ({ visible, onDismiss, onSave, paymentMethods }) => {
  const theme = useTheme();
  const [methods, setMethods] = useState({
    cash: { enabled: true, processingFee: 0 },
    card: { enabled: true, processingFee: 2.9 },
    digitalWallet: { enabled: false, processingFee: 2.5 },
    bankTransfer: { enabled: false, processingFee: 1.0 },
    giftCard: { enabled: true, processingFee: 0 },
  });

  const [errors, setErrors] = useState({});

  const paymentOptions = [
    {
      key: 'cash',
      label: 'Cash',
      icon: 'cash',
      description: 'Physical cash payments',
    },
    {
      key: 'card',
      label: 'Credit/Debit Card',
      icon: 'credit-card',
      description: 'Visa, Mastercard, American Express',
    },
    {
      key: 'digitalWallet',
      label: 'Digital Wallet',
      icon: 'wallet',
      description: 'Apple Pay, Google Pay, Samsung Pay',
    },
    {
      key: 'bankTransfer',
      label: 'Bank Transfer',
      icon: 'bank-transfer',
      description: 'Direct bank transfers',
    },
    {
      key: 'giftCard',
      label: 'Gift Cards',
      icon: 'gift',
      description: 'Store gift cards and vouchers',
    },
  ];

  useEffect(() => {
    if (paymentMethods) {
      setMethods(paymentMethods);
    }
    setErrors({});
  }, [paymentMethods, visible]);

  const validateForm = () => {
    const newErrors = {};
    
    Object.keys(methods).forEach(key => {
      const fee = parseFloat(methods[key].processingFee);
      if (methods[key].enabled && (isNaN(fee) || fee < 0 || fee > 10)) {
        newErrors[key] = 'Processing fee must be between 0 and 10%';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const toggleMethod = (key) => {
    setMethods(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        enabled: !prev[key].enabled
      }
    }));
  };

  const updateProcessingFee = (key, fee) => {
    setMethods(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        processingFee: fee
      }
    }));
  };

  const handleSave = () => {
    if (validateForm()) {
      onSave(methods);
      onDismiss();
    }
  };

  const PaymentMethodCard = ({ option }) => (
    <Card style={styles.methodCard} mode="outlined">
      <Card.Content>
        <View style={styles.methodHeader}>
          <View style={styles.methodInfo}>
            <Icon name={option.icon} size={24} color={theme.colors.primary} />
            <View style={styles.methodText}>
              <Text variant="titleMedium">{option.label}</Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                {option.description}
              </Text>
            </View>
          </View>
          <Switch
            value={methods[option.key].enabled}
            onValueChange={() => toggleMethod(option.key)}
            color={theme.colors.primary}
          />
        </View>

        {methods[option.key].enabled && (
          <>
            <Divider style={{ marginVertical: 12 }} />
            <View style={styles.feeSection}>
              <TextInput
                label="Processing Fee (%)"
                value={methods[option.key].processingFee.toString()}
                onChangeText={(text) => updateProcessingFee(option.key, parseFloat(text) || 0)}
                mode="outlined"
                style={styles.feeInput}
                keyboardType="decimal-pad"
                error={!!errors[option.key]}
                dense
              />
              <HelperText type="error" visible={!!errors[option.key]}>
                {errors[option.key]}
              </HelperText>
            </View>
          </>
        )}
      </Card.Content>
    </Card>
  );

  const enabledMethods = Object.keys(methods).filter(key => methods[key].enabled);

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.modal,
          { backgroundColor: theme.colors.surface }
        ]}
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
            Payment Methods
          </Text>

          <Card style={styles.summaryCard} mode="contained">
            <Card.Content>
              <Text variant="titleMedium" style={{ marginBottom: 8 }}>
                Summary
              </Text>
              <Text variant="bodyMedium">
                {enabledMethods.length} payment method{enabledMethods.length !== 1 ? 's' : ''} enabled
              </Text>
              {enabledMethods.length > 0 && (
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 4 }}>
                  {paymentOptions
                    .filter(option => methods[option.key].enabled)
                    .map(option => option.label)
                    .join(', ')}
                </Text>
              )}
            </Card.Content>
          </Card>

          {paymentOptions.map((option) => (
            <PaymentMethodCard key={option.key} option={option} />
          ))}

          <Card style={styles.infoCard} mode="outlined">
            <Card.Content>
              <View style={styles.infoHeader}>
                <Icon name="information" size={20} color={theme.colors.primary} />
                <Text variant="titleSmall" style={{ marginLeft: 8 }}>
                  Processing Fees
                </Text>
              </View>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 8 }}>
                Processing fees are automatically calculated and added to transactions. 
                These fees help cover payment processing costs and gateway charges.
              </Text>
            </Card.Content>
          </Card>

          <View style={styles.buttonRow}>
            <Button
              mode="outlined"
              onPress={onDismiss}
              style={styles.button}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleSave}
              style={styles.button}
            >
              Save Methods
            </Button>
          </View>
        </ScrollView>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  modal: {
    margin: 20,
    padding: 20,
    borderRadius: 16,
    maxHeight: '90%',
  },
  title: {
    textAlign: 'center',
    marginBottom: 24,
  },
  summaryCard: {
    marginBottom: 16,
  },
  methodCard: {
    marginBottom: 12,
  },
  methodHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  methodInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  methodText: {
    marginLeft: 12,
    flex: 1,
  },
  feeSection: {
    marginTop: 8,
  },
  feeInput: {
    marginBottom: 4,
  },
  infoCard: {
    marginTop: 16,
    marginBottom: 16,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  button: {
    flex: 1,
    marginHorizontal: 8,
  },
});

export default PaymentMethodsModal;
