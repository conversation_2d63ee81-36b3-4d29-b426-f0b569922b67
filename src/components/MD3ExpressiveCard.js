/**
 * MD3 Expressive Card Component
 * Enhanced Material Design 3 card with expressive styling and adaptive layouts
 */

import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Text, useTheme, Surface } from 'react-native-paper';
import { 
  SPACING, 
  SHAPE, 
  TYPOGRAPHY, 
  ELEVATION,
  getElevationStyle,
  getShapeStyle,
  getTypographyStyle 
} from '../theme/designTokens';

const MD3ExpressiveCard = ({
  // Content
  children,
  title,
  subtitle,
  description,
  
  // Variants
  variant = 'elevated', // 'elevated', 'filled', 'outlined'
  size = 'standard', // 'compact', 'standard', 'expanded'
  
  // Styling
  style,
  contentStyle,
  headerStyle,
  
  // Interaction
  onPress,
  onLongPress,
  disabled = false,
  
  // Layout
  expressive = false, // Use expressive shape family
  fullWidth = false,
  
  // Accessibility
  accessibilityLabel,
  accessibilityHint,
}) => {
  const theme = useTheme();

  // Get size configuration
  const sizeConfig = {
    compact: { padding: SPACING.md, minHeight: 72 },
    standard: { padding: SPACING.lg, minHeight: 96 },
    expanded: { padding: SPACING.xl, minHeight: 120 },
  };
  
  const currentSizeConfig = sizeConfig[size] || sizeConfig.standard;
  
  // Get shape style
  const shapeFamily = expressive ? 'expressive' : 'rounded';
  const shapeSize = size === 'compact' ? 'small' : size === 'expanded' ? 'large' : 'medium';
  const shapeStyle = getShapeStyle(shapeFamily, shapeSize);

  // Variant styles
  const getVariantStyles = () => {
    const variants = {
      elevated: {
        backgroundColor: theme.colors.surfaceContainerLow || theme.colors.surface,
        borderWidth: 0,
        elevation: 1,
      },
      filled: {
        backgroundColor: theme.colors.surfaceContainerHighest || theme.colors.surfaceVariant,
        borderWidth: 0,
        elevation: 0,
      },
      outlined: {
        backgroundColor: theme.colors.surface,
        borderWidth: 1,
        borderColor: theme.colors.outlineVariant,
        elevation: 0,
      },
    };

    return variants[variant] || variants.elevated;
  };

  const variantStyles = getVariantStyles();

  // Typography styles
  const titleStyle = getTypographyStyle('title', 'medium', expressive);
  const subtitleStyle = getTypographyStyle('body', 'medium');
  const descriptionStyle = getTypographyStyle('body', 'small');

  // Render header
  const renderHeader = () => {
    if (!title && !subtitle) return null;

    return (
      <View style={[styles.header, headerStyle]}>
        {title && (
          <Text 
            style={[
              titleStyle,
              { color: theme.colors.onSurface },
              styles.title
            ]}
            numberOfLines={2}
          >
            {title}
          </Text>
        )}
        {subtitle && (
          <Text 
            style={[
              subtitleStyle,
              { color: theme.colors.onSurfaceVariant },
              styles.subtitle
            ]}
            numberOfLines={1}
          >
            {subtitle}
          </Text>
        )}
      </View>
    );
  };

  // Render description
  const renderDescription = () => {
    if (!description) return null;

    return (
      <Text 
        style={[
          descriptionStyle,
          { color: theme.colors.onSurfaceVariant },
          styles.description
        ]}
        numberOfLines={3}
      >
        {description}
      </Text>
    );
  };

  // Render content
  const renderContent = () => {
    return (
      <View style={[styles.content, contentStyle, { padding: currentSizeConfig.padding }]}>
        {renderHeader()}
        {renderDescription()}
        {children}
      </View>
    );
  };

  // Container styles
  const containerStyles = [
    styles.container,
    shapeStyle,
    {
      backgroundColor: variantStyles.backgroundColor,
      borderColor: variantStyles.borderColor,
      borderWidth: variantStyles.borderWidth,
      minHeight: currentSizeConfig.minHeight,
      width: fullWidth ? '100%' : undefined,
      opacity: disabled ? 0.6 : 1,
    },
    variantStyles.elevation > 0 && getElevationStyle(variantStyles.elevation, theme),
    style,
  ];

  // If onPress is provided, wrap in TouchableOpacity
  if (onPress) {
    return (
      <TouchableOpacity
        style={containerStyles}
        onPress={onPress}
        onLongPress={onLongPress}
        disabled={disabled}
        activeOpacity={0.8}
        accessibilityLabel={accessibilityLabel}
        accessibilityHint={accessibilityHint}
        accessibilityRole="button"
        accessibilityState={{ disabled }}
      >
        {renderContent()}
      </TouchableOpacity>
    );
  }

  // Otherwise, use Surface for elevation
  return (
    <Surface 
      style={containerStyles}
      elevation={variantStyles.elevation}
    >
      {renderContent()}
    </Surface>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  content: {
    flex: 1,
  },
  header: {
    marginBottom: SPACING.sm,
  },
  title: {
    marginBottom: SPACING.xs,
  },
  subtitle: {
    // No additional margin needed
  },
  description: {
    marginBottom: SPACING.md,
  },
});

export default MD3ExpressiveCard;
