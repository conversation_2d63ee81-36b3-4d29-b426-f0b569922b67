/**
 * UnifiedFilterChips - Consistent filter chips component for all screens
 * Provides horizontal scrollable filter chips with MD3 Expressive styling
 */

import React from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { useTheme } from 'react-native-paper';
import MD3ExpressiveChip from './MD3ExpressiveChip';
import { SPACING } from '../theme/designTokens';

const UnifiedFilterChips = ({
  filters = [],
  selectedFilter,
  onFilterChange,
  style,
  chipStyle,
  showCounts = false,
  data = [],
  countField = 'status', // Field to count for each filter
  horizontal = true,
  contentContainerStyle,
}) => {
  const theme = useTheme();

  // Calculate counts for each filter if showCounts is enabled
  const getFilterCount = (filter) => {
    if (!showCounts || !data.length) return null;

    if (filter === 'All') {
      return data.length;
    }

    return data.filter(item => {
      const fieldValue = item[countField];
      return fieldValue === filter;
    }).length;
  };

  // Get chip label with optional count
  const getChipLabel = (filter) => {
    const count = getFilterCount(filter);
    return count !== null ? `${filter} (${count})` : filter;
  };

  // Get chip styling based on selection state
  const getChipStyles = (filter) => {
    const isSelected = selectedFilter === filter;

    return {
      backgroundColor: isSelected
        ? theme.colors.primary
        : theme.colors.surface,
      borderColor: isSelected
        ? theme.colors.primary
        : theme.colors.outline,
      borderWidth: 1,
    };
  };

  // Get text color based on selection state
  const getTextColor = (filter) => {
    const isSelected = selectedFilter === filter;
    return isSelected ? theme.colors.onPrimary : theme.colors.onSurface;
  };

  const renderChips = () => (
    <>
      {filters.map((filter) => (
        <MD3ExpressiveChip
          key={filter}
          variant="filter"
          selected={selectedFilter === filter}
          onPress={() => onFilterChange?.(filter)}
          style={[styles.chip, chipStyle]}
          expressive={true}
        >
          {getChipLabel(filter)}
        </MD3ExpressiveChip>
      ))}
    </>
  );

  if (!horizontal) {
    return (
      <View style={[styles.verticalContainer, style]}>
        {renderChips()}
      </View>
    );
  }

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={[styles.container, style]}
      contentContainerStyle={[styles.contentContainer, contentContainerStyle]}
    >
      {renderChips()}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 0,
  },
  contentContainer: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    gap: SPACING.sm,
  },
  verticalContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    gap: SPACING.sm,
  },
  chip: {
    marginRight: SPACING.sm,
  },
});

export default UnifiedFilterChips;
