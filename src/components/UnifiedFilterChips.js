/**
 * UnifiedFilterChips - Consistent filter chips component for all screens
 * Provides horizontal scrollable filter chips with unified styling
 */

import React from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { Chip, useTheme } from 'react-native-paper';

const UnifiedFilterChips = ({
  filters = [],
  selectedFilter,
  onFilterChange,
  style,
  chipStyle,
  showCounts = false,
  data = [],
  countField = 'status', // Field to count for each filter
  horizontal = true,
  contentContainerStyle,
}) => {
  const theme = useTheme();

  // Calculate counts for each filter if showCounts is enabled
  const getFilterCount = (filter) => {
    if (!showCounts || !data.length) return null;

    if (filter === 'All') {
      return data.length;
    }

    return data.filter(item => {
      const fieldValue = item[countField];
      return fieldValue === filter;
    }).length;
  };

  // Get chip label with optional count
  const getChipLabel = (filter) => {
    const count = getFilterCount(filter);
    return count !== null ? `${filter} (${count})` : filter;
  };

  // Get chip styling based on selection state
  const getChipStyles = (filter) => {
    const isSelected = selectedFilter === filter;

    return {
      backgroundColor: isSelected
        ? theme.colors.primary
        : theme.colors.surface,
      borderColor: isSelected
        ? theme.colors.primary
        : theme.colors.outline,
      borderWidth: 1,
    };
  };

  // Get text color based on selection state
  const getTextColor = (filter) => {
    const isSelected = selectedFilter === filter;
    return isSelected ? theme.colors.onPrimary : theme.colors.onSurface;
  };

  const renderChips = () => (
    <>
      {filters.map((filter) => (
        <Chip
          key={filter}
          selected={selectedFilter === filter}
          onPress={() => onFilterChange?.(filter)}
          style={[
            styles.chip,
            getChipStyles(filter),
            chipStyle,
          ]}
          textStyle={{
            color: getTextColor(filter),
            fontWeight: selectedFilter === filter ? '600' : '400',
          }}
          mode={selectedFilter === filter ? 'flat' : 'outlined'}
          compact
        >
          {getChipLabel(filter)}
        </Chip>
      ))}
    </>
  );

  if (!horizontal) {
    return (
      <View style={[styles.verticalContainer, style]}>
        {renderChips()}
      </View>
    );
  }

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={[styles.container, style]}
      contentContainerStyle={[styles.contentContainer, contentContainerStyle]}
    >
      {renderChips()}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 0,
  },
  contentContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
  },
  verticalContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
  },
  chip: {
    marginRight: 8,
    height: 36,
    borderRadius: 20,
  },
});

export default UnifiedFilterChips;
