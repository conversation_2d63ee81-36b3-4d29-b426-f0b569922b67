import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import UnifiedInfoCard from './UnifiedInfoCard';
import { SPACING } from '../theme/designTokens';

const StatCardGroup = ({
  title,
  cards = [],
  columns = 2,
  showTitle = true,
  titleStyle = {},
  containerStyle = {},
  onCardPress
}) => {
  const handleCardPress = (card, index) => {
    if (onCardPress) {
      onCardPress(card, index);
    } else if (card.onPress) {
      card.onPress();
    }
  };

  const isOddCount = cards.length % columns !== 0;
  const lastCardIndex = cards.length - 1;

  return (
    <View style={[styles.container, containerStyle]}>
      {showTitle && title && (
        <Text variant="titleLarge" style={[styles.title, titleStyle]}>
          {title}
        </Text>
      )}

      <View style={[styles.grid, { marginHorizontal: -2, marginVertical: 0 }]}>
        {cards.map((card, index) => {
          const isLastCard = index === lastCardIndex;
          const shouldSpanFullWidth = isOddCount && isLastCard;

          return (
            <View
              key={card.key || index}
              style={[
                styles.cardWrapper,
                {
                  width: shouldSpanFullWidth ? '100%' : `${100 / columns}%`,
                  paddingHorizontal: 2,
                  paddingVertical: 0
                }
              ]}
            >
              <UnifiedInfoCard
                type="stat"
                title={card.value} // Show data/numbers as title
                subtitle={card.title} // Show card name as subtitle
                icon={card.icon}
                iconColor={card.iconColor || card.color}
                elevation={card.elevation || 1}
                onPress={() => handleCardPress(card, index)}
                style={{ marginHorizontal: 2, marginVertical: -3 }}
                {...card.props}
              />
            </View>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.md,
  },
  title: {
    fontWeight: '700',
    marginBottom: SPACING.md,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  cardWrapper: {
    marginBottom: SPACING.xs,
  },
});

export default StatCardGroup;
