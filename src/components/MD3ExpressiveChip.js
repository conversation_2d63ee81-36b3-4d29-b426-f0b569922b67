/**
 * MD3ExpressiveChip - Material Design 3 Expressive Chip Component
 * Provides enhanced chip variants with expressive styling
 */

import React from 'react';
import { TouchableOpacity, StyleSheet, View } from 'react-native';
import { Text, useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { 
  SPACING, 
  SHAPE, 
  TYPOGRAPHY, 
  COMPONENT_SIZES,
  getElevationStyle,
  getTypographyStyle,
  getShapeStyle 
} from '../theme/designTokens';

const MD3ExpressiveChip = ({
  // Content
  children,
  icon,
  avatar,
  
  // Variants
  variant = 'assist', // 'assist', 'filter', 'input', 'suggestion'
  size = 'medium', // 'small', 'medium', 'large'
  
  // States
  selected = false,
  disabled = false,
  
  // Actions
  onPress,
  onClose,
  
  // Styling
  style,
  textStyle,
  
  // Advanced
  expressive = false,
  showCheckmark = true,
}) => {
  const theme = useTheme();

  // Get size configuration
  const sizeConfig = COMPONENT_SIZES.chip?.[size] || {
    height: 32,
    paddingHorizontal: SPACING.md,
    fontSize: TYPOGRAPHY.label.medium.fontSize,
    iconSize: 18,
  };

  // Get variant styles
  const getVariantStyles = () => {
    const baseStyles = {
      backgroundColor: theme.colors.surface,
      borderColor: theme.colors.outline,
      borderWidth: 1,
    };

    if (selected) {
      switch (variant) {
        case 'filter':
          return {
            backgroundColor: theme.colors.secondaryContainer,
            borderColor: theme.colors.secondary,
            textColor: theme.colors.onSecondaryContainer,
          };
        case 'input':
          return {
            backgroundColor: theme.colors.primaryContainer,
            borderColor: theme.colors.primary,
            textColor: theme.colors.onPrimaryContainer,
          };
        default:
          return {
            backgroundColor: theme.colors.primaryContainer,
            borderColor: theme.colors.primary,
            textColor: theme.colors.onPrimaryContainer,
          };
      }
    }

    return {
      ...baseStyles,
      textColor: theme.colors.onSurface,
    };
  };

  const variantStyles = getVariantStyles();
  const shapeStyle = expressive 
    ? getShapeStyle('expressive', 'small')
    : { borderRadius: sizeConfig.height / 2 };

  const containerStyles = [
    styles.container,
    {
      height: sizeConfig.height,
      paddingHorizontal: sizeConfig.paddingHorizontal,
      backgroundColor: variantStyles.backgroundColor,
      borderColor: variantStyles.borderColor,
      borderWidth: variantStyles.borderWidth,
    },
    shapeStyle,
    disabled && styles.disabled,
    style,
  ];

  const textStyles = [
    styles.text,
    {
      fontSize: sizeConfig.fontSize,
      color: variantStyles.textColor,
    },
    textStyle,
  ];

  const renderIcon = () => {
    if (selected && showCheckmark && variant === 'filter') {
      return (
        <Icon
          name="check"
          size={sizeConfig.iconSize}
          color={variantStyles.textColor}
          style={styles.icon}
        />
      );
    }

    if (icon) {
      return (
        <Icon
          name={icon}
          size={sizeConfig.iconSize}
          color={variantStyles.textColor}
          style={styles.icon}
        />
      );
    }

    if (avatar) {
      return (
        <View style={[styles.avatar, { width: sizeConfig.iconSize, height: sizeConfig.iconSize }]}>
          {avatar}
        </View>
      );
    }

    return null;
  };

  const renderCloseButton = () => {
    if (!onClose) return null;

    return (
      <TouchableOpacity
        onPress={onClose}
        style={styles.closeButton}
        hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
      >
        <Icon
          name="close"
          size={sizeConfig.iconSize}
          color={variantStyles.textColor}
        />
      </TouchableOpacity>
    );
  };

  return (
    <TouchableOpacity
      style={containerStyles}
      onPress={onPress}
      disabled={disabled || !onPress}
      activeOpacity={0.7}
      accessibilityRole="button"
      accessibilityState={{ selected, disabled }}
    >
      {renderIcon()}
      <Text style={textStyles}>{children}</Text>
      {renderCloseButton()}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.xs,
    marginBottom: SPACING.xs,
  },
  text: {
    fontWeight: '500',
  },
  icon: {
    marginRight: SPACING.xs,
  },
  avatar: {
    marginRight: SPACING.xs,
    borderRadius: 50,
    overflow: 'hidden',
  },
  closeButton: {
    marginLeft: SPACING.xs,
    padding: 2,
  },
  disabled: {
    opacity: 0.38,
  },
});

export default MD3ExpressiveChip;
