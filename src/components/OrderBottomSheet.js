/**
 * OrderBottomSheet - Unified Order Form
 * Redesigned with unified design inspired by QuickActionsBottomSheet
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { View, StyleSheet, Alert, Keyboard, ScrollView } from 'react-native';
import {
  Text,
  TextInput,
  Button,
  useTheme,
  Surface,
  Card,
  IconButton,
  Divider,
  HelperText,
  Chip,
  Portal,
} from 'react-native-paper';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import UnifiedSearch from './UnifiedSearch';

const OrderBottomSheet = ({ bottomSheetRef, onSave, order = null, mode = 'add' }) => {
  const theme = useTheme();
  const { products, customers } = useData();
  const insets = useSafeAreaInsets();
  const [formData, setFormData] = useState({
    customer: '',
    phone: '',
    email: '',
    items: [],
    status: 'Pending',
  });
  const [errors, setErrors] = useState({});
  const [searchQuery, setSearchQuery] = useState('');
  const [showProductSearch, setShowProductSearch] = useState(false);

  const snapPoints = useMemo(() => ['25%', '95%'], []);

  useEffect(() => {
    if (order && mode === 'edit') {
      setFormData({
        customer: order.customer,
        phone: order.phone,
        email: order.email || '',
        items: [...order.items],
        status: order.status,
      });
    } else {
      setFormData({
        customer: '',
        phone: '',
        email: '',
        items: [],
        status: 'Pending',
      });
    }
    setErrors({});
    setSearchQuery('');
    setShowProductSearch(false);
  }, [order, mode]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.customer.trim()) {
      newErrors.customer = 'Customer name is required';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    if (formData.items.length === 0) {
      newErrors.items = 'At least one item is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const calculateTotal = () => {
    return formData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  };

  const addProductToOrder = (product) => {
    const existingItem = formData.items.find(item => item.productId === product.id);

    if (existingItem) {
      if (existingItem.quantity < product.stock) {
        setFormData({
          ...formData,
          items: formData.items.map(item =>
            item.productId === product.id
              ? { ...item, quantity: item.quantity + 1 }
              : item
          ),
        });
      } else {
        Alert.alert('Stock Limit', `Only ${product.stock} items available in stock`);
      }
    } else {
      if (product.stock > 0) {
        setFormData({
          ...formData,
          items: [...formData.items, {
            productId: product.id,
            name: product.name,
            price: product.price,
            quantity: 1,
          }],
        });
      } else {
        Alert.alert('Out of Stock', 'This product is currently out of stock');
      }
    }
    setSearchQuery('');
  };

  const updateItemQuantity = (productId, newQuantity) => {
    const product = products.find(p => p.id === productId);

    if (newQuantity <= 0) {
      setFormData({
        ...formData,
        items: formData.items.filter(item => item.productId !== productId),
      });
    } else if (newQuantity <= product.stock) {
      setFormData({
        ...formData,
        items: formData.items.map(item =>
          item.productId === productId
            ? { ...item, quantity: newQuantity }
            : item
        ),
      });
    } else {
      Alert.alert('Stock Limit', `Only ${product.stock} items available in stock`);
    }
  };

  const removeItem = (productId) => {
    setFormData({
      ...formData,
      items: formData.items.filter(item => item.productId !== productId),
    });
  };

  const handleSave = () => {
    if (validateForm()) {
      const orderData = {
        ...formData,
        total: calculateTotal(),
        date: new Date().toISOString().split('T')[0],
        time: new Date().toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        }),
        createdAt: order?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      if (mode === 'edit') {
        orderData.id = order.id;
      }

      onSave(orderData);
      bottomSheetRef.current?.close();
    }
  };

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      Keyboard.dismiss();
      setFormData({
        customer: '',
        phone: '',
        email: '',
        items: [],
        status: 'Pending',
      });
      setErrors({});
      setShowProductSearch(false);
      setSearchQuery('');
    }
  }, []);

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  const filteredProducts = useMemo(() =>
    products.filter(product =>
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
      product.stock > 0
    ), [products, searchQuery]);

  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
        style={styles.bottomSheet}
      >
        <BottomSheetView style={styles.container}>
          {/* Header - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.header, { borderBottomColor: theme.colors.outline + '20' }]}>
            <View style={styles.headerLeft}>
              <View style={[styles.iconContainer, { backgroundColor: theme.colors.secondary + '15' }]}>
                <Icon name="clipboard-list" size={24} color={theme.colors.secondary} />
              </View>
              <View style={styles.titleContainer}>
                <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
                  {mode === 'edit' ? 'Edit Order' : 'Create New Order'}
                </Text>
                <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
                  {formData.items.length > 0
                    ? `${formData.items.length} items • Total: ৳${calculateTotal().toFixed(2)}`
                    : 'Add customer details and products'
                  }
                </Text>
              </View>
            </View>
            <IconButton
              icon="close"
              size={20}
              onPress={() => bottomSheetRef.current?.close()}
              iconColor={theme.colors.onSurfaceVariant}
            />
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Customer Information
          </Text>

          <TextInput
            label="Customer Name"
            value={formData.customer}
            onChangeText={(text) => setFormData({ ...formData, customer: text })}
            mode="outlined"
            style={styles.input}
            error={!!errors.customer}
          />
          <HelperText type="error" visible={!!errors.customer}>
            {errors.customer}
          </HelperText>

          <TextInput
            label="Phone Number"
            value={formData.phone}
            onChangeText={(text) => setFormData({ ...formData, phone: text })}
            mode="outlined"
            style={styles.input}
            keyboardType="phone-pad"
            error={!!errors.phone}
          />
          <HelperText type="error" visible={!!errors.phone}>
            {errors.phone}
          </HelperText>

          <TextInput
            label="Email (Optional)"
            value={formData.email}
            onChangeText={(text) => setFormData({ ...formData, email: text })}
            mode="outlined"
            style={styles.input}
            keyboardType="email-address"
          />

          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Order Items
          </Text>

          <Button
            mode="outlined"
            icon="plus"
            onPress={() => setShowProductSearch(!showProductSearch)}
            style={styles.addButton}
          >
            Add Products
          </Button>

          {showProductSearch && (
            <View style={styles.productSearch}>
              <UnifiedSearch
                type="products"
                mode="bar"
                data={products.filter(p => p.stock > 0)}
                searchFields={["name", "description", "category"]}
                placeholder="Search products to add..."
                onSearch={setSearchQuery}
                onResultSelect={(product) => {
                  addProductToOrder(product);
                  setSearchQuery('');
                }}
                showSuggestions={true}
                showRecentSearches={false}
                style={styles.searchbar}
              />
              {searchQuery.length > 0 && (
                <View style={styles.productList}>
                  {filteredProducts.slice(0, 5).map((product) => (
                    <Surface
                      key={product.id}
                      style={[styles.productCard, { backgroundColor: theme.colors.surfaceVariant }]}
                      elevation={0}
                    >
                      <View style={styles.productContent}>
                        <Icon name={product.icon} size={20} color={theme.colors.primary} />
                        <View style={styles.productInfo}>
                          <Text variant="bodyMedium">{product.name}</Text>
                          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                            ৳{product.price.toFixed(2)} • Stock: {product.stock}
                          </Text>
                        </View>
                        <IconButton
                          icon="plus"
                          size={20}
                          onPress={() => addProductToOrder(product)}
                        />
                      </View>
                    </Surface>
                  ))}
                </View>
              )}
            </View>
          )}

          {formData.items.length > 0 && (
            <View style={styles.itemsList}>
              {formData.items.map((item, index) => (
                <Surface key={item.productId} style={[styles.itemCard, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
                  <View style={styles.itemContent}>
                    <View style={styles.itemInfo}>
                      <Text variant="bodyMedium" style={{ fontWeight: '600' }}>{item.name}</Text>
                      <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                        ৳{item.price.toFixed(2)} each
                      </Text>
                    </View>
                    <View style={styles.quantityControls}>
                      <IconButton
                        icon="minus"
                        size={20}
                        onPress={() => updateItemQuantity(item.productId, item.quantity - 1)}
                      />
                      <Text variant="bodyLarge" style={styles.quantity}>
                        {item.quantity}
                      </Text>
                      <IconButton
                        icon="plus"
                        size={20}
                        onPress={() => updateItemQuantity(item.productId, item.quantity + 1)}
                      />
                      <IconButton
                        icon="delete"
                        size={20}
                        iconColor={theme.colors.error}
                        onPress={() => removeItem(item.productId)}
                      />
                    </View>
                  </View>
                  <Text variant="bodyMedium" style={[styles.itemTotal, { color: theme.colors.primary }]}>
                    Subtotal: ৳{(item.price * item.quantity).toFixed(2)}
                  </Text>
                </Surface>
              ))}
            </View>
          )}

          <HelperText type="error" visible={!!errors.items}>
            {errors.items}
          </HelperText>

          {formData.items.length > 0 && (
            <Surface style={[styles.totalCard, { backgroundColor: theme.colors.primaryContainer }]} elevation={2}>
              <Text variant="headlineSmall" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '700' }}>
                Total: ৳{calculateTotal().toFixed(2)}
              </Text>
            </Surface>
          )}

            {/* Bottom padding for safe area */}
            <View style={{ height: Math.max(insets.bottom + 80, 100) }} />
          </ScrollView>

          {/* Footer - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.footer, { paddingBottom: insets.bottom, borderTopColor: theme.colors.outline + '20' }]}>
            <View style={styles.buttonRow}>
              <Button
                mode="outlined"
                onPress={() => bottomSheetRef.current?.close()}
                style={styles.button}
              >
                Cancel
              </Button>
              <Button
                mode="contained"
                onPress={handleSave}
                style={styles.button}
              >
                {mode === 'edit' ? 'Update' : 'Create'} Order
              </Button>
            </View>
          </View>
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
};

const styles = StyleSheet.create({
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '700',
    lineHeight: 28,
  },
  subtitle: {
    marginTop: 2,
    lineHeight: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  sectionTitle: {
    marginTop: 12,
    marginBottom: 8,
    fontWeight: '600',
  },
  input: {
    marginBottom: 6,
  },
  addButton: {
    marginBottom: 12,
  },
  productSearch: {
    marginBottom: 12,
  },
  searchbar: {
    marginBottom: 8,
    borderRadius: 8,
  },
  productList: {
    maxHeight: 180,
  },
  productCard: {
    marginBottom: 6,
    borderRadius: 8,
    padding: 10,
  },
  productContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  productInfo: {
    flex: 1,
    marginLeft: 10,
  },
  itemsList: {
    marginTop: 12,
  },
  itemCard: {
    marginBottom: 8,
    borderRadius: 8,
    padding: 12,
  },
  itemContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemInfo: {
    flex: 1,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantity: {
    marginHorizontal: 8,
    minWidth: 30,
    textAlign: 'center',
    fontWeight: '600',
  },
  itemTotal: {
    textAlign: 'right',
    marginTop: 8,
    fontWeight: '600',
  },
  totalCard: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginVertical: 16,
  },
  footer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    borderTopWidth: 1,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    marginHorizontal: 4,
  },
});

export default OrderBottomSheet;
