/**
 * OrderDetailsBottomSheet - Unified Order Details View
 * Redesigned with unified design inspired by QuickActionsBottomSheet
 */

import React, { useMemo, useCallback, useState, useEffect } from 'react';
import { View, StyleSheet, Alert, ScrollView, Keyboard, Image, TouchableOpacity } from 'react-native';
import {
  Text,
  Button,
  useTheme,
  Surface,
  Divider,
  Chip,
  IconButton,
  Portal,
} from 'react-native-paper';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import UnifiedStatusPicker from './UnifiedStatusPicker';

const OrderDetailsBottomSheet = ({
  bottomSheetRef,
  order,
  onUpdateStatus,
  onEdit,
  onDelete
}) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const snapPoints = useMemo(() => ['25%', '85%'], []);
  const [selectedStatus, setSelectedStatus] = useState(order?.status || 'Pending');

  const statusOptions = [
    { value: 'Pending', label: 'Pending', color: '#FF9800', icon: 'clock-outline' },
    { value: 'Processing', label: 'Processing', color: '#2196F3', icon: 'cog-outline' },
    { value: 'Ready', label: 'Ready', color: '#4CAF50', icon: 'check-circle-outline' },
    { value: 'Completed', label: 'Completed', color: '#8BC34A', icon: 'check-all' },
    { value: 'Cancelled', label: 'Cancelled', color: '#F44336', icon: 'close-circle-outline' },
  ];

  useEffect(() => {
    if (order?.status) {
      setSelectedStatus(order.status);
    }
  }, [order?.status]);

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      Keyboard.dismiss();
    }
  }, []);

  const handleStatusChange = (newStatus) => {
    setSelectedStatus(newStatus);
    if (onUpdateStatus && newStatus !== order?.status) {
      onUpdateStatus(order.id, newStatus);
    }
  };

  const getCurrentStatusOption = () => {
    return statusOptions.find(option => option.value === selectedStatus) || statusOptions[0];
  };

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  const getStatusColor = (status) => {
    switch (status) {
      case 'Pending': return theme.colors.tertiary;
      case 'In Progress': return theme.colors.primary;
      case 'Completed': return theme.colors.secondary;
      case 'Cancelled': return theme.colors.error;
      default: return theme.colors.onSurfaceVariant;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Pending': return 'clock-outline';
      case 'In Progress': return 'progress-clock';
      case 'Completed': return 'check-circle';
      case 'Cancelled': return 'cancel';
      default: return 'help-circle';
    }
  };

  const handleStatusUpdate = (newStatus) => {
    Alert.alert(
      'Update Order Status',
      `Change order status to ${newStatus}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          onPress: () => {
            onUpdateStatus(order.id, newStatus);
            bottomSheetRef.current?.close();
          }
        },
      ]
    );
  };

  const handleEdit = () => {
    onEdit(order);
    bottomSheetRef.current?.close();
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Order',
      'Are you sure you want to delete this order?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            onDelete(order);
            bottomSheetRef.current?.close();
          }
        },
      ]
    );
  };

  const getActionButtons = () => {
    const buttons = [];

    // Status-specific actions
    if (order.status === 'Pending') {
      buttons.push(
        <Button
          key="cancel"
          mode="outlined"
          onPress={() => handleStatusUpdate('Cancelled')}
          icon="cancel"
          style={styles.actionButton}
        >
          Cancel
        </Button>
      );
      buttons.push(
        <Button
          key="start"
          mode="contained"
          onPress={() => handleStatusUpdate('In Progress')}
          icon="play"
          style={styles.actionButton}
        >
          Start
        </Button>
      );
    } else if (order.status === 'In Progress') {
      buttons.push(
        <Button
          key="complete"
          mode="contained"
          onPress={() => handleStatusUpdate('Completed')}
          icon="check"
          style={styles.actionButton}
        >
          Complete
        </Button>
      );
    } else if (order.status === 'Cancelled') {
      buttons.push(
        <Button
          key="reactivate"
          mode="contained"
          onPress={() => handleStatusUpdate('Pending')}
          icon="restore"
          style={styles.actionButton}
        >
          Reactivate
        </Button>
      );
    }

    // Always show edit button
    buttons.push(
      <Button
        key="edit"
        mode="outlined"
        onPress={handleEdit}
        icon="pencil"
        style={styles.actionButton}
      >
        Edit
      </Button>
    );

    return buttons;
  };

  if (!order) return null;

  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
        style={styles.bottomSheet}
      >
        <BottomSheetView style={styles.container}>
          {/* Header - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.header, { borderBottomColor: theme.colors.outline + '20' }]}>
            <View style={styles.headerLeft}>
              <View style={[styles.iconContainer, { backgroundColor: getStatusColor(order.status) + '15' }]}>
                <Icon name={getStatusIcon(order.status)} size={24} color={getStatusColor(order.status)} />
              </View>
              <View style={styles.titleContainer}>
                <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
                  Order #{order.id}
                </Text>
                <View style={styles.subtitleRow}>
                  <Chip
                    mode="flat"
                    style={[styles.statusChip, { backgroundColor: getStatusColor(order.status) + '20' }]}
                    textStyle={{ color: getStatusColor(order.status), fontWeight: '600' }}
                  >
                    {order.status}
                  </Chip>
                  <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
                    {order.date} • {order.time}
                  </Text>
                </View>
              </View>
            </View>
            <IconButton
              icon="close"
              size={20}
              onPress={() => bottomSheetRef.current?.close()}
              iconColor={theme.colors.onSurfaceVariant}
            />
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
          {/* Customer Information */}
          <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
            <View style={styles.sectionHeader}>
              <Icon name="account" size={20} color={theme.colors.primary} />
              <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Customer Information
              </Text>
            </View>
            <View style={styles.customerInfo}>
              <Text variant="bodyLarge" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                {order.customer}
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                📞 {order.phone}
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                ✉️ {order.email}
              </Text>
            </View>
          </Surface>

          {/* Status Management */}
          <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
            <View style={styles.sectionHeader}>
              <Icon name="clipboard-check" size={20} color={theme.colors.primary} />
              <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Order Status
              </Text>
            </View>

            <UnifiedStatusPicker
              selectedStatus={selectedStatus}
              onStatusChange={handleStatusChange}
              statusOptions={statusOptions}
              title=""
            />
          </Surface>

          {/* Order Details */}
          <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
            <View style={styles.sectionHeader}>
              <Icon name="calendar-clock" size={20} color={theme.colors.primary} />
              <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Order Details
              </Text>
            </View>

            {/* Order Image */}
            {order.image && (
              <View style={styles.imageContainer}>
                <Image source={{ uri: order.image }} style={styles.orderImage} />
              </View>
            )}

            <View style={styles.orderMeta}>
              <View style={styles.metaRow}>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Date:</Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                  {order.date}
                </Text>
              </View>
              <View style={styles.metaRow}>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Time:</Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                  {order.time}
                </Text>
              </View>
              <View style={styles.metaRow}>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Total:</Text>
                <Text variant="titleMedium" style={{ color: theme.colors.primary, fontWeight: '700' }}>
                  ৳{order.total.toFixed(2)}
                </Text>
              </View>
            </View>
          </Surface>

          {/* Order Items */}
          <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
            <View style={styles.sectionHeader}>
              <Icon name="food" size={20} color={theme.colors.primary} />
              <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Items ({order.items?.length || 0})
              </Text>
            </View>
            {order.items?.map((item, index) => (
              <View key={index} style={styles.orderItem}>
                <View style={styles.itemInfo}>
                  <Text variant="bodyLarge" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                    {item.name}
                  </Text>
                  <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                    ${item.price.toFixed(2)} × {item.quantity}
                  </Text>
                </View>
                <Text variant="bodyLarge" style={{ color: theme.colors.primary, fontWeight: '600' }}>
                  ${(item.price * item.quantity).toFixed(2)}
                </Text>
              </View>
            ))}
          </Surface>

            {/* Bottom padding for safe area */}
            <View style={{ height: Math.max(insets.bottom + 80, 100) }} />
          </ScrollView>

          {/* Footer - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.footer, { paddingBottom: insets.bottom, borderTopColor: theme.colors.outline + '20' }]}>
            <View style={styles.actionButtons}>
              {getActionButtons()}
            </View>
          </View>
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
};

const styles = StyleSheet.create({
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '700',
    lineHeight: 28,
  },
  subtitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    gap: 8,
  },
  statusChip: {
    height: 24,
  },
  subtitle: {
    lineHeight: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  section: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  sectionTitle: {
    fontWeight: '600',
  },
  customerInfo: {
    gap: 4,
  },
  imageContainer: {
    alignItems: 'center',
    marginBottom: 12,
  },
  orderImage: {
    width: 120,
    height: 120,
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  orderMeta: {
    gap: 8,
  },
  metaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  orderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  itemInfo: {
    flex: 1,
  },
  footer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    borderTopWidth: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});

export default OrderDetailsBottomSheet;
