/**
 * ImagePicker Component - Handles image selection from camera or gallery
 *
 * @component
 * @description Provides a unified interface for selecting images from camera or gallery
 * with proper permissions handling and image optimization.
 *
 * @param {Object} props - Component props
 * @param {Function} props.onImageSelected - Callback when image is selected
 * @param {string} [props.currentImage] - Current image URI to display
 * @param {Object} [props.style] - Additional styles
 * @param {string} [props.placeholder] - Placeholder text when no image
 *
 * @example
 * ```jsx
 * <ImagePicker
 *   onImageSelected={(imageUri) => setFormData({...formData, image: imageUri})}
 *   currentImage={formData.image}
 *   placeholder="Add Product Image"
 * />
 * ```
 */

import React, { useState } from 'react';
import { View, StyleSheet, Alert, Image, Platform, TouchableOpacity } from 'react-native';
import { Button, Text, useTheme, Surface, IconButton } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as ImagePickerExpo from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';
import { SPACING, SHAPE, TYPOGRAPHY, getBorderColor } from '../theme/designTokens';

const ImagePicker = ({
  onImageSelected,
  currentImage,
  style,
  placeholder = "Add Image"
}) => {
  const theme = useTheme();
  const [isLoading, setIsLoading] = useState(false);

  /**
   * Request camera permissions
   */
  const requestCameraPermissions = async () => {
    try {
      const { status } = await ImagePickerExpo.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Camera Permission Required',
          'Please grant camera permission to take photos.',
          [{ text: 'OK' }]
        );
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error requesting camera permissions:', error);
      return false;
    }
  };

  /**
   * Request media library permissions
   */
  const requestMediaLibraryPermissions = async () => {
    try {
      const { status } = await ImagePickerExpo.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Gallery Permission Required',
          'Please grant gallery permission to select photos.',
          [{ text: 'OK' }]
        );
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error requesting media library permissions:', error);
      return false;
    }
  };

  /**
   * Optimize image for storage and performance
   */
  const optimizeImage = async (imageUri) => {
    try {
      console.log('Starting image optimization for:', imageUri);

      // Get image info first
      const imageInfo = await ImageManipulator.manipulateAsync(
        imageUri,
        [],
        { format: ImageManipulator.SaveFormat.JPEG }
      );

      console.log('Image info obtained, proceeding with optimization');

      const manipulatedImage = await ImageManipulator.manipulateAsync(
        imageUri,
        [
          // Resize to max 800px width while maintaining aspect ratio
          { resize: { width: 800 } }
        ],
        {
          // Compress to reduce file size
          compress: 0.7,
          format: ImageManipulator.SaveFormat.JPEG,
          base64: false,
        }
      );

      console.log('Image optimization completed:', manipulatedImage.uri);
      return manipulatedImage.uri;
    } catch (error) {
      console.error('Error optimizing image:', error);
      console.log('Returning original image URI due to optimization failure');
      return imageUri; // Return original if optimization fails
    }
  };

  /**
   * Take photo with camera
   */
  const takePhoto = async () => {
    try {
      setIsLoading(true);

      // Check if camera is available
      const cameraAvailable = await ImagePickerExpo.getCameraPermissionsAsync();
      if (!cameraAvailable.canAskAgain && cameraAvailable.status !== 'granted') {
        Alert.alert(
          'Camera Not Available',
          'Camera permissions are required. Please enable them in your device settings.',
          [{ text: 'OK' }]
        );
        setIsLoading(false);
        return;
      }

      const hasPermission = await requestCameraPermissions();
      if (!hasPermission) {
        setIsLoading(false);
        return;
      }

      const result = await ImagePickerExpo.launchCameraAsync({
        mediaTypes: ImagePickerExpo.MediaType.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        exif: false,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const imageUri = result.assets[0].uri;
        console.log('Camera image selected:', imageUri);
        const optimizedUri = await optimizeImage(imageUri);
        console.log('Camera image optimized:', optimizedUri);
        onImageSelected(optimizedUri);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Select photo from gallery
   */
  const selectFromGallery = async () => {
    try {
      setIsLoading(true);

      // Check media library permissions
      const mediaLibraryAvailable = await ImagePickerExpo.getMediaLibraryPermissionsAsync();
      if (!mediaLibraryAvailable.canAskAgain && mediaLibraryAvailable.status !== 'granted') {
        Alert.alert(
          'Gallery Access Required',
          'Gallery permissions are required. Please enable them in your device settings.',
          [{ text: 'OK' }]
        );
        setIsLoading(false);
        return;
      }

      const hasPermission = await requestMediaLibraryPermissions();
      if (!hasPermission) {
        setIsLoading(false);
        return;
      }

      const result = await ImagePickerExpo.launchImageLibraryAsync({
        mediaTypes: ImagePickerExpo.MediaType.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        exif: false,
        selectionLimit: 1,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const imageUri = result.assets[0].uri;
        console.log('Gallery image selected:', imageUri);
        const optimizedUri = await optimizeImage(imageUri);
        console.log('Gallery image optimized:', optimizedUri);
        onImageSelected(optimizedUri);
      }
    } catch (error) {
      console.error('Error selecting from gallery:', error);
      Alert.alert('Error', 'Failed to select photo. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Show image source selection options
   */
  const showImageSourceOptions = () => {
    Alert.alert(
      'Select Image Source',
      'Choose how you want to add an image',
      [
        {
          text: 'Camera',
          onPress: takePhoto,
          style: 'default',
        },
        {
          text: 'Gallery',
          onPress: selectFromGallery,
          style: 'default',
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ],
      { cancelable: true }
    );
  };

  /**
   * Remove current image
   */
  const removeImage = () => {
    Alert.alert(
      'Remove Image',
      'Are you sure you want to remove this image?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          onPress: () => onImageSelected(null),
          style: 'destructive',
        },
      ]
    );
  };

  return (
    <View style={[styles.container, style]}>
      {currentImage ? (
        <TouchableOpacity
          style={[styles.imageContainer, { backgroundColor: theme.colors.surfaceVariant }]}
          onPress={showImageSourceOptions}
          disabled={isLoading}
        >
          <Image
            source={{ uri: currentImage }}
            style={styles.image}
            onError={(error) => {
              console.warn('Image loading error:', error.nativeEvent?.error || error);
              // Could implement fallback image here
            }}
            onLoad={() => {
              console.log('Image loaded successfully:', currentImage);
            }}
            resizeMode="cover"
          />
          <View style={styles.imageOverlay}>
            <View style={[styles.overlayButton, { backgroundColor: theme.colors.surface + 'E6' }]}>
              <Icon name="pencil" size={16} color={theme.colors.onSurface} />
            </View>
            <TouchableOpacity
              style={[styles.overlayButton, { backgroundColor: theme.colors.error + 'E6' }]}
              onPress={(e) => {
                e.stopPropagation();
                removeImage();
              }}
              disabled={isLoading}
            >
              <Icon name="close" size={16} color={theme.colors.onError} />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      ) : (
        <TouchableOpacity
          style={[
            styles.placeholderContainer,
            {
              backgroundColor: theme.colors.surfaceVariant,
              borderColor: theme.colors.outline,
            }
          ]}
          onPress={showImageSourceOptions}
          disabled={isLoading}
        >
          <Icon
            name="camera-plus"
            size={24}
            color={theme.colors.onSurfaceVariant}
          />
          <Text
            style={[styles.placeholderText, {
              color: theme.colors.onSurfaceVariant,
              fontSize: TYPOGRAPHY.body.medium.fontSize,
              fontWeight: TYPOGRAPHY.body.medium.fontWeight
            }]}
          >
            {isLoading ? 'Processing...' : placeholder}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.md,
  },
  imageContainer: {
    borderRadius: SHAPE.corner.lg,
    overflow: 'hidden',
    position: 'relative',
    height: 80,
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  imageOverlay: {
    position: 'absolute',
    top: SPACING.xs,
    right: SPACING.xs,
    flexDirection: 'row',
    gap: SPACING.xs,
  },
  overlayButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderContainer: {
    height: 80,
    borderRadius: SHAPE.corner.lg,
    borderWidth: 1,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.md,
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  placeholderText: {
    textAlign: 'center',
  },
});

export default ImagePicker;
