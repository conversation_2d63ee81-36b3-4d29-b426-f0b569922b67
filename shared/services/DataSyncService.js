/**
 * DataSyncService - Shared service for syncing data between Shop Manager and Employee apps
 * Uses SQLite + Real-time sync for seamless data sharing
 */

import * as SQLite from 'expo-sqlite';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';

class DataSyncService {
  constructor() {
    this.db = null;
    this.syncQueue = [];
    this.isOnline = false;
    this.syncInterval = null;
    this.listeners = new Map();
  }

  // Initialize the service
  async initialize(appType = 'manager') {
    try {
      // Open shared SQLite database
      this.db = SQLite.openDatabase('tailor_shop_shared.db');
      this.appType = appType;

      // Create tables if they don't exist
      await this.createTables();

      // Set up network monitoring
      this.setupNetworkMonitoring();

      // Start sync service
      this.startSyncService();

      console.log(`DataSyncService initialized for ${appType} app`);
      return true;
    } catch (error) {
      console.error('Failed to initialize DataSyncService:', error);
      return false;
    }
  }

  // Create shared database tables
  async createTables() {
    return new Promise((resolve, reject) => {
      this.db.transaction(tx => {
        // Orders table
        tx.executeSql(`
          CREATE TABLE IF NOT EXISTS orders (
            id TEXT PRIMARY KEY,
            customer_id TEXT,
            customer_name TEXT,
            garment_type TEXT,
            status TEXT,
            assigned_to TEXT,
            priority TEXT,
            delivery_date TEXT,
            created_at TEXT,
            updated_at TEXT,
            sync_status TEXT DEFAULT 'synced'
          )
        `);

        // Employees table
        tx.executeSql(`
          CREATE TABLE IF NOT EXISTS employees (
            id TEXT PRIMARY KEY,
            name TEXT,
            role TEXT,
            skills TEXT,
            is_active INTEGER,
            phone TEXT,
            created_at TEXT,
            updated_at TEXT,
            sync_status TEXT DEFAULT 'synced'
          )
        `);

        // Order status updates table
        tx.executeSql(`
          CREATE TABLE IF NOT EXISTS order_status_updates (
            id TEXT PRIMARY KEY,
            order_id TEXT,
            employee_id TEXT,
            old_status TEXT,
            new_status TEXT,
            notes TEXT,
            timestamp TEXT,
            sync_status TEXT DEFAULT 'pending'
          )
        `);

        // Sync log table
        tx.executeSql(`
          CREATE TABLE IF NOT EXISTS sync_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            action TEXT,
            table_name TEXT,
            record_id TEXT,
            timestamp TEXT,
            app_source TEXT,
            sync_status TEXT
          )
        `);

        resolve();
      }, reject);
    });
  }

  // Network monitoring
  setupNetworkMonitoring() {
    NetInfo.addEventListener(state => {
      this.isOnline = state.isConnected;
      if (this.isOnline && this.syncQueue.length > 0) {
        this.processSyncQueue();
      }
    });
  }

  // Start background sync service
  startSyncService() {
    // Sync every 30 seconds when online
    this.syncInterval = setInterval(() => {
      if (this.isOnline) {
        this.syncWithOtherApp();
      }
    }, 30000);
  }

  // Stop sync service
  stopSyncService() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  // Add data change listener
  addListener(table, callback) {
    if (!this.listeners.has(table)) {
      this.listeners.set(table, []);
    }
    this.listeners.get(table).push(callback);
  }

  // Remove data change listener
  removeListener(table, callback) {
    if (this.listeners.has(table)) {
      const callbacks = this.listeners.get(table);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // Notify listeners of data changes
  notifyListeners(table, action, data) {
    if (this.listeners.has(table)) {
      this.listeners.get(table).forEach(callback => {
        callback({ action, data, table });
      });
    }
  }

  // CRUD Operations with auto-sync

  // Get orders for employee
  async getMyOrders(employeeId) {
    return new Promise((resolve, reject) => {
      this.db.transaction(tx => {
        tx.executeSql(
          'SELECT * FROM orders WHERE assigned_to = ? ORDER BY created_at DESC',
          [employeeId],
          (_, { rows }) => resolve(rows._array),
          (_, error) => reject(error)
        );
      });
    });
  }

  // Get all orders (for manager app)
  async getAllOrders() {
    return new Promise((resolve, reject) => {
      this.db.transaction(tx => {
        tx.executeSql(
          'SELECT * FROM orders ORDER BY created_at DESC',
          [],
          (_, { rows }) => resolve(rows._array),
          (_, error) => reject(error)
        );
      });
    });
  }

  // Update order status (from employee app)
  async updateOrderStatus(orderId, newStatus, employeeId, notes = '') {
    const timestamp = new Date().toISOString();
    const updateId = `${orderId}_${timestamp}`;

    return new Promise((resolve, reject) => {
      this.db.transaction(tx => {
        // Get current status
        tx.executeSql(
          'SELECT status FROM orders WHERE id = ?',
          [orderId],
          (_, { rows }) => {
            const oldStatus = rows._array[0]?.status || '';

            // Update order status
            tx.executeSql(
              'UPDATE orders SET status = ?, updated_at = ?, sync_status = ? WHERE id = ?',
              [newStatus, timestamp, 'pending', orderId]
            );

            // Log the status update
            tx.executeSql(
              'INSERT INTO order_status_updates (id, order_id, employee_id, old_status, new_status, notes, timestamp) VALUES (?, ?, ?, ?, ?, ?, ?)',
              [updateId, orderId, employeeId, oldStatus, newStatus, notes, timestamp]
            );

            // Add to sync queue
            this.addToSyncQueue('orders', 'update', orderId);
            this.addToSyncQueue('order_status_updates', 'create', updateId);

            // Notify listeners
            this.notifyListeners('orders', 'update', { id: orderId, status: newStatus });

            resolve({ success: true, updateId });
          },
          (_, error) => reject(error)
        );
      });
    });
  }

  // Add new order (from manager app)
  async addOrder(orderData) {
    const timestamp = new Date().toISOString();
    const order = {
      ...orderData,
      created_at: timestamp,
      updated_at: timestamp,
      sync_status: 'pending'
    };

    return new Promise((resolve, reject) => {
      this.db.transaction(tx => {
        tx.executeSql(
          `INSERT INTO orders (
            id, customer_id, customer_name, garment_type, status, 
            assigned_to, priority, delivery_date, created_at, updated_at, sync_status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            order.id, order.customer_id, order.customer_name, order.garment_type,
            order.status, order.assigned_to, order.priority, order.delivery_date,
            order.created_at, order.updated_at, order.sync_status
          ],
          () => {
            this.addToSyncQueue('orders', 'create', order.id);
            this.notifyListeners('orders', 'create', order);
            resolve(order);
          },
          (_, error) => reject(error)
        );
      });
    });
  }

  // Get employee info
  async getEmployee(employeeId) {
    return new Promise((resolve, reject) => {
      this.db.transaction(tx => {
        tx.executeSql(
          'SELECT * FROM employees WHERE id = ?',
          [employeeId],
          (_, { rows }) => resolve(rows._array[0] || null),
          (_, error) => reject(error)
        );
      });
    });
  }

  // Sync queue management
  addToSyncQueue(table, action, recordId) {
    this.syncQueue.push({
      table,
      action,
      recordId,
      timestamp: new Date().toISOString(),
      appSource: this.appType
    });

    // Process immediately if online
    if (this.isOnline) {
      this.processSyncQueue();
    }
  }

  // Process sync queue
  async processSyncQueue() {
    if (this.syncQueue.length === 0) return;

    try {
      // In a real implementation, this would sync with a central server
      // For now, we'll simulate local sync between apps
      await this.simulateSync();
      
      // Clear processed items
      this.syncQueue = [];
    } catch (error) {
      console.error('Sync failed:', error);
    }
  }

  // Simulate sync between apps (in real app, this would be server sync)
  async simulateSync() {
    // Store sync data in AsyncStorage for other app to pick up
    const syncData = {
      queue: this.syncQueue,
      timestamp: new Date().toISOString(),
      source: this.appType
    };

    await AsyncStorage.setItem('sync_data', JSON.stringify(syncData));
    console.log(`Synced ${this.syncQueue.length} items from ${this.appType} app`);
  }

  // Check for updates from other app
  async syncWithOtherApp() {
    try {
      const syncDataStr = await AsyncStorage.getItem('sync_data');
      if (!syncDataStr) return;

      const syncData = JSON.parse(syncDataStr);
      
      // Don't process our own sync data
      if (syncData.source === this.appType) return;

      // Process updates from other app
      for (const item of syncData.queue) {
        await this.applySyncUpdate(item);
      }

      // Clear processed sync data
      await AsyncStorage.removeItem('sync_data');
    } catch (error) {
      console.error('Failed to sync with other app:', error);
    }
  }

  // Apply sync update from other app
  async applySyncUpdate(syncItem) {
    // Notify listeners about the update
    this.notifyListeners(syncItem.table, syncItem.action, { id: syncItem.recordId });
  }

  // Get sync statistics
  getSyncStats() {
    return {
      isOnline: this.isOnline,
      queueLength: this.syncQueue.length,
      appType: this.appType,
      lastSync: new Date().toISOString()
    };
  }

  // Clean up
  destroy() {
    this.stopSyncService();
    this.listeners.clear();
    if (this.db) {
      this.db.close();
    }
  }
}

// Export singleton instance
export default new DataSyncService();
